"""
Performance Benchmark for Enhanced Simulation Systems

Tests the performance of the new simulation systems to ensure they can handle
the requirements for RL training (target: 1000+ games/hour).
"""

import time
import statistics
from typing import List, Dict, Any
import random

from simulator.enhanced_combat import EnhancedCombatSystem
from simulator.enhanced_shop import EnhancedShop
from simulator.status_effects import StatusEffectManager
from simulator.resources import ResourceManager
from simulator.damage_system import DamageCalculator, AttackInfo, DefenseInfo
from simulator.core import Player, Backpack, Item


def create_test_item(name: str, damage_min: int = 5, damage_max: int = 10, 
                    cooldown: float = 2.0, stamina_cost: float = 1.0) -> Item:
    """Create a test item for benchmarking"""
    data = {
        'id': hash(name) % 1000,
        'name': name,
        'rarity': 'Common',
        'cost': 5,
        'item_class': 'Neutral',
        'item_type': 'weapon',
        'shape': [[1]],
        'description': f"Test {name}",
        'raw_stats': {
            'damage_min': damage_min,
            'damage_max': damage_max,
            'cooldown': cooldown,
            'stamina_cost': stamina_cost
        },
        'synergy_triggers': [],
        'synergy_effects': []
    }
    return Item(data)


def create_test_player(name: str, health: int = 100) -> Player:
    """Create a test player with some items"""
    player = Player(name=name, player_class="Ranger")

    # Set health and gold manually
    player.health = health
    player.gold = 50

    # Add some test items to backpack
    backpack = Backpack()

    # Create test items
    sword = create_test_item("Sword", 8, 12, 2.0, 1.5)
    shield = create_test_item("Shield", 0, 0, 3.0, 1.0)
    potion = create_test_item("Potion", 0, 0, 5.0, 0.5)

    # Add items to backpack (simplified placement)
    backpack.items["sword"] = sword
    backpack.items["shield"] = shield
    backpack.items["potion"] = potion

    player.backpack = backpack
    return player


def benchmark_status_effects(iterations: int = 10000) -> Dict[str, float]:
    """Benchmark status effects system"""
    print(f"Benchmarking Status Effects ({iterations} iterations)...")
    
    times = []
    for _ in range(iterations):
        start = time.perf_counter()
        
        manager = StatusEffectManager()
        manager.add_effect("Empower", stacks=5)
        manager.add_effect("Heat", stacks=3)
        manager.add_effect("Luck", stacks=2)
        manager.add_effect("Poison", stacks=1)
        
        # Simulate some operations
        manager.get_stat_modifier("damage")
        manager.get_stat_modifier("cooldown_multiplier")
        manager.get_stat_modifier("accuracy_multiplier")
        manager.update(2.0, 0.1)
        
        end = time.perf_counter()
        times.append(end - start)
    
    return {
        "mean_time": statistics.mean(times),
        "median_time": statistics.median(times),
        "max_time": max(times),
        "operations_per_second": 1.0 / statistics.mean(times)
    }


def benchmark_damage_calculation(iterations: int = 10000) -> Dict[str, float]:
    """Benchmark damage calculation system"""
    print(f"Benchmarking Damage Calculation ({iterations} iterations)...")
    
    calculator = DamageCalculator()
    times = []
    
    for _ in range(iterations):
        start = time.perf_counter()
        
        attack = AttackInfo(
            min_damage=random.randint(5, 8),
            max_damage=random.randint(10, 15),
            accuracy=random.uniform(0.7, 0.95),
            crit_chance=random.uniform(0.05, 0.25)
        )
        
        attacker_effects = {
            "Empower": random.randint(0, 5),
            "Luck": random.randint(0, 3)
        }
        
        defender = DefenseInfo(
            current_health=random.randint(50, 100),
            max_health=100,
            block=random.randint(0, 10),
            status_effects={"Blind": random.randint(0, 2)}
        )
        
        result = calculator.calculate_damage(attack, attacker_effects, defender)
        
        end = time.perf_counter()
        times.append(end - start)
    
    calculator.clear_log()  # Clean up
    
    return {
        "mean_time": statistics.mean(times),
        "median_time": statistics.median(times),
        "max_time": max(times),
        "calculations_per_second": 1.0 / statistics.mean(times)
    }


def benchmark_resource_management(iterations: int = 10000) -> Dict[str, float]:
    """Benchmark resource management system"""
    print(f"Benchmarking Resource Management ({iterations} iterations)...")
    
    times = []
    
    for _ in range(iterations):
        start = time.perf_counter()
        
        manager = ResourceManager(max_stamina=10.0, max_mana=5)
        
        # Simulate resource operations
        manager.use_stamina(random.uniform(1.0, 3.0))
        manager.use_mana(random.randint(1, 2))
        manager.update(random.uniform(0.1, 2.0))
        manager.add_stamina(random.uniform(0.5, 1.5))
        
        end = time.perf_counter()
        times.append(end - start)
    
    return {
        "mean_time": statistics.mean(times),
        "median_time": statistics.median(times),
        "max_time": max(times),
        "operations_per_second": 1.0 / statistics.mean(times)
    }


def benchmark_shop_generation(iterations: int = 1000) -> Dict[str, float]:
    """Benchmark shop generation system"""
    print(f"Benchmarking Shop Generation ({iterations} iterations)...")
    
    # Create mock items for shop
    mock_items = {}
    for i in range(100):  # 100 different items
        name = f"Item_{i}"
        rarity = random.choice(["Common", "Rare", "Epic", "Legendary", "Godly"])
        mock_items[name] = create_test_item(name)
        mock_items[name].rarity = rarity
    
    shop = EnhancedShop(mock_items, use_shop_blacklist=False)
    times = []
    
    for _ in range(iterations):
        start = time.perf_counter()
        
        # Generate shop offerings for random round
        round_num = random.randint(1, 18)
        player_class = random.choice(["Ranger", "Berserker", "Pyromancer", "Reaper"])
        
        shop.generate_shop_offerings(round_num, player_class, None, True)
        
        end = time.perf_counter()
        times.append(end - start)
    
    return {
        "mean_time": statistics.mean(times),
        "median_time": statistics.median(times),
        "max_time": max(times),
        "generations_per_second": 1.0 / statistics.mean(times)
    }


def benchmark_combat_components(iterations: int = 1000) -> Dict[str, float]:
    """Benchmark combat system components"""
    print(f"Benchmarking Combat Components ({iterations} iterations)...")

    times = []

    for _ in range(iterations):
        start = time.perf_counter()

        # Simulate a simplified combat round
        # Status effects
        status_mgr = StatusEffectManager()
        status_mgr.add_effect("Empower", stacks=3)
        status_mgr.add_effect("Heat", stacks=2)

        # Resource management
        resources = ResourceManager()
        resources.use_stamina(2.0)
        resources.update(1.0)

        # Damage calculation
        calculator = DamageCalculator()
        attack = AttackInfo(min_damage=8, max_damage=12, accuracy=0.85)
        attacker_effects = {"Empower": 3, "Luck": 1}
        defender = DefenseInfo(100, 100, 5, {"Blind": 1})

        result = calculator.calculate_damage(attack, attacker_effects, defender)

        # Status updates
        status_mgr.update(2.0, 0.1)

        end = time.perf_counter()
        times.append(end - start)

    # Estimate full battles per hour based on component performance
    # Assume a battle has ~50 combat rounds on average
    rounds_per_second = 1.0 / statistics.mean(times)
    battles_per_second = rounds_per_second / 50
    battles_per_hour = battles_per_second * 3600

    return {
        "mean_time": statistics.mean(times),
        "median_time": statistics.median(times),
        "max_time": max(times),
        "rounds_per_second": rounds_per_second,
        "estimated_battles_per_second": battles_per_second,
        "estimated_battles_per_hour": battles_per_hour
    }


def run_full_benchmark():
    """Run complete performance benchmark"""
    print("🚀 ENHANCED SIMULATION PERFORMANCE BENCHMARK")
    print("=" * 60)
    print()
    
    # Individual system benchmarks
    status_results = benchmark_status_effects()
    damage_results = benchmark_damage_calculation()
    resource_results = benchmark_resource_management()
    shop_results = benchmark_shop_generation()
    combat_results = benchmark_combat_components()
    
    print("\n" + "=" * 60)
    print("📊 BENCHMARK RESULTS")
    print("=" * 60)
    
    print(f"\n🔮 Status Effects System:")
    print(f"  Operations/sec: {status_results['operations_per_second']:,.0f}")
    print(f"  Mean time: {status_results['mean_time']*1000:.3f}ms")
    
    print(f"\n⚔️  Damage Calculation System:")
    print(f"  Calculations/sec: {damage_results['calculations_per_second']:,.0f}")
    print(f"  Mean time: {damage_results['mean_time']*1000:.3f}ms")
    
    print(f"\n⚡ Resource Management System:")
    print(f"  Operations/sec: {resource_results['operations_per_second']:,.0f}")
    print(f"  Mean time: {resource_results['mean_time']*1000:.3f}ms")
    
    print(f"\n🛒 Shop Generation System:")
    print(f"  Generations/sec: {shop_results['generations_per_second']:,.0f}")
    print(f"  Mean time: {shop_results['mean_time']*1000:.3f}ms")
    
    print(f"\n⚔️  Combat Components:")
    print(f"  Rounds/sec: {combat_results['rounds_per_second']:,.0f}")
    print(f"  Est. Battles/sec: {combat_results['estimated_battles_per_second']:.2f}")
    print(f"  Est. Battles/hour: {combat_results['estimated_battles_per_hour']:,.0f}")
    print(f"  Mean time: {combat_results['mean_time']*1000:.3f}ms")

    print(f"\n🎯 RL TRAINING ASSESSMENT:")
    target_games_per_hour = 1000
    actual_games_per_hour = combat_results['estimated_battles_per_hour']
    
    if actual_games_per_hour >= target_games_per_hour:
        print(f"  ✅ EXCELLENT: {actual_games_per_hour:,.0f} games/hour (target: {target_games_per_hour:,})")
        print(f"  Performance is {actual_games_per_hour/target_games_per_hour:.1f}x the target!")
    else:
        print(f"  ⚠️  NEEDS OPTIMIZATION: {actual_games_per_hour:,.0f} games/hour (target: {target_games_per_hour:,})")
        print(f"  Performance is {actual_games_per_hour/target_games_per_hour:.1f}x the target")
    
    print("\n" + "=" * 60)
    
    return {
        "status_effects": status_results,
        "damage_calculation": damage_results,
        "resource_management": resource_results,
        "shop_generation": shop_results,
        "combat_components": combat_results
    }


if __name__ == "__main__":
    results = run_full_benchmark()
