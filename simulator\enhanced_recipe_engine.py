"""
Enhanced Recipe Engine

Implements the complete recipe/combination system as specified in Section 6
of DeepResearch.txt:

- Recipe Activation Logic: Automatic between battle and shop
- Catalysts: Items that enable but aren't consumed
- Combination Locks: Prevent unwanted combinations
- Resolution: Remove ingredients, place result
- Adjacency: Items must share grid edges
"""

from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum

from simulator.enhanced_item_database import EnhancedItem


@dataclass
class Recipe:
    """Enhanced recipe with catalyst support"""
    result_item: str
    ingredients: List[str]
    catalyst: Optional[str] = None
    required_class: Optional[str] = None
    description: str = ""
    
    def __post_init__(self):
        # Sort ingredients for consistent matching
        self.ingredients = sorted(self.ingredients)
    
    def matches_ingredients(self, available_items: List[str]) -> bool:
        """Check if available items match this recipe's ingredients"""
        available_sorted = sorted(available_items)
        return available_sorted == self.ingredients
    
    def has_catalyst(self, available_items: List[str]) -> bool:
        """Check if catalyst is available (if required)"""
        if not self.catalyst:
            return True
        return self.catalyst in available_items


class GridPosition:
    """Represents a position on the backpack grid"""
    def __init__(self, x: int, y: int):
        self.x = x
        self.y = y
    
    def __eq__(self, other):
        return self.x == other.x and self.y == other.y
    
    def __hash__(self):
        return hash((self.x, self.y))
    
    def is_adjacent_to(self, other: 'GridPosition') -> bool:
        """Check if this position is adjacent to another (shares an edge)"""
        dx = abs(self.x - other.x)
        dy = abs(self.y - other.y)
        return (dx == 1 and dy == 0) or (dx == 0 and dy == 1)
    
    def get_adjacent_positions(self) -> List['GridPosition']:
        """Get all adjacent positions"""
        return [
            GridPosition(self.x + 1, self.y),
            GridPosition(self.x - 1, self.y),
            GridPosition(self.x, self.y + 1),
            GridPosition(self.x, self.y - 1)
        ]


@dataclass
class PlacedItem:
    """Represents an item placed on the grid"""
    item: EnhancedItem
    position: GridPosition  # Top-left corner
    is_locked: bool = False  # Combination lock
    placement_order: int = 0  # For FIFO ordering

    def __hash__(self):
        return hash((self.item.item_name, self.position.x, self.position.y, self.placement_order))

    def __eq__(self, other):
        if not isinstance(other, PlacedItem):
            return False
        return (self.item.item_name == other.item.item_name and
                self.position.x == other.position.x and
                self.position.y == other.position.y and
                self.placement_order == other.placement_order)
    
    def get_occupied_positions(self) -> List[GridPosition]:
        """Get all grid positions occupied by this item"""
        positions = []
        for y, row in enumerate(self.item.grid_shape):
            for x, occupied in enumerate(row):
                if occupied:
                    positions.append(GridPosition(
                        self.position.x + x,
                        self.position.y + y
                    ))
        return positions
    
    def is_adjacent_to(self, other: 'PlacedItem') -> bool:
        """Check if this item is adjacent to another item"""
        my_positions = set(self.get_occupied_positions())
        other_positions = set(other.get_occupied_positions())
        
        for my_pos in my_positions:
            for other_pos in other_positions:
                if my_pos.is_adjacent_to(other_pos):
                    return True
        return False


class EnhancedRecipeEngine:
    """Enhanced recipe engine with full feature support"""
    
    def __init__(self):
        self.recipes: List[Recipe] = []
        self.placed_items: List[PlacedItem] = []
        self.next_placement_order = 1
        self._load_recipes()
    
    def _load_recipes(self):
        """Load all recipes from the research specifications"""
        # Master Recipe List from Table 6.1
        self.recipes = [
            # Basic weapon upgrades
            Recipe(
                result_item="Hero Sword",
                ingredients=["Wooden Sword", "Whetstone", "Whetstone"],
                description="Upgrade wooden sword with whetstones"
            ),
            Recipe(
                result_item="Hero Longsword",
                ingredients=["Hero Sword", "Whetstone", "Whetstone"],
                description="Further upgrade hero sword"
            ),
            Recipe(
                result_item="Falcon Blade",
                ingredients=["Hero Sword", "Gloves of Haste", "Gloves of Haste"],
                description="Speed-focused sword upgrade"
            ),
            Recipe(
                result_item="Crossblades",
                ingredients=["Falcon Blade", "Hero Longsword"],
                description="Combine two upgraded swords"
            ),
            
            # Torch variants
            Recipe(
                result_item="Torch",
                ingredients=["Wooden Sword", "Lump of Coal"],
                description="Basic torch creation"
            ),
            Recipe(
                result_item="Burning Torch",
                ingredients=["Torch", "Flame"],
                description="Enhance torch with fire"
            ),
            Recipe(
                result_item="Magic Torch",
                ingredients=["Torch", "Mana Potion"],
                description="Magical torch variant"
            ),
            
            # Dagger variants
            Recipe(
                result_item="Poison Dagger",
                ingredients=["Dagger", "Pestilence Flask"],
                description="Poison-enhanced dagger"
            ),
            Recipe(
                result_item="Bloody Dagger",
                ingredients=["Dagger", "Blood Amulet"],
                description="Vampiric dagger"
            ),
            
            # Advanced weapons
            Recipe(
                result_item="Bloodthorne",
                ingredients=["Hungry Blade", "Thorn Whip"],
                description="Combine life-stealing weapons"
            ),
            Recipe(
                result_item="Manathirst",
                ingredients=["Hungry Blade", "Mana Orb"],
                description="Mana-draining weapon"
            ),
            
            # Shields
            Recipe(
                result_item="Spiked Shield",
                ingredients=["Wooden Buckler", "Walrus Tusk"],
                description="Add spikes to shield"
            ),
            
            # Pets
            Recipe(
                result_item="Steel Goobert",
                ingredients=["Goobert", "Hero Sword"],
                description="Armored pet variant"
            ),
            Recipe(
                result_item="Poison Goobert",
                ingredients=["Goobert", "Fly Agaric", "Fly Agaric"],
                description="Poisonous pet variant",
                required_class="Reaper"
            ),
            
            # Potions
            Recipe(
                result_item="Strong Health Potion",
                ingredients=["Health Potion", "Healing Herbs"],
                description="Enhanced healing potion"
            ),
            Recipe(
                result_item="Mana Potion",
                ingredients=["Health Potion", "Blueberries"],
                description="Convert health to mana potion"
            ),
            
            # Armor
            Recipe(
                result_item="Vampiric Armor",
                ingredients=["Leather Armor", "Blood Amulet"],
                description="Life-stealing armor"
            ),
            Recipe(
                result_item="Sun Armor",
                ingredients=["Holy Armor", "Flame", "Flame"],
                description="Holy fire armor",
                required_class="Pyromancer"
            ),
            Recipe(
                result_item="Molten Spear",
                ingredients=["Spear", "Flame", "Flame"],
                description="Fire-enhanced spear",
                required_class="Pyromancer"
            ),
            
            # Berserker recipes with catalyst
            Recipe(
                result_item="Double Axe",
                ingredients=["Axe", "Axe"],
                description="Combine two axes",
                required_class="Berserker"
            ),
            Recipe(
                result_item="Dragonscale Armor",
                ingredients=["Leather Armor"],
                catalyst="Forging Hammer",
                description="Upgrade armor with forging hammer",
                required_class="Berserker"
            ),
            
            # Ranger recipes
            Recipe(
                result_item="Fortuna's Grace",
                ingredients=["Bow and Arrow", "Lucky Clover", "Lucky Clover"],
                description="Luck-enhanced bow",
                required_class="Ranger"
            )
        ]
    
    def add_item(self, item: EnhancedItem, position: GridPosition, is_locked: bool = False) -> bool:
        """Add an item to the grid"""
        placed_item = PlacedItem(
            item=item,
            position=position,
            is_locked=is_locked,
            placement_order=self.next_placement_order
        )
        self.next_placement_order += 1
        self.placed_items.append(placed_item)
        return True
    
    def remove_item(self, item_name: str) -> Optional[PlacedItem]:
        """Remove an item from the grid"""
        for i, placed_item in enumerate(self.placed_items):
            if placed_item.item.item_name == item_name:
                return self.placed_items.pop(i)
        return None
    
    def toggle_combination_lock(self, item_name: str) -> bool:
        """Toggle the combination lock on an item"""
        for placed_item in self.placed_items:
            if placed_item.item.item_name == item_name:
                placed_item.is_locked = not placed_item.is_locked
                return True
        return False
    
    def find_adjacent_groups(self) -> List[List[PlacedItem]]:
        """Find groups of adjacent items"""
        groups = []
        processed = set()
        
        for item in self.placed_items:
            if item in processed:
                continue
            
            # Start a new group
            group = []
            to_process = [item]
            
            while to_process:
                current = to_process.pop()
                if current in processed:
                    continue
                
                processed.add(current)
                group.append(current)
                
                # Find adjacent items
                for other in self.placed_items:
                    if other not in processed and current.is_adjacent_to(other):
                        to_process.append(other)
            
            groups.append(group)
        
        return groups
    
    def find_valid_recipes(self) -> List[Tuple[Recipe, List[PlacedItem], Optional[PlacedItem]]]:
        """Find all valid recipes that can be activated"""
        valid_recipes = []
        adjacent_groups = self.find_adjacent_groups()
        
        for group in adjacent_groups:
            # Skip if any item in group is locked
            if any(item.is_locked for item in group):
                continue
            
            # Get item names in this group
            item_names = [item.item.item_name for item in group]
            
            # Check each recipe
            for recipe in self.recipes:
                # Check if ingredients match
                if not recipe.matches_ingredients(item_names):
                    continue
                
                # Find catalyst if required
                catalyst_item = None
                if recipe.catalyst:
                    # Look for catalyst in adjacent items
                    catalyst_found = False
                    for item in group:
                        if item.item.item_name == recipe.catalyst:
                            catalyst_item = item
                            catalyst_found = True
                            break
                    
                    if not catalyst_found:
                        continue
                
                # Get ingredient items (excluding catalyst)
                ingredient_items = []
                for item in group:
                    if item.item.item_name in recipe.ingredients:
                        ingredient_items.append(item)
                
                # Verify we have all ingredients
                ingredient_names = [item.item.item_name for item in ingredient_items]
                if recipe.matches_ingredients(ingredient_names):
                    valid_recipes.append((recipe, ingredient_items, catalyst_item))
        
        return valid_recipes
    
    def execute_recipe(self, recipe: Recipe, ingredient_items: List[PlacedItem], 
                      catalyst_item: Optional[PlacedItem]) -> Optional[PlacedItem]:
        """Execute a recipe and return the result item"""
        # Remove ingredient items (but not catalyst)
        for item in ingredient_items:
            if item in self.placed_items:
                self.placed_items.remove(item)
        
        # Create result item (this would need to load from database)
        # For now, create a placeholder
        from simulator.enhanced_item_database import EnhancedItem, ItemRarity, ItemClass, ItemType
        
        result_item = EnhancedItem(
            item_id=f"recipe_{recipe.result_item}",
            item_name=recipe.result_item,
            rarity=ItemRarity.EPIC,  # Most crafted items are epic+
            item_class=ItemClass.NEUTRAL,
            item_type=ItemType.WEAPON,
            subtype=[],
            cost=0,  # Crafted items have no shop cost
            grid_shape=[[True, True], [True, True]],  # Default 2x2
            sockets=1,
            in_shop=False,
            stats={},
            effects=[],
            description=recipe.description
        )
        
        # Place result item where ingredients were
        if ingredient_items:
            result_position = ingredient_items[0].position
            result_placed = PlacedItem(
                item=result_item,
                position=result_position,
                placement_order=self.next_placement_order
            )
            self.next_placement_order += 1
            self.placed_items.append(result_placed)
            return result_placed
        
        return None
    
    def process_combinations(self) -> List[str]:
        """Process all valid combinations and return results"""
        results = []
        valid_recipes = self.find_valid_recipes()
        
        for recipe, ingredient_items, catalyst_item in valid_recipes:
            result = self.execute_recipe(recipe, ingredient_items, catalyst_item)
            if result:
                results.append(f"Created {recipe.result_item}")
        
        return results
    
    def get_item_at_position(self, position: GridPosition) -> Optional[PlacedItem]:
        """Get the item at a specific grid position"""
        for item in self.placed_items:
            if position in item.get_occupied_positions():
                return item
        return None
    
    def get_locked_items(self) -> List[PlacedItem]:
        """Get all items with combination locks"""
        return [item for item in self.placed_items if item.is_locked]
    
    def get_recipe_suggestions(self, available_items: List[str]) -> List[Recipe]:
        """Get recipes that could be made with available items"""
        suggestions = []
        
        for recipe in self.recipes:
            # Check if we have all ingredients
            missing_ingredients = []
            for ingredient in recipe.ingredients:
                if ingredient not in available_items:
                    missing_ingredients.append(ingredient)
            
            # If we're only missing 1-2 ingredients, suggest it
            if len(missing_ingredients) <= 2:
                suggestions.append(recipe)
        
        return suggestions
