"""
Gemstone Context System

Implements the context-dependent gem effects as specified in Section 4.4
of DeepResearch.txt:

- Rarity Progression: Chipped → Flawed → Regular → Flawless → Perfect
- Context-Dependent Effects: Different effects in weapon/armor/backpack
- Combination Logic: 2 same gems → next tier
- Effect Function: get_gem_effect(type, rarity, context)
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from simulator.status_effects import StatusEffectManager


class GemType(Enum):
    RUBY = "Ruby"
    SAPPHIRE = "Sapphire"
    EMERALD = "Emerald"
    TOPAZ = "Topaz"
    AMETHYST = "Amethyst"
    DIAMOND = "Diamond"
    ONYX = "Onyx"


class GemRarity(Enum):
    CHIPPED = "Chipped"
    FLAWED = "Flawed"
    REGULAR = "Regular"
    FLAWLESS = "Flawless"
    PERFECT = "Perfect"


class GemContext(Enum):
    WEAPON = "Weapon"
    ARMOR = "Armor"
    BACKPACK = "Backpack"  # Not socketed, just in backpack


@dataclass
class GemEffect:
    """Represents a gem effect in a specific context"""
    gem_type: GemType
    gem_rarity: GemRarity
    context: GemContext
    effect_type: str
    magnitude: float
    description: str


@dataclass
class Gemstone:
    """Represents a gemstone instance"""
    gem_type: GemType
    gem_rarity: GemRarity
    socketed_in: Optional[str] = None  # Item name it's socketed in
    position: Tuple[int, int] = (0, 0)  # Grid position if in backpack
    
    def get_tier_value(self) -> int:
        """Get numeric tier value for combination logic"""
        tier_values = {
            GemRarity.CHIPPED: 1,
            GemRarity.FLAWED: 2,
            GemRarity.REGULAR: 3,
            GemRarity.FLAWLESS: 4,
            GemRarity.PERFECT: 5
        }
        return tier_values[self.gem_rarity]
    
    def can_combine_with(self, other: 'Gemstone') -> bool:
        """Check if this gem can combine with another"""
        return (self.gem_type == other.gem_type and 
                self.gem_rarity == other.gem_rarity and
                self.gem_rarity != GemRarity.PERFECT)  # Perfect gems can't be upgraded
    
    def get_combined_result(self) -> Optional['Gemstone']:
        """Get the result of combining two identical gems"""
        if self.gem_rarity == GemRarity.PERFECT:
            return None
        
        next_rarity = {
            GemRarity.CHIPPED: GemRarity.FLAWED,
            GemRarity.FLAWED: GemRarity.REGULAR,
            GemRarity.REGULAR: GemRarity.FLAWLESS,
            GemRarity.FLAWLESS: GemRarity.PERFECT
        }
        
        return Gemstone(self.gem_type, next_rarity[self.gem_rarity])


class GemstoneEffectCalculator:
    """Calculates gem effects based on type, rarity, and context"""
    
    def __init__(self):
        self.base_effects = self._initialize_base_effects()
        self.rarity_multipliers = self._initialize_rarity_multipliers()
    
    def _initialize_base_effects(self) -> Dict[Tuple[GemType, GemContext], Dict[str, Any]]:
        """Initialize base effects for each gem type and context"""
        return {
            # Ruby effects
            (GemType.RUBY, GemContext.WEAPON): {
                "effect_type": "lifesteal",
                "base_magnitude": 1.0,
                "description": "Lifesteal on hit"
            },
            (GemType.RUBY, GemContext.ARMOR): {
                "effect_type": "healing_received",
                "base_magnitude": 0.25,
                "description": "Increases all healing received"
            },
            (GemType.RUBY, GemContext.BACKPACK): {
                "effect_type": "regeneration",
                "base_magnitude": 1.0,
                "description": "Grants Regeneration stacks"
            },
            
            # Sapphire effects
            (GemType.SAPPHIRE, GemContext.WEAPON): {
                "effect_type": "mana_on_hit",
                "base_magnitude": 1.0,
                "description": "Gain mana on hit"
            },
            (GemType.SAPPHIRE, GemContext.ARMOR): {
                "effect_type": "mana_shield",
                "base_magnitude": 0.5,
                "description": "Mana absorbs damage"
            },
            (GemType.SAPPHIRE, GemContext.BACKPACK): {
                "effect_type": "max_mana",
                "base_magnitude": 2.0,
                "description": "Increases maximum mana"
            },
            
            # Emerald effects
            (GemType.EMERALD, GemContext.WEAPON): {
                "effect_type": "poison_on_hit",
                "base_magnitude": 1.0,
                "description": "Inflict Poison on hit"
            },
            (GemType.EMERALD, GemContext.ARMOR): {
                "effect_type": "poison_resistance",
                "base_magnitude": 0.25,
                "description": "Reduces Poison damage taken"
            },
            (GemType.EMERALD, GemContext.BACKPACK): {
                "effect_type": "nature_synergy",
                "base_magnitude": 1.0,
                "description": "Enhances Nature item effects"
            },
            
            # Topaz effects
            (GemType.TOPAZ, GemContext.WEAPON): {
                "effect_type": "stamina_efficiency",
                "base_magnitude": 0.15,
                "description": "Reduces stamina cost"
            },
            (GemType.TOPAZ, GemContext.ARMOR): {
                "effect_type": "stamina_on_hit",
                "base_magnitude": 1.0,
                "description": "Gain stamina when hit"
            },
            (GemType.TOPAZ, GemContext.BACKPACK): {
                "effect_type": "stamina_regen",
                "base_magnitude": 0.25,
                "description": "Increases stamina regeneration"
            },
            
            # Amethyst effects
            (GemType.AMETHYST, GemContext.WEAPON): {
                "effect_type": "blind_on_hit",
                "base_magnitude": 1.0,
                "description": "Inflict Blind on hit"
            },
            (GemType.AMETHYST, GemContext.ARMOR): {
                "effect_type": "accuracy_reduction",
                "base_magnitude": 0.1,
                "description": "Reduces incoming accuracy"
            },
            (GemType.AMETHYST, GemContext.BACKPACK): {
                "effect_type": "luck_bonus",
                "base_magnitude": 1.0,
                "description": "Grants Luck stacks"
            },
            
            # Diamond effects
            (GemType.DIAMOND, GemContext.WEAPON): {
                "effect_type": "armor_penetration",
                "base_magnitude": 0.25,
                "description": "Ignores armor/block"
            },
            (GemType.DIAMOND, GemContext.ARMOR): {
                "effect_type": "damage_reflection",
                "base_magnitude": 0.15,
                "description": "Reflects damage"
            },
            (GemType.DIAMOND, GemContext.BACKPACK): {
                "effect_type": "all_stats",
                "base_magnitude": 1.0,
                "description": "Small bonus to all stats"
            },
            
            # Onyx effects
            (GemType.ONYX, GemContext.WEAPON): {
                "effect_type": "critical_damage",
                "base_magnitude": 0.5,
                "description": "Increases critical damage"
            },
            (GemType.ONYX, GemContext.ARMOR): {
                "effect_type": "critical_resistance",
                "base_magnitude": 0.2,
                "description": "Reduces critical chance against you"
            },
            (GemType.ONYX, GemContext.BACKPACK): {
                "effect_type": "dark_synergy",
                "base_magnitude": 1.0,
                "description": "Enhances Dark item effects"
            }
        }
    
    def _initialize_rarity_multipliers(self) -> Dict[GemRarity, float]:
        """Initialize rarity multipliers for gem effects"""
        return {
            GemRarity.CHIPPED: 1.0,
            GemRarity.FLAWED: 2.0,
            GemRarity.REGULAR: 4.0,
            GemRarity.FLAWLESS: 8.0,
            GemRarity.PERFECT: 16.0
        }
    
    def get_gem_effect(self, gem_type: GemType, gem_rarity: GemRarity, 
                      context: GemContext) -> Optional[GemEffect]:
        """Get the effect of a gem in a specific context"""
        base_effect = self.base_effects.get((gem_type, context))
        if not base_effect:
            return None
        
        multiplier = self.rarity_multipliers[gem_rarity]
        final_magnitude = base_effect["base_magnitude"] * multiplier
        
        return GemEffect(
            gem_type=gem_type,
            gem_rarity=gem_rarity,
            context=context,
            effect_type=base_effect["effect_type"],
            magnitude=final_magnitude,
            description=f"{gem_rarity.value} {gem_type.value}: {base_effect['description']}"
        )
    
    def calculate_total_effects(self, gems: List[Gemstone], 
                              item_contexts: Dict[str, GemContext]) -> Dict[str, float]:
        """Calculate total effects from all gems"""
        total_effects = {}
        
        for gem in gems:
            if gem.socketed_in:
                context = item_contexts.get(gem.socketed_in, GemContext.BACKPACK)
            else:
                context = GemContext.BACKPACK
            
            effect = self.get_gem_effect(gem.gem_type, gem.gem_rarity, context)
            if effect:
                if effect.effect_type not in total_effects:
                    total_effects[effect.effect_type] = 0
                total_effects[effect.effect_type] += effect.magnitude
        
        return total_effects


class GemstoneManager:
    """Manages gemstone inventory, socketing, and combinations"""
    
    def __init__(self):
        self.effect_calculator = GemstoneEffectCalculator()
        self.gems: List[Gemstone] = []
        self.item_contexts: Dict[str, GemContext] = {}  # item_name -> context
    
    def add_gem(self, gem: Gemstone) -> bool:
        """Add a gem to inventory"""
        self.gems.append(gem)
        return True
    
    def socket_gem(self, gem: Gemstone, item_name: str) -> bool:
        """Socket a gem into an item"""
        if gem in self.gems and not gem.socketed_in:
            gem.socketed_in = item_name
            return True
        return False
    
    def unsocket_gem(self, gem: Gemstone) -> bool:
        """Remove a gem from its socket"""
        if gem.socketed_in:
            gem.socketed_in = None
            return True
        return False
    
    def combine_gems(self, gem1: Gemstone, gem2: Gemstone) -> Optional[Gemstone]:
        """Combine two identical gems into a higher tier"""
        if not gem1.can_combine_with(gem2):
            return None
        
        if gem1 in self.gems and gem2 in self.gems:
            # Remove the two input gems
            self.gems.remove(gem1)
            self.gems.remove(gem2)
            
            # Create the combined result
            result = gem1.get_combined_result()
            if result:
                self.gems.append(result)
                return result
        
        return None
    
    def get_socketed_gems(self, item_name: str) -> List[Gemstone]:
        """Get all gems socketed in a specific item"""
        return [gem for gem in self.gems if gem.socketed_in == item_name]
    
    def get_backpack_gems(self) -> List[Gemstone]:
        """Get all gems in backpack (not socketed)"""
        return [gem for gem in self.gems if not gem.socketed_in]
    
    def set_item_context(self, item_name: str, context: GemContext):
        """Set the context (weapon/armor) for an item"""
        self.item_contexts[item_name] = context
    
    def get_total_effects(self) -> Dict[str, float]:
        """Get total effects from all gems"""
        return self.effect_calculator.calculate_total_effects(self.gems, self.item_contexts)
    
    def get_gem_effects_for_item(self, item_name: str) -> List[GemEffect]:
        """Get all gem effects for a specific item"""
        socketed_gems = self.get_socketed_gems(item_name)
        context = self.item_contexts.get(item_name, GemContext.BACKPACK)
        
        effects = []
        for gem in socketed_gems:
            effect = self.effect_calculator.get_gem_effect(gem.gem_type, gem.gem_rarity, context)
            if effect:
                effects.append(effect)
        
        return effects
    
    def find_combinable_gems(self) -> List[Tuple[Gemstone, Gemstone]]:
        """Find pairs of gems that can be combined"""
        combinable_pairs = []
        
        for i, gem1 in enumerate(self.gems):
            for gem2 in self.gems[i+1:]:
                if gem1.can_combine_with(gem2):
                    combinable_pairs.append((gem1, gem2))
        
        return combinable_pairs
