"""
Bag Expansion Mechanics System

Implements dynamic bag expansion as specified in the research:
- Bag items that add grid space when placed
- Contents move with the bag when repositioned
- Nested bag support (bags within bags)
- Grid space calculation and validation
- Expansion effects and bonuses
"""

from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum

from simulator.enhanced_item_database import EnhancedItem


class BagType(Enum):
    BASIC_BAG = "Basic Bag"
    LARGE_BAG = "Large Bag"
    MAGIC_POUCH = "Magic Pouch"
    DIMENSIONAL_BAG = "Dimensional Bag"
    SPECIALTY_BAG = "Specialty Bag"


@dataclass
class BagExpansion:
    """Represents the expansion provided by a bag item"""
    width_bonus: int
    height_bonus: int
    total_slots_bonus: int
    special_effects: List[str]
    description: str
    
    def get_total_expansion(self) -> int:
        """Get total grid cells added"""
        return max(self.width_bonus * self.height_bonus, self.total_slots_bonus)


@dataclass
class GridCell:
    """Represents a single cell in the backpack grid"""
    x: int
    y: int
    is_base_grid: bool = True  # True if part of original grid
    added_by_bag: Optional[str] = None  # Name of bag that added this cell
    occupied_by: Optional[str] = None  # Name of item occupying this cell
    
    def __hash__(self):
        return hash((self.x, self.y))
    
    def __eq__(self, other):
        return isinstance(other, GridCell) and self.x == other.x and self.y == other.y


@dataclass
class PlacedBagItem:
    """Represents a bag item placed in the backpack"""
    bag_item: EnhancedItem
    position: Tuple[int, int]  # Top-left corner
    expansion: BagExpansion
    contained_items: List[str]  # Items inside this bag
    is_active: bool = True
    
    def get_occupied_cells(self) -> List[Tuple[int, int]]:
        """Get all cells occupied by this bag"""
        cells = []
        for y, row in enumerate(self.bag_item.grid_shape):
            for x, occupied in enumerate(row):
                if occupied:
                    cells.append((self.position[0] + x, self.position[1] + y))
        return cells
    
    def get_expansion_area(self) -> List[Tuple[int, int]]:
        """Get the area of grid expansion provided by this bag"""
        # This would depend on the specific bag's expansion pattern
        # For now, assume it expands to the right and down
        expansion_cells = []
        
        if self.expansion.width_bonus > 0 or self.expansion.height_bonus > 0:
            # Calculate expansion area based on bag position
            start_x = self.position[0] + len(self.bag_item.grid_shape[0])
            start_y = self.position[1]
            
            for y in range(self.expansion.height_bonus):
                for x in range(self.expansion.width_bonus):
                    expansion_cells.append((start_x + x, start_y + y))
        
        return expansion_cells


class BagExpansionManager:
    """Manages bag expansion mechanics and grid space calculation"""
    
    def __init__(self, base_width: int = 9, base_height: int = 7):
        self.base_width = base_width
        self.base_height = base_height
        self.placed_bags: List[PlacedBagItem] = []
        self.grid_cells: Dict[Tuple[int, int], GridCell] = {}
        self.bag_definitions = self._initialize_bag_definitions()
        self._initialize_base_grid()
    
    def _initialize_base_grid(self):
        """Initialize the base 9x7 grid"""
        for y in range(self.base_height):
            for x in range(self.base_width):
                cell = GridCell(x, y, is_base_grid=True)
                self.grid_cells[(x, y)] = cell
    
    def _initialize_bag_definitions(self) -> Dict[str, BagExpansion]:
        """Initialize bag expansion definitions"""
        return {
            # Basic expansion bags
            "Small Bag": BagExpansion(
                width_bonus=2,
                height_bonus=2,
                total_slots_bonus=4,
                special_effects=[],
                description="Adds 2x2 grid space"
            ),
            "Medium Bag": BagExpansion(
                width_bonus=3,
                height_bonus=2,
                total_slots_bonus=6,
                special_effects=[],
                description="Adds 3x2 grid space"
            ),
            "Large Bag": BagExpansion(
                width_bonus=3,
                height_bonus=3,
                total_slots_bonus=9,
                special_effects=[],
                description="Adds 3x3 grid space"
            ),
            
            # Specialty bags with effects
            "Quiver": BagExpansion(
                width_bonus=1,
                height_bonus=4,
                total_slots_bonus=4,
                special_effects=["ranged_weapon_bonus"],
                description="Adds 1x4 space, +10% ranged weapon damage"
            ),
            "Potion Belt": BagExpansion(
                width_bonus=4,
                height_bonus=1,
                total_slots_bonus=4,
                special_effects=["potion_efficiency"],
                description="Adds 4x1 space, potions 25% more effective"
            ),
            "Gem Pouch": BagExpansion(
                width_bonus=2,
                height_bonus=2,
                total_slots_bonus=4,
                special_effects=["gem_combination_bonus"],
                description="Adds 2x2 space, gems combine 50% faster"
            ),
            "Tool Belt": BagExpansion(
                width_bonus=3,
                height_bonus=1,
                total_slots_bonus=3,
                special_effects=["tool_efficiency"],
                description="Adds 3x1 space, tools activate 20% faster"
            ),
            
            # Advanced bags
            "Dimensional Pouch": BagExpansion(
                width_bonus=4,
                height_bonus=4,
                total_slots_bonus=16,
                special_effects=["dimensional_storage"],
                description="Adds 4x4 space, items inside don't count for weight"
            ),
            "Bag of Holding": BagExpansion(
                width_bonus=5,
                height_bonus=3,
                total_slots_bonus=15,
                special_effects=["extra_storage", "item_preservation"],
                description="Adds 5x3 space, items inside gain +1 durability"
            ),
            
            # Class-specific bags
            "Berserker Pack": BagExpansion(
                width_bonus=2,
                height_bonus=3,
                total_slots_bonus=6,
                special_effects=["battle_rage_extension"],
                description="Adds 2x3 space, Battle Rage lasts 2s longer"
            ),
            "Ranger Satchel": BagExpansion(
                width_bonus=3,
                height_bonus=2,
                total_slots_bonus=6,
                special_effects=["nature_synergy"],
                description="Adds 3x2 space, Nature items 15% more effective"
            ),
            "Reaper Coffin": BagExpansion(
                width_bonus=2,
                height_bonus=4,
                total_slots_bonus=8,
                special_effects=["dark_energy"],
                description="Adds 2x4 space, Dark items generate 1 extra effect"
            ),
            "Pyro Furnace": BagExpansion(
                width_bonus=3,
                height_bonus=3,
                total_slots_bonus=9,
                special_effects=["heat_generation"],
                description="Adds 3x3 space, generates 1 Heat every 10 seconds"
            )
        }
    
    def place_bag(self, bag_item: EnhancedItem, position: Tuple[int, int]) -> bool:
        """Place a bag item and expand the grid"""
        # Check if this is a valid bag item
        expansion = self.bag_definitions.get(bag_item.item_name)
        if not expansion:
            return False
        
        # Check if position is valid
        if not self._is_valid_placement(bag_item, position):
            return False
        
        # Create placed bag
        placed_bag = PlacedBagItem(
            bag_item=bag_item,
            position=position,
            expansion=expansion,
            contained_items=[]
        )
        
        # Add the bag to our tracking
        self.placed_bags.append(placed_bag)
        
        # Mark bag's occupied cells
        for cell_pos in placed_bag.get_occupied_cells():
            if cell_pos in self.grid_cells:
                self.grid_cells[cell_pos].occupied_by = bag_item.item_name
        
        # Add expansion cells
        for cell_pos in placed_bag.get_expansion_area():
            if cell_pos not in self.grid_cells:
                new_cell = GridCell(
                    x=cell_pos[0],
                    y=cell_pos[1],
                    is_base_grid=False,
                    added_by_bag=bag_item.item_name
                )
                self.grid_cells[cell_pos] = new_cell
        
        return True
    
    def remove_bag(self, bag_name: str) -> bool:
        """Remove a bag and its expansion"""
        # Find the bag
        bag_to_remove = None
        for bag in self.placed_bags:
            if bag.bag_item.item_name == bag_name:
                bag_to_remove = bag
                break
        
        if not bag_to_remove:
            return False
        
        # Remove items that were in this bag (they would drop to base grid or be lost)
        items_to_relocate = bag_to_remove.contained_items.copy()
        
        # Remove expansion cells
        cells_to_remove = []
        for pos, cell in self.grid_cells.items():
            if cell.added_by_bag == bag_name:
                cells_to_remove.append(pos)
        
        for pos in cells_to_remove:
            del self.grid_cells[pos]
        
        # Clear occupied cells
        for cell_pos in bag_to_remove.get_occupied_cells():
            if cell_pos in self.grid_cells:
                self.grid_cells[cell_pos].occupied_by = None
        
        # Remove from tracking
        self.placed_bags.remove(bag_to_remove)
        
        return True
    
    def move_bag(self, bag_name: str, new_position: Tuple[int, int]) -> bool:
        """Move a bag and its contents to a new position"""
        # Find the bag
        bag_to_move = None
        for bag in self.placed_bags:
            if bag.bag_item.item_name == bag_name:
                bag_to_move = bag
                break
        
        if not bag_to_move:
            return False
        
        # Store contained items
        contained_items = bag_to_move.contained_items.copy()
        
        # Remove bag from current position
        self.remove_bag(bag_name)
        
        # Place bag at new position
        success = self.place_bag(bag_to_move.bag_item, new_position)
        
        if success:
            # Restore contained items
            for bag in self.placed_bags:
                if bag.bag_item.item_name == bag_name:
                    bag.contained_items = contained_items
                    break
        
        return success
    
    def _is_valid_placement(self, bag_item: EnhancedItem, position: Tuple[int, int]) -> bool:
        """Check if a bag can be placed at the given position"""
        # Check if all required cells are available
        for y, row in enumerate(bag_item.grid_shape):
            for x, occupied in enumerate(row):
                if occupied:
                    cell_pos = (position[0] + x, position[1] + y)
                    
                    # Must be within base grid or existing expansion
                    if cell_pos not in self.grid_cells:
                        return False
                    
                    # Cell must not be occupied
                    if self.grid_cells[cell_pos].occupied_by is not None:
                        return False
        
        return True
    
    def get_total_grid_size(self) -> Tuple[int, int]:
        """Get the current total grid dimensions"""
        if not self.grid_cells:
            return (self.base_width, self.base_height)
        
        max_x = max(cell.x for cell in self.grid_cells.values())
        max_y = max(cell.y for cell in self.grid_cells.values())
        min_x = min(cell.x for cell in self.grid_cells.values())
        min_y = min(cell.y for cell in self.grid_cells.values())
        
        return (max_x - min_x + 1, max_y - min_y + 1)
    
    def get_available_cells(self) -> List[Tuple[int, int]]:
        """Get all available (unoccupied) cells"""
        available = []
        for pos, cell in self.grid_cells.items():
            if cell.occupied_by is None:
                available.append(pos)
        return available
    
    def get_expansion_effects(self) -> Dict[str, List[str]]:
        """Get all active expansion effects from placed bags"""
        effects = {}
        for bag in self.placed_bags:
            if bag.is_active and bag.expansion.special_effects:
                effects[bag.bag_item.item_name] = bag.expansion.special_effects
        return effects
    
    def place_item_in_bag(self, item_name: str, bag_name: str) -> bool:
        """Place an item inside a specific bag"""
        for bag in self.placed_bags:
            if bag.bag_item.item_name == bag_name:
                if item_name not in bag.contained_items:
                    bag.contained_items.append(item_name)
                    return True
        return False
    
    def remove_item_from_bag(self, item_name: str, bag_name: str) -> bool:
        """Remove an item from a specific bag"""
        for bag in self.placed_bags:
            if bag.bag_item.item_name == bag_name:
                if item_name in bag.contained_items:
                    bag.contained_items.remove(item_name)
                    return True
        return False
    
    def get_bag_contents(self, bag_name: str) -> List[str]:
        """Get all items contained in a specific bag"""
        for bag in self.placed_bags:
            if bag.bag_item.item_name == bag_name:
                return bag.contained_items.copy()
        return []
    
    def get_grid_visualization(self) -> List[List[str]]:
        """Get a visual representation of the current grid"""
        max_x, max_y = self.get_total_grid_size()
        grid = [['.' for _ in range(max_x)] for _ in range(max_y)]
        
        for pos, cell in self.grid_cells.items():
            if 0 <= cell.x < max_x and 0 <= cell.y < max_y:
                if cell.occupied_by:
                    grid[cell.y][cell.x] = 'X'
                elif cell.is_base_grid:
                    grid[cell.y][cell.x] = 'O'
                else:
                    grid[cell.y][cell.x] = '+'
        
        return grid
