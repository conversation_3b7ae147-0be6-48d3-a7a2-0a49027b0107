"""
Test script for the Enhanced RL Environment

Validates that the new environment works correctly and can be used
for RL training with the enhanced simulation systems.
"""

import numpy as np
import time
from rl.enhanced_environment_adapter import create_enhanced_env


def test_environment_basic():
    """Test basic environment functionality"""
    print("🧪 Testing Enhanced Environment - Basic Functionality")
    print("=" * 60)
    
    # Create environment
    env = create_enhanced_env(player_class="Ranger", render_mode="human")
    
    print("✅ Environment created successfully")
    
    # Test reset
    observation, info = env.reset(seed=42)
    print("✅ Environment reset successfully")
    
    # Print observation space info
    print(f"\nObservation space keys: {list(observation.keys())}")
    print(f"Action space: {env.action_space}")
    
    # Test a few actions
    print("\n🎮 Testing Actions:")
    
    for i in range(5):
        action = env.action_space.sample()  # Random action
        obs, reward, terminated, truncated, info = env.step(action)
        
        print(f"  Step {i+1}: Action {action}, Reward {reward:.3f}, "
              f"Round {obs['round'][0]}, Health {obs['health'][0]}")
        
        if terminated or truncated:
            print("  Episode ended")
            break
    
    env.close()
    print("\n✅ Basic functionality test passed!")


def test_environment_performance():
    """Test environment performance for RL training"""
    print("\n🚀 Testing Enhanced Environment - Performance")
    print("=" * 60)
    
    env = create_enhanced_env(player_class="Ranger")
    
    # Performance test
    num_steps = 1000
    start_time = time.perf_counter()
    
    observation, info = env.reset(seed=42)
    
    for step in range(num_steps):
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        
        if terminated or truncated:
            observation, info = env.reset()
    
    end_time = time.perf_counter()
    total_time = end_time - start_time
    
    steps_per_second = num_steps / total_time
    
    print(f"Performance Results:")
    print(f"  Total steps: {num_steps}")
    print(f"  Total time: {total_time:.3f} seconds")
    print(f"  Steps per second: {steps_per_second:,.0f}")
    print(f"  Time per step: {(total_time/num_steps)*1000:.3f}ms")
    
    # Estimate training performance
    episodes_per_hour = (steps_per_second * 3600) / 100  # Assume 100 steps per episode
    print(f"  Estimated episodes/hour: {episodes_per_hour:,.0f}")
    
    env.close()
    
    if steps_per_second > 1000:
        print("✅ Performance test passed! (>1000 steps/sec)")
    else:
        print("⚠️  Performance below target (<1000 steps/sec)")


def test_observation_consistency():
    """Test that observations are consistent and valid"""
    print("\n🔍 Testing Enhanced Environment - Observation Consistency")
    print("=" * 60)
    
    env = create_enhanced_env(player_class="Ranger")
    
    observation, info = env.reset(seed=42)
    
    print("Observation validation:")
    
    # Check all required keys are present
    required_keys = [
        "round", "health", "gold", "lives", "trophies",
        "shop_items", "shop_costs", "shop_locks", "reroll_cost",
        "status_effects", "stamina", "max_stamina", "mana", "max_mana",
        "inventory_items", "last_battle_won"
    ]
    
    missing_keys = []
    for key in required_keys:
        if key not in observation:
            missing_keys.append(key)
    
    if missing_keys:
        print(f"❌ Missing observation keys: {missing_keys}")
    else:
        print("✅ All required observation keys present")
    
    # Check observation shapes and ranges
    print("\nObservation ranges:")
    for key, value in observation.items():
        if isinstance(value, np.ndarray):
            print(f"  {key}: shape={value.shape}, range=[{value.min():.2f}, {value.max():.2f}]")
    
    # Test multiple steps to ensure consistency
    print("\nTesting observation consistency over multiple steps:")
    
    for i in range(10):
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        
        # Check that observations maintain valid ranges
        if obs["health"][0] < 0 or obs["gold"][0] < 0:
            print(f"❌ Invalid observation at step {i}: health={obs['health'][0]}, gold={obs['gold'][0]}")
            break
        
        if terminated or truncated:
            observation, info = env.reset()
    else:
        print("✅ Observations remain consistent over multiple steps")
    
    env.close()


def test_reward_system():
    """Test the reward system"""
    print("\n💰 Testing Enhanced Environment - Reward System")
    print("=" * 60)
    
    env = create_enhanced_env(player_class="Ranger")
    
    observation, info = env.reset(seed=42)
    
    rewards = []
    actions_taken = []
    
    print("Testing different actions and their rewards:")
    
    # Test specific actions
    test_actions = [
        (0, "Buy item slot 0"),
        (5, "Reroll shop"),
        (6, "Toggle lock slot 0"),
        (11, "End shop phase"),
        (16, "Skip action")
    ]
    
    for action, description in test_actions:
        obs, reward, terminated, truncated, info = env.step(action)
        rewards.append(reward)
        actions_taken.append((action, description, reward))
        
        print(f"  {description}: reward = {reward:.3f}")
        
        if terminated or truncated:
            break
    
    print(f"\nReward statistics:")
    print(f"  Total rewards: {len(rewards)}")
    print(f"  Mean reward: {np.mean(rewards):.3f}")
    print(f"  Reward range: [{min(rewards):.3f}, {max(rewards):.3f}]")
    
    env.close()
    print("✅ Reward system test completed")


def run_all_tests():
    """Run all environment tests"""
    print("🧪 ENHANCED ENVIRONMENT TEST SUITE")
    print("=" * 60)
    
    try:
        test_environment_basic()
        test_environment_performance()
        test_observation_consistency()
        test_reward_system()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print("✅ Enhanced environment is ready for RL training")
        print("✅ Performance exceeds requirements")
        print("✅ Observations are consistent and valid")
        print("✅ Reward system is functioning")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
