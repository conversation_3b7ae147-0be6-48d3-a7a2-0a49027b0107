"""
Test the Vision System Integration
"""

import numpy as np
import cv2
from assist_mode.vision_system_integration import (
    VisionConfig, VisionSystemIntegrator, ItemRecognitionEngine,
    GridDetector, ScreenCapture, DetectedItem, GameStateCapture
)

def create_mock_game_image():
    """Create a mock game screenshot for testing"""
    # Create a 1920x1080 mock image
    image = np.zeros((1080, 1920, 3), dtype=np.uint8)
    
    # Fill with dark background
    image[:] = (20, 20, 30)
    
    # Draw inventory grid (9x7 cells)
    grid_x, grid_y = 800, 300
    grid_w, grid_h = 450, 350
    cell_w, cell_h = grid_w // 9, grid_h // 7
    
    # Draw grid lines
    for i in range(10):  # 10 vertical lines for 9 cells
        x = grid_x + i * cell_w
        cv2.line(image, (x, grid_y), (x, grid_y + grid_h), (100, 100, 100), 2)
    
    for i in range(8):  # 8 horizontal lines for 7 cells
        y = grid_y + i * cell_h
        cv2.line(image, (grid_x, y), (grid_x + grid_w, y), (100, 100, 100), 2)
    
    # Draw some mock items in grid
    # Item 1: Red rectangle (sword)
    item1_x, item1_y = grid_x + cell_w + 5, grid_y + cell_h + 5
    cv2.rectangle(image, (item1_x, item1_y), (item1_x + cell_w - 10, item1_y + cell_h - 10), (0, 0, 255), -1)
    cv2.putText(image, "SWORD", (item1_x + 5, item1_y + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # Item 2: Blue rectangle (armor)
    item2_x, item2_y = grid_x + 3 * cell_w + 5, grid_y + 2 * cell_h + 5
    cv2.rectangle(image, (item2_x, item2_y), (item2_x + cell_w - 10, item2_y + cell_h - 10), (255, 0, 0), -1)
    cv2.putText(image, "ARMOR", (item2_x + 5, item2_y + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # Draw shop area
    shop_x, shop_y = 100, 800
    shop_w, shop_h = 1000, 150
    cv2.rectangle(image, (shop_x, shop_y), (shop_x + shop_w, shop_y + shop_h), (50, 50, 50), -1)
    cv2.putText(image, "SHOP", (shop_x + 10, shop_y + 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    # Draw shop items
    for i in range(5):
        item_x = shop_x + 20 + i * 190
        item_y = shop_y + 50
        cv2.rectangle(image, (item_x, item_y), (item_x + 150, item_y + 80), (0, 255, 0), 2)
        cv2.putText(image, f"ITEM_{i+1}", (item_x + 10, item_y + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # Draw UI elements
    # Health bar
    cv2.rectangle(image, (50, 50), (300, 80), (255, 0, 0), -1)
    cv2.putText(image, "HEALTH: 85/100", (60, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # Gold
    cv2.putText(image, "GOLD: 42", (50, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 215, 0), 2)
    
    # Round
    cv2.putText(image, "ROUND: 3", (50, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    return image

def test_vision_system_integration():
    print("👁️ TESTING VISION SYSTEM INTEGRATION")
    print("=" * 60)
    
    # Test vision configuration
    print("\n⚙️ TESTING VISION CONFIGURATION:")
    print("-" * 30)
    
    config = VisionConfig(
        capture_fps=5,
        item_confidence_threshold=0.7,
        template_match_threshold=0.6,
        debug_mode=True
    )
    
    print(f"  Configuration created:")
    print(f"    Capture FPS: {config.capture_fps}")
    print(f"    Item confidence threshold: {config.item_confidence_threshold}")
    print(f"    Template match threshold: {config.template_match_threshold}")
    print(f"    Debug mode: {config.debug_mode}")
    
    # Test grid detector
    print("\n🔲 TESTING GRID DETECTOR:")
    print("-" * 30)
    
    grid_detector = GridDetector(config)
    mock_image = create_mock_game_image()
    
    print(f"  Created mock image: {mock_image.shape}")
    
    # Detect grid
    grid_region = grid_detector.detect_grid(mock_image)
    
    if grid_region:
        print(f"  Grid detected: {grid_region}")
        x, y, w, h = grid_region
        print(f"    Position: ({x}, {y})")
        print(f"    Size: {w}x{h}")
        print(f"    Aspect ratio: {w/h:.2f} (expected: {9/7:.2f})")
    else:
        print("  Grid not detected")
    
    # Test item recognition engine
    print("\n🔍 TESTING ITEM RECOGNITION ENGINE:")
    print("-" * 30)
    
    item_recognizer = ItemRecognitionEngine(config)
    
    print(f"  Item templates loaded: {len(item_recognizer.item_templates)}")
    print(f"  Grid template loaded: {item_recognizer.grid_template is not None}")
    
    # Test template matching with mock items
    if grid_region:
        detected_items = item_recognizer.detect_items_in_region(mock_image, grid_region)
        print(f"  Items detected in grid region: {len(detected_items)}")
        
        for i, item in enumerate(detected_items):
            print(f"    Item {i+1}: {item.item_name} (confidence: {item.confidence:.3f})")
            print(f"      Bounding box: {item.bounding_box}")
    
    # Test detected item structure
    print("\n📦 TESTING DETECTED ITEM STRUCTURE:")
    print("-" * 30)
    
    # Create mock detected items
    mock_items = [
        DetectedItem(
            item_name="Hero Sword",
            confidence=0.95,
            bounding_box=(850, 350, 50, 50),
            grid_position=(1, 1),
            rotation=0
        ),
        DetectedItem(
            item_name="Leather Armor", 
            confidence=0.87,
            bounding_box=(950, 400, 50, 50),
            grid_position=(3, 2),
            rotation=0
        )
    ]
    
    for i, item in enumerate(mock_items):
        print(f"  Mock item {i+1}:")
        print(f"    Name: {item.item_name}")
        print(f"    Confidence: {item.confidence}")
        print(f"    Grid position: {item.grid_position}")
        print(f"    Bounding box: {item.bounding_box}")
    
    # Test grid mapping
    if grid_region:
        mapped_items = grid_detector.map_items_to_grid(mock_items, grid_region)
        print(f"\n  Grid mapping results:")
        for item in mapped_items:
            print(f"    {item.item_name}: grid position {item.grid_position}")
    
    # Test game state capture
    print("\n📸 TESTING GAME STATE CAPTURE:")
    print("-" * 30)
    
    # Create mock game state capture
    game_capture = GameStateCapture(
        timestamp=**********.0,
        backpack_items=mock_items,
        shop_items=[None, mock_items[0], None, mock_items[1], None],
        player_stats={"health": 85, "gold": 42, "round": 3, "lives": 3},
        game_phase="shop",
        confidence_score=0.91
    )
    
    print(f"  Game state capture created:")
    print(f"    Timestamp: {game_capture.timestamp}")
    print(f"    Backpack items: {len(game_capture.backpack_items)}")
    print(f"    Shop items: {sum(1 for item in game_capture.shop_items if item is not None)}/5")
    print(f"    Player stats: {game_capture.player_stats}")
    print(f"    Game phase: {game_capture.game_phase}")
    print(f"    Confidence: {game_capture.confidence_score:.3f}")
    
    # Test conversion to simulation state
    sim_state = game_capture.to_simulation_state()
    print(f"\n  Simulation state conversion:")
    print(f"    Keys: {list(sim_state.keys())}")
    print(f"    Backpack items: {len(sim_state['backpack_items'])}")
    print(f"    Shop items: {len(sim_state['shop_items'])}")
    
    # Test vision system integrator
    print("\n🎯 TESTING VISION SYSTEM INTEGRATOR:")
    print("-" * 30)
    
    vision_system = VisionSystemIntegrator(config)
    
    print(f"  Vision system created with components:")
    print(f"    Item recognizer: {vision_system.item_recognizer is not None}")
    print(f"    Grid detector: {vision_system.grid_detector is not None}")
    print(f"    Screen capture: {vision_system.screen_capture is not None}")
    
    # Test game state capture with mock image
    captured_state = vision_system.capture_game_state(mock_image)
    
    if captured_state:
        print(f"\n  Game state captured successfully:")
        print(f"    Backpack items: {len(captured_state.backpack_items)}")
        print(f"    Shop items: {len(captured_state.shop_items)}")
        print(f"    Confidence: {captured_state.confidence_score:.3f}")
        print(f"    Phase: {captured_state.game_phase}")
    else:
        print("  Game state capture failed")
    
    # Test debug image generation
    debug_image = vision_system.get_debug_image(mock_image)
    if debug_image is not None:
        print(f"  Debug image generated: {debug_image.shape}")
    
    # Test screen capture (without actually starting it)
    print("\n📺 TESTING SCREEN CAPTURE:")
    print("-" * 30)
    
    screen_capture = ScreenCapture(config)
    
    print(f"  Screen capture created:")
    print(f"    Capture active: {screen_capture.capture_active}")
    print(f"    Frame queue size: {screen_capture.frame_queue.qsize()}")
    print(f"    Capture region: {config.capture_region}")
    
    # Test preprocessing
    preprocessed = screen_capture._preprocess_frame(mock_image)
    print(f"  Preprocessing test:")
    print(f"    Original shape: {mock_image.shape}")
    print(f"    Processed shape: {preprocessed.shape}")
    print(f"    Preprocessing applied: {not np.array_equal(mock_image, preprocessed)}")
    
    # Test template matching
    print("\n🎯 TESTING TEMPLATE MATCHING:")
    print("-" * 30)
    
    # Create a simple template
    template = np.ones((50, 50, 3), dtype=np.uint8) * 128  # Gray square
    
    # Test multiscale matching
    matches = item_recognizer._template_match_multiscale(mock_image, template)
    print(f"  Template matching results:")
    print(f"    Matches found: {len(matches)}")
    
    for i, match in enumerate(matches[:3]):  # Show first 3 matches
        print(f"    Match {i+1}: confidence={match['confidence']:.3f}, scale={match['scale']:.2f}")
    
    # Test non-maximum suppression
    if len(matches) > 1:
        suppressed = item_recognizer._non_maximum_suppression(matches)
        print(f"    After NMS: {len(suppressed)} matches")
    
    # Test IoU calculation
    if len(matches) >= 2:
        iou = item_recognizer._calculate_iou(matches[0], matches[1])
        print(f"    IoU between first two matches: {iou:.3f}")
    
    # Test confidence calculation
    print("\n📊 TESTING CONFIDENCE CALCULATION:")
    print("-" * 30)
    
    # Test with different confidence scenarios
    high_conf_items = [
        DetectedItem("Item1", 0.95, (0, 0, 50, 50)),
        DetectedItem("Item2", 0.92, (60, 0, 50, 50))
    ]
    
    low_conf_items = [
        DetectedItem("Item1", 0.65, (0, 0, 50, 50)),
        DetectedItem("Item2", 0.58, (60, 0, 50, 50))
    ]
    
    high_confidence = vision_system._calculate_confidence(high_conf_items, [])
    low_confidence = vision_system._calculate_confidence(low_conf_items, [])
    empty_confidence = vision_system._calculate_confidence([], [])
    
    print(f"  Confidence calculations:")
    print(f"    High confidence items: {high_confidence:.3f}")
    print(f"    Low confidence items: {low_confidence:.3f}")
    print(f"    Empty backpack: {empty_confidence:.3f}")
    
    # Test error handling
    print("\n❌ TESTING ERROR HANDLING:")
    print("-" * 30)
    
    # Test with invalid image
    try:
        invalid_capture = vision_system.capture_game_state(None)
        print(f"  Null image handling: {'Success' if invalid_capture is None else 'Failed'}")
    except Exception as e:
        print(f"  Null image error: {e}")
    
    # Test with corrupted image
    try:
        corrupted_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        corrupted_capture = vision_system.capture_game_state(corrupted_image)
        print(f"  Corrupted image handling: {'Success' if corrupted_capture is None else 'Handled'}")
    except Exception as e:
        print(f"  Corrupted image error: {e}")
    
    # Performance test
    print("\n⚡ TESTING PERFORMANCE:")
    print("-" * 30)
    
    import time
    
    # Time grid detection
    start_time = time.time()
    for _ in range(10):
        grid_detector.detect_grid(mock_image)
    grid_time = (time.time() - start_time) / 10
    
    # Time full capture
    start_time = time.time()
    for _ in range(5):
        vision_system.capture_game_state(mock_image)
    capture_time = (time.time() - start_time) / 5
    
    print(f"  Performance metrics:")
    print(f"    Grid detection: {grid_time*1000:.2f} ms")
    print(f"    Full capture: {capture_time*1000:.2f} ms")
    print(f"    Estimated FPS: {1/capture_time:.1f}")
    
    print("\n" + "=" * 60)
    print("✅ VISION SYSTEM INTEGRATION TEST COMPLETE")

if __name__ == "__main__":
    test_vision_system_integration()
