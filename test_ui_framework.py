"""
Test the UI Framework (without actually showing UI)
"""

import tkinter as tk
from assist_mode.ui_framework import (
    UIConfig, AssistModeUI, RecommendationWidget, BuildAnalysisWidget, 
    PerformanceWidget, create_assist_mode_ui
)
from assist_mode.build_recommendation_engine import (
    Recommendation, BuildAnalysis, RecommendationType, Priority
)
from assist_mode.real_time_analysis_pipeline import AnalysisConfig

def create_mock_recommendation():
    """Create a mock recommendation for testing"""
    return Recommendation(
        recommendation_type=RecommendationType.BUY_ITEM,
        priority=Priority.HIGH,
        confidence=0.85,
        action_parameters={"shop_index": 0, "item_name": "Hero Sword"},
        reasoning="This item provides excellent synergy with your current build",
        expected_benefit=0.6,
        risk_assessment="Low risk - affordable and fits build"
    )

def create_mock_build_analysis():
    """Create a mock build analysis for testing"""
    return BuildAnalysis(
        synergy_score=0.75,
        power_level=0.68,
        defensive_rating=0.45,
        offensive_rating=0.82,
        flexibility_score=0.55,
        completion_percentage=0.60,
        weaknesses=["Insufficient defensive items", "Low item synergy"],
        strengths=["High power level", "Strong berserker focus"],
        recommended_direction="Prioritize defensive items and build synergies"
    )

def test_ui_framework():
    print("🖥️ TESTING UI FRAMEWORK")
    print("=" * 60)
    
    # Test UI configuration
    print("\n⚙️ TESTING UI CONFIGURATION:")
    print("-" * 30)
    
    # Default config
    default_config = UIConfig()
    print(f"  Default UI config:")
    print(f"    Window size: {default_config.window_width}x{default_config.window_height}")
    print(f"    Theme: {default_config.theme}")
    print(f"    Font: {default_config.font_family} {default_config.font_size}")
    print(f"    Always on top: {default_config.always_on_top}")
    print(f"    Update interval: {default_config.ui_update_interval}ms")
    
    # Custom config
    custom_config = UIConfig(
        window_width=1000,
        window_height=800,
        theme="light",
        always_on_top=False,
        ui_update_interval=250
    )
    print(f"\n  Custom UI config:")
    print(f"    Window size: {custom_config.window_width}x{custom_config.window_height}")
    print(f"    Theme: {custom_config.theme}")
    print(f"    Always on top: {custom_config.always_on_top}")
    print(f"    Update interval: {custom_config.ui_update_interval}ms")
    
    # Test color schemes
    print(f"\n  Color schemes:")
    print(f"    Dark theme colors: {len(default_config.colors)} colors")
    print(f"      Background: {default_config.colors['bg']}")
    print(f"      Foreground: {default_config.colors['fg']}")
    print(f"      Accent: {default_config.colors['accent']}")
    
    print(f"    Light theme colors: {len(custom_config.colors)} colors")
    print(f"      Background: {custom_config.colors['bg']}")
    print(f"      Foreground: {custom_config.colors['fg']}")
    print(f"      Accent: {custom_config.colors['accent']}")
    
    # Test widget creation (without showing)
    print("\n🧩 TESTING WIDGET CREATION:")
    print("-" * 30)
    
    # Create root window (hidden)
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    try:
        # Test recommendation widget
        rec_widget = RecommendationWidget(root, default_config)
        print(f"  Recommendation widget created successfully")
        
        # Test with mock recommendation
        mock_rec = create_mock_recommendation()
        rec_widget.update_recommendation(mock_rec)
        print(f"    Updated with recommendation: {mock_rec.recommendation_type.value}")
        print(f"    Priority: {mock_rec.priority.value}")
        print(f"    Confidence: {mock_rec.confidence:.1%}")
        
        # Test clearing recommendation
        rec_widget.update_recommendation(None)
        print(f"    Cleared recommendation successfully")
        
        # Test build analysis widget
        analysis_widget = BuildAnalysisWidget(root, default_config)
        print(f"  Build analysis widget created successfully")
        
        # Test with mock analysis
        mock_analysis = create_mock_build_analysis()
        analysis_widget.update_analysis(mock_analysis)
        print(f"    Updated with analysis:")
        print(f"      Synergy: {mock_analysis.synergy_score:.1%}")
        print(f"      Power: {mock_analysis.power_level:.1%}")
        print(f"      Strengths: {len(mock_analysis.strengths)}")
        print(f"      Weaknesses: {len(mock_analysis.weaknesses)}")
        
        # Test clearing analysis
        analysis_widget.update_analysis(None)
        print(f"    Cleared analysis successfully")
        
        # Test performance widget
        perf_widget = PerformanceWidget(root, default_config)
        print(f"  Performance widget created successfully")
        
        # Test with mock performance data
        mock_status = {
            "running": True,
            "queue_size": 3,
            "max_queue_size": 10,
            "performance": {
                "analyses_per_second": 2.5,
                "average_processing_time": 0.085,
                "success_rate": 0.92
            }
        }
        perf_widget.update_performance(mock_status)
        print(f"    Updated with performance data:")
        print(f"      Running: {mock_status['running']}")
        print(f"      FPS: {mock_status['performance']['analyses_per_second']}")
        print(f"      Success rate: {mock_status['performance']['success_rate']:.1%}")
        
    except Exception as e:
        print(f"  Widget creation error: {e}")
    
    finally:
        root.destroy()
    
    # Test main UI creation (without showing)
    print("\n🖼️ TESTING MAIN UI CREATION:")
    print("-" * 30)
    
    try:
        # Create UI with custom configs
        ui_config = UIConfig(window_width=600, window_height=400)
        analysis_config = AnalysisConfig(analysis_fps=1, vision_fps=2)
        
        ui = create_assist_mode_ui(ui_config, analysis_config)
        print(f"  Main UI created successfully")
        print(f"    UI config: {ui.ui_config is not None}")
        print(f"    Analysis config: {ui.analysis_config is not None}")
        print(f"    Assistant: {ui.assistant is not None}")
        print(f"    Root window: {ui.root is not None}")
        
        # Test widget lists
        print(f"    Recommendation widgets: {len(ui.recommendation_widgets)}")
        print(f"    Build analysis widget: {ui.build_analysis_widget is not None}")
        print(f"    Performance widget: {ui.performance_widget is not None}")
        
        # Test UI state
        print(f"    Running: {ui.running}")
        print(f"    Update thread: {ui.update_thread is not None}")
        
        # Clean up
        ui.root.destroy()
        
    except Exception as e:
        print(f"  Main UI creation error: {e}")
    
    # Test recommendation display logic
    print("\n📋 TESTING RECOMMENDATION DISPLAY:")
    print("-" * 30)
    
    # Create multiple mock recommendations
    recommendations = [
        Recommendation(
            RecommendationType.BUY_ITEM, Priority.CRITICAL, 0.95,
            {"item": "Epic Sword"}, "Critical upgrade needed", 0.8, "Low risk"
        ),
        Recommendation(
            RecommendationType.PLACE_ITEM, Priority.HIGH, 0.87,
            {"item": "Shield", "x": 2, "y": 3}, "Optimal placement for defense", 0.6, "No risk"
        ),
        Recommendation(
            RecommendationType.SELL_ITEM, Priority.MEDIUM, 0.72,
            {"item": "Weak Dagger"}, "Free up space for better items", 0.3, "Low risk"
        ),
        Recommendation(
            RecommendationType.REROLL_SHOP, Priority.LOW, 0.65,
            {"cost": 2}, "Current shop has poor options", 0.4, "Medium risk"
        ),
        Recommendation(
            RecommendationType.STRATEGIC_ADVICE, Priority.LOW, 0.80,
            {"advice": "Focus on synergies"}, "Build coherence improvement", 0.5, "No risk"
        )
    ]
    
    print(f"  Created {len(recommendations)} test recommendations:")
    for i, rec in enumerate(recommendations, 1):
        print(f"    {i}. {rec.recommendation_type.value} ({rec.priority.value}) - {rec.confidence:.1%}")
    
    # Test priority sorting
    sorted_recs = sorted(recommendations, key=lambda r: (
        {"critical": 4, "high": 3, "medium": 2, "low": 1}[r.priority.value],
        r.confidence
    ), reverse=True)
    
    print(f"\n  Priority-sorted recommendations:")
    for i, rec in enumerate(sorted_recs, 1):
        print(f"    {i}. {rec.recommendation_type.value} ({rec.priority.value}) - {rec.confidence:.1%}")
    
    # Test recommendation conversion
    print("\n🔄 TESTING RECOMMENDATION CONVERSION:")
    print("-" * 30)
    
    for rec in recommendations[:2]:  # Test first 2
        rec_dict = rec.to_dict()
        print(f"  {rec.recommendation_type.value} conversion:")
        print(f"    Keys: {list(rec_dict.keys())}")
        print(f"    Type: {rec_dict['type']}")
        print(f"    Priority: {rec_dict['priority']}")
        print(f"    Confidence: {rec_dict['confidence']}")
    
    # Test performance history
    print("\n📊 TESTING PERFORMANCE HISTORY:")
    print("-" * 30)
    
    # Create root for performance widget test
    root = tk.Tk()
    root.withdraw()
    
    try:
        perf_widget = PerformanceWidget(root, default_config)
        
        # Add multiple performance updates
        for i in range(5):
            status = {
                "running": True,
                "performance": {
                    "analyses_per_second": 2.0 + i * 0.2,
                    "average_processing_time": 0.08 + i * 0.01,
                    "success_rate": 0.9 + i * 0.02
                }
            }
            perf_widget.update_performance(status)
        
        history_size = len(perf_widget.performance_history)
        print(f"  Performance history size: {history_size}")
        
        if history_size > 0:
            latest = perf_widget.performance_history[-1]
            print(f"  Latest performance:")
            print(f"    FPS: {latest['fps']:.1f}")
            print(f"    Processing time: {latest['processing_time']:.1f}ms")
            print(f"    Success rate: {latest['success_rate']:.1%}")
    
    finally:
        root.destroy()
    
    # Test configuration edge cases
    print("\n🔍 TESTING CONFIGURATION EDGE CASES:")
    print("-" * 30)
    
    # Minimal config
    minimal_config = UIConfig(
        window_width=400,
        window_height=300,
        max_recommendation_display=1,
        ui_update_interval=1000
    )
    print(f"  Minimal config:")
    print(f"    Window: {minimal_config.window_width}x{minimal_config.window_height}")
    print(f"    Max recommendations: {minimal_config.max_recommendation_display}")
    print(f"    Update interval: {minimal_config.ui_update_interval}ms")
    
    # Large config
    large_config = UIConfig(
        window_width=1920,
        window_height=1080,
        max_recommendation_display=10,
        ui_update_interval=100
    )
    print(f"\n  Large config:")
    print(f"    Window: {large_config.window_width}x{large_config.window_height}")
    print(f"    Max recommendations: {large_config.max_recommendation_display}")
    print(f"    Update interval: {large_config.ui_update_interval}ms")
    
    # Test theme variations
    print("\n🎨 TESTING THEME VARIATIONS:")
    print("-" * 30)
    
    themes = ["dark", "light"]
    for theme in themes:
        theme_config = UIConfig(theme=theme)
        print(f"  {theme.title()} theme:")
        print(f"    Background: {theme_config.colors['bg']}")
        print(f"    Text: {theme_config.colors['fg']}")
        print(f"    Success: {theme_config.colors['success']}")
        print(f"    Warning: {theme_config.colors['warning']}")
        print(f"    Error: {theme_config.colors['error']}")
    
    # Test error handling
    print("\n❌ TESTING ERROR HANDLING:")
    print("-" * 30)
    
    # Test with invalid recommendation
    root = tk.Tk()
    root.withdraw()
    
    try:
        rec_widget = RecommendationWidget(root, default_config)
        
        # Test with None (should not crash)
        rec_widget.update_recommendation(None)
        print(f"  Handled None recommendation successfully")
        
        # Test with invalid recommendation object
        try:
            rec_widget.update_recommendation("invalid")
            print(f"  ERROR: Should have failed with invalid recommendation")
        except:
            print(f"  Correctly rejected invalid recommendation")
    
    finally:
        root.destroy()
    
    print("\n" + "=" * 60)
    print("✅ UI FRAMEWORK TEST COMPLETE")
    print("\nNote: UI components created and tested without displaying windows")
    print("To see the actual UI, run: python assist_mode/ui_framework.py")

if __name__ == "__main__":
    test_ui_framework()
