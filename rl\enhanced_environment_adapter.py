"""
Enhanced Environment Adapter for RL Training

Bridges the new research-compliant simulation systems with the existing
RL environment interface. Provides backward compatibility while enabling
access to the enhanced features.
"""

import numpy as np
from typing import Dict, List, Any, Tuple, Optional
import gymnasium as gym
from gymnasium import spaces

from bbagent.enhanced_game_manager import <PERSON>han<PERSON><PERSON><PERSON><PERSON>ana<PERSON>
from simulator.enhanced_shop import EnhancedShop
from simulator.enhanced_combat import EnhancedCombatSystem
from simulator.status_effects import StatusEffectManager
from simulator.resources import ResourceManager


class EnhancedBackpackBattlesEnv(gym.Env):
    """
    Enhanced Gymnasium environment for Backpack Battles RL training.
    
    Integrates all the new research-compliant systems while maintaining
    compatibility with existing RL training code.
    """
    
    def __init__(self, player_class: str = "Ranger", render_mode: Optional[str] = None):
        super().__init__()
        
        self.player_class = player_class
        self.render_mode = render_mode
        
        # Initialize enhanced game manager
        self.game_manager = EnhancedGameManager(player_class)
        
        # Define action space (expanded for new features)
        # 0-4: Buy shop items (slots 0-4)
        # 5: Reroll shop
        # 6-10: Toggle shop locks (slots 0-4)
        # 11: End shop phase (proceed to battle)
        # 12-15: Use consumable items (if any)
        # 16: Skip action
        self.action_space = spaces.Discrete(17)
        
        # Define observation space (enhanced with new systems)
        self.observation_space = spaces.Dict({
            # Core game state
            "round": spaces.Discrete(19),  # 1-18
            "health": spaces.Box(0, 500, shape=(1,), dtype=np.int32),
            "gold": spaces.Box(0, 200, shape=(1,), dtype=np.int32),
            "lives": spaces.Box(0, 5, shape=(1,), dtype=np.int32),
            "trophies": spaces.Box(0, 10, shape=(1,), dtype=np.int32),
            
            # Shop state (enhanced)
            "shop_items": spaces.Box(0, 1000, shape=(5,), dtype=np.int32),  # Item IDs
            "shop_costs": spaces.Box(0, 50, shape=(5,), dtype=np.int32),
            "shop_locks": spaces.Box(0, 1, shape=(5,), dtype=np.int32),
            "reroll_cost": spaces.Box(1, 2, shape=(1,), dtype=np.int32),
            "sale_indicators": spaces.Box(0, 1, shape=(5,), dtype=np.int32),
            
            # Pity timer information
            "items_since_sale": spaces.Box(0, 20, shape=(1,), dtype=np.int32),
            "items_since_bag": spaces.Box(0, 15, shape=(1,), dtype=np.int32),
            
            # Status effects (new)
            "status_effects": spaces.Box(0, 20, shape=(10,), dtype=np.int32),  # Top 10 effects
            
            # Resource state (new)
            "stamina": spaces.Box(0, 20, shape=(1,), dtype=np.float32),
            "max_stamina": spaces.Box(0, 20, shape=(1,), dtype=np.float32),
            "mana": spaces.Box(0, 10, shape=(1,), dtype=np.int32),
            "max_mana": spaces.Box(0, 10, shape=(1,), dtype=np.int32),
            
            # Inventory state
            "inventory_items": spaces.Box(0, 100, shape=(1,), dtype=np.int32),
            "inventory_value": spaces.Box(0, 1000, shape=(1,), dtype=np.float32),
            
            # Battle results (from last battle)
            "last_battle_won": spaces.Box(0, 1, shape=(1,), dtype=np.int32),
            "last_battle_time": spaces.Box(0, 120, shape=(1,), dtype=np.float32),
            "damage_dealt": spaces.Box(0, 500, shape=(1,), dtype=np.int32),
            "damage_taken": spaces.Box(0, 500, shape=(1,), dtype=np.int32),
        })
        
        # State tracking
        self.current_state = None
        self.episode_rewards = []
        self.episode_length = 0
        self.max_episode_length = 1000  # Prevent infinite episodes
        
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict, Dict]:
        """Reset the environment to initial state"""
        super().reset(seed=seed)
        
        if seed is not None:
            np.random.seed(seed)
        
        # Reset game manager
        self.game_manager = EnhancedGameManager(self.player_class)
        
        # Reset tracking
        self.episode_rewards = []
        self.episode_length = 0
        
        # Get initial observation
        observation = self._get_observation()
        info = self._get_info()
        
        self.current_state = observation
        return observation, info
    
    def step(self, action: int) -> Tuple[Dict, float, bool, bool, Dict]:
        """Execute one step in the environment"""
        self.episode_length += 1
        
        # Execute action
        reward = self._execute_action(action)
        
        # Get new observation
        observation = self._get_observation()
        
        # Check termination conditions
        terminated = self._is_terminated()
        truncated = self.episode_length >= self.max_episode_length
        
        # Get info
        info = self._get_info()
        
        self.current_state = observation
        self.episode_rewards.append(reward)
        
        return observation, reward, terminated, truncated, info
    
    def _execute_action(self, action: int) -> float:
        """Execute the given action and return reward"""
        reward = 0.0
        
        if action <= 4:  # Buy shop item
            success = self.game_manager.buy_shop_item(action)
            if success:
                reward += 0.1  # Small reward for successful purchase
            else:
                reward -= 0.05  # Small penalty for failed purchase
                
        elif action == 5:  # Reroll shop
            success = self.game_manager.reroll_shop()
            if success:
                reward -= 0.02  # Small cost for rerolling
            else:
                reward -= 0.05  # Penalty for failed reroll
                
        elif action <= 10:  # Toggle shop locks (6-10)
            slot = action - 6
            self.game_manager.toggle_shop_lock(slot)
            
        elif action == 11:  # End shop phase
            if self.game_manager.game_state == 'shop_phase':
                self.game_manager.start_battle()
                # Reward based on battle outcome will be calculated after battle
                
        elif action <= 15:  # Use consumable items (12-15)
            # Placeholder for consumable item usage
            pass
            
        elif action == 16:  # Skip action
            pass  # No reward or penalty
        
        # Additional rewards based on game state
        game_state = self.game_manager.get_game_state()
        
        # Reward for winning battles
        if self.game_manager.last_battle_result:
            if self.game_manager.last_battle_result.get("winner") == 0:
                reward += 1.0  # Major reward for winning
            else:
                reward -= 0.5  # Penalty for losing
        
        # Reward for progression
        if game_state["trophies"] > 0:
            reward += game_state["trophies"] * 0.2
        
        # Penalty for losing lives
        if game_state["lives"] < 5:
            reward -= (5 - game_state["lives"]) * 0.3
        
        return reward
    
    def _get_observation(self) -> Dict[str, np.ndarray]:
        """Get current observation"""
        game_state = self.game_manager.get_game_state()
        shop_state = self.game_manager.get_shop_statistics()
        
        # Convert shop items to IDs (simplified)
        shop_item_ids = []
        for item_name in shop_state.get("current_offerings", []):
            if item_name:
                shop_item_ids.append(hash(item_name) % 1000)
            else:
                shop_item_ids.append(0)
        
        # Pad to 5 items
        while len(shop_item_ids) < 5:
            shop_item_ids.append(0)
        
        # Extract status effects (top 10)
        status_effects = list(game_state.get("status_effects", {}).values())[:10]
        while len(status_effects) < 10:
            status_effects.append(0)
        
        # Build observation
        observation = {
            "round": np.array([game_state["round"]], dtype=np.int32),
            "health": np.array([game_state["health"]], dtype=np.int32),
            "gold": np.array([game_state["gold"]], dtype=np.int32),
            "lives": np.array([game_state["lives"]], dtype=np.int32),
            "trophies": np.array([game_state["trophies"]], dtype=np.int32),
            
            "shop_items": np.array(shop_item_ids, dtype=np.int32),
            "shop_costs": np.array([0, 0, 0, 0, 0], dtype=np.int32),  # Placeholder
            "shop_locks": np.array([0, 0, 0, 0, 0], dtype=np.int32),  # Placeholder
            "reroll_cost": np.array([shop_state.get("reroll_cost", 1)], dtype=np.int32),
            "sale_indicators": np.array([0, 0, 0, 0, 0], dtype=np.int32),  # Placeholder
            
            "items_since_sale": np.array([shop_state.get("pity_timers", {}).get("items_since_last_sale", 0)], dtype=np.int32),
            "items_since_bag": np.array([shop_state.get("pity_timers", {}).get("items_since_last_bag", 0)], dtype=np.int32),
            
            "status_effects": np.array(status_effects, dtype=np.int32),
            
            "stamina": np.array([game_state.get("resources", {}).get("stamina", {}).get("current", 10.0)], dtype=np.float32),
            "max_stamina": np.array([game_state.get("resources", {}).get("stamina", {}).get("maximum", 10.0)], dtype=np.float32),
            "mana": np.array([game_state.get("resources", {}).get("mana", {}).get("current", 0)], dtype=np.int32),
            "max_mana": np.array([game_state.get("resources", {}).get("mana", {}).get("maximum", 0)], dtype=np.int32),
            
            "inventory_items": np.array([game_state["inventory_items"]], dtype=np.int32),
            "inventory_value": np.array([0.0], dtype=np.float32),  # Placeholder
            
            "last_battle_won": np.array([1 if game_state.get("last_battle_result", {}).get("winner") == 0 else 0], dtype=np.int32),
            "last_battle_time": np.array([game_state.get("last_battle_result", {}).get("battle_time", 0.0)], dtype=np.float32),
            "damage_dealt": np.array([0], dtype=np.int32),  # Placeholder
            "damage_taken": np.array([0], dtype=np.int32),  # Placeholder
        }
        
        return observation
    
    def _is_terminated(self) -> bool:
        """Check if episode should terminate"""
        game_state = self.game_manager.get_game_state()
        
        # Game ends when lives reach 0 or trophies reach 10
        return game_state["lives"] <= 0 or game_state["trophies"] >= 10 or game_state["round"] > 18
    
    def _get_info(self) -> Dict[str, Any]:
        """Get additional info"""
        game_state = self.game_manager.get_game_state()
        
        return {
            "episode_length": self.episode_length,
            "total_reward": sum(self.episode_rewards),
            "game_phase": game_state["game_phase"],
            "round": game_state["round"],
            "performance_stats": {
                "battles_simulated": 1 if self.game_manager.last_battle_result else 0,
                "shop_rerolls": game_state.get("shop_state", {}).get("rerolls_this_round", 0)
            }
        }
    
    def render(self):
        """Render the environment (optional)"""
        if self.render_mode == "human":
            game_state = self.game_manager.get_game_state()
            print(f"Round {game_state['round']}: {game_state['health']} HP, {game_state['gold']} Gold, "
                  f"{game_state['lives']} Lives, {game_state['trophies']} Trophies")
    
    def close(self):
        """Clean up resources"""
        pass


def create_enhanced_env(player_class: str = "Ranger", render_mode: Optional[str] = None) -> EnhancedBackpackBattlesEnv:
    """Factory function to create enhanced environment"""
    return EnhancedBackpackBattlesEnv(player_class=player_class, render_mode=render_mode)


# Register the environment with Gymnasium
gym.register(
    id='BackpackBattles-Enhanced-v1',
    entry_point='rl.enhanced_environment_adapter:EnhancedBackpackBattlesEnv',
    max_episode_steps=1000,
)
