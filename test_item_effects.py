"""
Test the Item Effect Parsing System
"""

from simulator.item_effects import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Effect<PERSON><PERSON><PERSON>

def test_item_effect_parsing():
    parser = ItemEffectParser()
    
    # Test cases from the database
    test_effects = [
        "Start of battle: gain 3 💪 random buffs. 🧪 Potion consumed: 70% chance to repeat its effect After 2.5s ⏰.",
        "10 -damage dealt: inflict 1 ☠️ random debuff. item activates: 30% chance to deal 5 ❄️ damage.",
        "Start of battle: The item triggers 100% faster for 1s. Buff used: Refund 25% of the used buffs.",
        "6 item activations: Heal for 20 and inflict 6 for 3s.",
        "On attack: Use 3 to gain 2, and during Battle Rage also gain 2.",
        "Game started: Replace this with random starting bags and items."
    ]
    
    print("🧪 TESTING ITEM EFFECT PARSING SYSTEM")
    print("=" * 60)
    
    for i, effect_text in enumerate(test_effects, 1):
        print(f"\nTest {i}: {effect_text}")
        print("-" * 40)
        
        parsed_effects = parser.parse_effect_text(effect_text)
        
        for j, effect in enumerate(parsed_effects):
            print(f"  Effect {j+1}:")
            print(f"    Trigger: {effect.trigger.value}")
            print(f"    Condition: {effect.condition}")
            print(f"    Type: {effect.effect_type}")
            print(f"    Magnitude: {effect.magnitude}")
            print(f"    Target: {effect.target}")
            print(f"    Duration: {effect.duration}")
            print(f"    Probability: {effect.probability}")
            print(f"    Raw: {effect.raw_text}")
    
    print("\n" + "=" * 60)
    print("✅ ITEM EFFECT PARSING TEST COMPLETE")

if __name__ == "__main__":
    test_item_effect_parsing()
