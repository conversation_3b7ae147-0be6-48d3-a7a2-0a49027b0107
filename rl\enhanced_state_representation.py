"""
Enhanced State Representation for RL Training

Creates detailed game state encoding that includes:
- Complete backpack grid representation
- Item synergy and adjacency information
- Strategic features and game progression
- Normalized numerical features for neural networks
- Hierarchical state encoding for different RL architectures
"""

from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
import numpy as np
from enum import Enum

from simulator.enhanced_item_database import EnhancedItem, ItemType, ItemRarity, ItemClass
from simulator.character_classes import CharacterClass


class StateFeatureType(Enum):
    GRID = "grid"
    NUMERICAL = "numerical"
    CATEGORICAL = "categorical"
    SEQUENCE = "sequence"


@dataclass
class StateFeature:
    """Represents a single feature in the state representation"""
    name: str
    feature_type: StateFeatureType
    shape: Tuple[int, ...]
    description: str
    normalization: Optional[str] = None  # "minmax", "standard", "none"


class EnhancedStateEncoder:
    """Encodes game state into RL-friendly representations"""
    
    def __init__(self, grid_width: int = 9, grid_height: int = 7):
        self.grid_width = grid_width
        self.grid_height = grid_height
        self.item_type_mapping = self._create_item_type_mapping()
        self.rarity_mapping = self._create_rarity_mapping()
        self.class_mapping = self._create_class_mapping()
        self.features = self._define_features()
    
    def _create_item_type_mapping(self) -> Dict[str, int]:
        """Create mapping from item types to integers"""
        mapping = {"empty": 0}
        for i, item_type in enumerate(ItemType, 1):
            mapping[item_type.value.lower()] = i
        return mapping
    
    def _create_rarity_mapping(self) -> Dict[str, int]:
        """Create mapping from rarities to integers"""
        mapping = {}
        for i, rarity in enumerate(ItemRarity):
            mapping[rarity.value.lower()] = i
        return mapping
    
    def _create_class_mapping(self) -> Dict[str, int]:
        """Create mapping from classes to integers"""
        mapping = {}
        for i, char_class in enumerate(CharacterClass):
            mapping[char_class.value.lower()] = i
        return mapping
    
    def _define_features(self) -> Dict[str, StateFeature]:
        """Define all state features"""
        return {
            # Grid-based features
            "item_grid": StateFeature(
                name="item_grid",
                feature_type=StateFeatureType.GRID,
                shape=(self.grid_height, self.grid_width),
                description="Grid showing item types at each position"
            ),
            "rarity_grid": StateFeature(
                name="rarity_grid", 
                feature_type=StateFeatureType.GRID,
                shape=(self.grid_height, self.grid_width),
                description="Grid showing item rarities"
            ),
            "synergy_grid": StateFeature(
                name="synergy_grid",
                feature_type=StateFeatureType.GRID,
                shape=(self.grid_height, self.grid_width),
                description="Grid showing synergy strength at each position"
            ),
            "adjacency_grid": StateFeature(
                name="adjacency_grid",
                feature_type=StateFeatureType.GRID,
                shape=(self.grid_height, self.grid_width),
                description="Grid showing number of adjacent items"
            ),
            
            # Numerical features
            "player_stats": StateFeature(
                name="player_stats",
                feature_type=StateFeatureType.NUMERICAL,
                shape=(10,),
                description="Player stats: health, gold, lives, round, etc.",
                normalization="minmax"
            ),
            "build_metrics": StateFeature(
                name="build_metrics",
                feature_type=StateFeatureType.NUMERICAL,
                shape=(15,),
                description="Build quality metrics: synergies, power, diversity",
                normalization="standard"
            ),
            "resource_state": StateFeature(
                name="resource_state",
                feature_type=StateFeatureType.NUMERICAL,
                shape=(8,),
                description="Resource management: stamina, mana, status effects",
                normalization="minmax"
            ),
            
            # Shop state
            "shop_items": StateFeature(
                name="shop_items",
                feature_type=StateFeatureType.SEQUENCE,
                shape=(5, 8),  # 5 shop slots, 8 features per item
                description="Shop items with type, rarity, cost, etc."
            ),
            
            # Strategic features
            "game_phase": StateFeature(
                name="game_phase",
                feature_type=StateFeatureType.CATEGORICAL,
                shape=(4,),  # One-hot: shop, battle, combination, end
                description="Current game phase"
            ),
            "character_info": StateFeature(
                name="character_info",
                feature_type=StateFeatureType.CATEGORICAL,
                shape=(8,),  # Class + subclass info
                description="Character class and subclass information"
            )
        }
    
    def encode_state(self, game_state: Dict[str, Any]) -> Dict[str, np.ndarray]:
        """Encode complete game state into feature arrays"""
        encoded = {}
        
        # Encode grid-based features
        encoded["item_grid"] = self._encode_item_grid(game_state)
        encoded["rarity_grid"] = self._encode_rarity_grid(game_state)
        encoded["synergy_grid"] = self._encode_synergy_grid(game_state)
        encoded["adjacency_grid"] = self._encode_adjacency_grid(game_state)
        
        # Encode numerical features
        encoded["player_stats"] = self._encode_player_stats(game_state)
        encoded["build_metrics"] = self._encode_build_metrics(game_state)
        encoded["resource_state"] = self._encode_resource_state(game_state)
        
        # Encode shop state
        encoded["shop_items"] = self._encode_shop_items(game_state)
        
        # Encode categorical features
        encoded["game_phase"] = self._encode_game_phase(game_state)
        encoded["character_info"] = self._encode_character_info(game_state)
        
        return encoded
    
    def _encode_item_grid(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode item types on the grid"""
        grid = np.zeros((self.grid_height, self.grid_width), dtype=np.int32)
        
        backpack_items = game_state.get("backpack_items", [])
        
        for item in backpack_items:
            position = item.get("position", (0, 0))
            rotation = item.get("rotation", 0)
            shape = item.get("shape", [[True]])
            item_type = item.get("type", "weapon")
            
            # Get rotated shape
            rotated_shape = self._rotate_shape(shape, rotation)
            type_id = self.item_type_mapping.get(item_type.lower(), 1)
            
            # Fill grid
            for dy, row in enumerate(rotated_shape):
                for dx, cell in enumerate(row):
                    if cell:
                        y, x = position[1] + dy, position[0] + dx
                        if 0 <= y < self.grid_height and 0 <= x < self.grid_width:
                            grid[y, x] = type_id
        
        return grid
    
    def _encode_rarity_grid(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode item rarities on the grid"""
        grid = np.zeros((self.grid_height, self.grid_width), dtype=np.int32)
        
        backpack_items = game_state.get("backpack_items", [])
        
        for item in backpack_items:
            position = item.get("position", (0, 0))
            rotation = item.get("rotation", 0)
            shape = item.get("shape", [[True]])
            rarity = item.get("rarity", "common")
            
            rotated_shape = self._rotate_shape(shape, rotation)
            rarity_id = self.rarity_mapping.get(rarity.lower(), 0)
            
            for dy, row in enumerate(rotated_shape):
                for dx, cell in enumerate(row):
                    if cell:
                        y, x = position[1] + dy, position[0] + dx
                        if 0 <= y < self.grid_height and 0 <= x < self.grid_width:
                            grid[y, x] = rarity_id
        
        return grid
    
    def _encode_synergy_grid(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode synergy strength at each grid position"""
        grid = np.zeros((self.grid_height, self.grid_width), dtype=np.float32)
        
        backpack_items = game_state.get("backpack_items", [])
        
        # Calculate synergy for each position
        for y in range(self.grid_height):
            for x in range(self.grid_width):
                synergy_score = self._calculate_position_synergy(x, y, backpack_items)
                grid[y, x] = synergy_score
        
        return grid
    
    def _encode_adjacency_grid(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode number of adjacent items at each position"""
        grid = np.zeros((self.grid_height, self.grid_width), dtype=np.int32)
        
        occupied_cells = game_state.get("occupied_cells", set())
        
        for y in range(self.grid_height):
            for x in range(self.grid_width):
                adjacent_count = 0
                for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
                    adj_x, adj_y = x + dx, y + dy
                    if (adj_x, adj_y) in occupied_cells:
                        adjacent_count += 1
                grid[y, x] = adjacent_count
        
        return grid
    
    def _encode_player_stats(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode player statistics"""
        stats = np.zeros(10, dtype=np.float32)
        
        # Basic stats (normalized to 0-1)
        stats[0] = min(1.0, game_state.get("health", 100) / 100.0)
        stats[1] = min(1.0, game_state.get("gold", 0) / 100.0)  # Normalize to reasonable max
        stats[2] = min(1.0, game_state.get("lives", 3) / 3.0)
        stats[3] = min(1.0, game_state.get("round", 1) / 8.0)  # Max round 8
        stats[4] = min(1.0, game_state.get("trophies", 0) / 1000.0)  # Normalize trophies
        
        # Inventory and backpack info
        stats[5] = min(1.0, len(game_state.get("inventory_items", [])) / 10.0)
        stats[6] = min(1.0, len(game_state.get("backpack_items", [])) / 20.0)
        
        # Shop info
        stats[7] = min(1.0, game_state.get("rerolls_used", 0) / 10.0)
        stats[8] = min(1.0, game_state.get("reserves_used", 0) / 5.0)
        
        # Turn/phase info
        stats[9] = 1.0 if game_state.get("phase") == "shop" else 0.0
        
        return stats
    
    def _encode_build_metrics(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode build quality and synergy metrics"""
        metrics = np.zeros(15, dtype=np.float32)
        
        backpack_items = game_state.get("backpack_items", [])
        
        if not backpack_items:
            return metrics
        
        # Item count and diversity
        metrics[0] = len(backpack_items) / 20.0  # Normalize to max items
        
        # Type diversity
        types_present = set(item.get("type", "weapon") for item in backpack_items)
        metrics[1] = len(types_present) / len(self.item_type_mapping)
        
        # Rarity distribution
        rarities = [item.get("rarity", "common") for item in backpack_items]
        rarity_scores = [self.rarity_mapping.get(r.lower(), 0) for r in rarities]
        metrics[2] = np.mean(rarity_scores) / max(self.rarity_mapping.values())
        
        # Synergy metrics
        total_synergies = self._count_total_synergies(backpack_items)
        metrics[3] = min(1.0, total_synergies / 20.0)  # Normalize synergies
        
        # Power level estimate
        power_levels = [item.get("power_level", 1.0) for item in backpack_items]
        metrics[4] = min(1.0, np.mean(power_levels) / 5.0)  # Normalize power
        
        # Adjacency utilization
        adjacency_score = self._calculate_adjacency_utilization(game_state)
        metrics[5] = adjacency_score
        
        # Class synergy
        character_class = game_state.get("character_class", "neutral")
        class_synergy = self._calculate_class_synergy(backpack_items, character_class)
        metrics[6] = class_synergy
        
        # Cost efficiency
        total_cost = sum(item.get("cost", 0) for item in backpack_items)
        gold_spent = game_state.get("gold_spent", 0)
        metrics[7] = min(1.0, total_cost / max(gold_spent, 1))
        
        # Combination potential
        combination_potential = self._calculate_combination_potential(backpack_items)
        metrics[8] = combination_potential
        
        # Defensive vs offensive balance
        defensive_items = sum(1 for item in backpack_items 
                            if item.get("type", "").lower() in ["armor", "shield", "helmet"])
        offensive_items = len(backpack_items) - defensive_items
        metrics[9] = defensive_items / max(len(backpack_items), 1)
        metrics[10] = offensive_items / max(len(backpack_items), 1)
        
        # Gemstone utilization
        socketed_gems = sum(item.get("socketed_gems", 0) for item in backpack_items)
        available_sockets = sum(item.get("sockets", 0) for item in backpack_items)
        metrics[11] = socketed_gems / max(available_sockets, 1)
        
        # Build completion (how close to optimal)
        completion_score = self._estimate_build_completion(backpack_items, game_state)
        metrics[12] = completion_score
        
        # Flexibility score (ability to adapt)
        flexibility = self._calculate_build_flexibility(backpack_items)
        metrics[13] = flexibility
        
        # Meta alignment (how well build follows current meta)
        meta_score = self._calculate_meta_alignment(backpack_items, game_state)
        metrics[14] = meta_score
        
        return metrics
    
    def _encode_resource_state(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode resource management state"""
        resources = np.zeros(8, dtype=np.float32)
        
        # Stamina and mana (normalized to 0-1)
        resources[0] = min(1.0, game_state.get("stamina", 100) / 100.0)
        resources[1] = min(1.0, game_state.get("max_stamina", 100) / 100.0)
        resources[2] = min(1.0, game_state.get("mana", 0) / 50.0)  # Typical max mana
        resources[3] = min(1.0, game_state.get("max_mana", 0) / 50.0)
        
        # Status effect counts
        status_effects = game_state.get("status_effects", {})
        positive_effects = sum(stacks for effect, stacks in status_effects.items() 
                             if effect in ["Empower", "Luck", "Regeneration", "Vampirism"])
        negative_effects = sum(stacks for effect, stacks in status_effects.items()
                             if effect in ["Poison", "Blind", "Cold", "Stun"])
        
        resources[4] = min(1.0, positive_effects / 20.0)
        resources[5] = min(1.0, negative_effects / 20.0)
        
        # Battle state
        resources[6] = 1.0 if game_state.get("in_battle", False) else 0.0
        resources[7] = min(1.0, game_state.get("battle_time", 0) / 60.0)  # Max 60s battles
        
        return resources
    
    def _encode_shop_items(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode shop items"""
        shop_array = np.zeros((5, 8), dtype=np.float32)
        
        shop_items = game_state.get("shop_items", [])
        
        for i, item in enumerate(shop_items[:5]):
            if item:
                # Item type
                item_type = item.get("type", "weapon")
                shop_array[i, 0] = self.item_type_mapping.get(item_type.lower(), 0) / len(self.item_type_mapping)
                
                # Rarity
                rarity = item.get("rarity", "common")
                shop_array[i, 1] = self.rarity_mapping.get(rarity.lower(), 0) / len(self.rarity_mapping)
                
                # Cost (normalized)
                shop_array[i, 2] = min(1.0, item.get("cost", 0) / 50.0)
                
                # Power level
                shop_array[i, 3] = min(1.0, item.get("power_level", 1.0) / 5.0)
                
                # Locked status
                shop_array[i, 4] = 1.0 if item.get("locked", False) else 0.0
                
                # Reserved status
                shop_array[i, 5] = 1.0 if item.get("reserved", False) else 0.0
                
                # Synergy with current build
                synergy_score = self._calculate_item_build_synergy(item, game_state)
                shop_array[i, 6] = synergy_score
                
                # Affordability
                player_gold = game_state.get("gold", 0)
                shop_array[i, 7] = 1.0 if player_gold >= item.get("cost", 0) else 0.0
        
        return shop_array
    
    def _encode_game_phase(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode current game phase as one-hot"""
        phase_vector = np.zeros(4, dtype=np.float32)
        
        phase = game_state.get("phase", "shop")
        phase_mapping = {"shop": 0, "battle": 1, "combination": 2, "end": 3}
        
        phase_idx = phase_mapping.get(phase, 0)
        phase_vector[phase_idx] = 1.0
        
        return phase_vector
    
    def _encode_character_info(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Encode character class and subclass information"""
        char_info = np.zeros(8, dtype=np.float32)
        
        # Character class (one-hot)
        character_class = game_state.get("character_class", "neutral")
        class_idx = self.class_mapping.get(character_class.lower(), 0)
        if class_idx < 4:  # Ensure within bounds
            char_info[class_idx] = 1.0
        
        # Subclass selected
        char_info[4] = 1.0 if game_state.get("subclass") else 0.0
        
        # Starting bag effects
        starting_bag = game_state.get("starting_bag", "")
        char_info[5] = 1.0 if starting_bag else 0.0
        
        # Class synergy strength
        class_synergy = self._calculate_class_synergy(
            game_state.get("backpack_items", []), character_class
        )
        char_info[6] = class_synergy
        
        # Round 8+ indicator (subclass available)
        char_info[7] = 1.0 if game_state.get("round", 1) >= 8 else 0.0
        
        return char_info
    
    def get_flattened_state(self, game_state: Dict[str, Any]) -> np.ndarray:
        """Get complete state as a single flattened vector"""
        encoded = self.encode_state(game_state)
        
        flattened_parts = []
        for feature_name in sorted(encoded.keys()):
            flattened_parts.append(encoded[feature_name].flatten())
        
        return np.concatenate(flattened_parts)
    
    def get_state_size(self) -> int:
        """Get total size of flattened state vector"""
        total_size = 0
        for feature in self.features.values():
            feature_size = np.prod(feature.shape)
            total_size += feature_size
        return total_size
    
    # Helper methods
    def _rotate_shape(self, shape: List[List[bool]], rotation: int) -> List[List[bool]]:
        """Rotate item shape"""
        if rotation == 0:
            return shape
        elif rotation == 1:  # 90 degrees
            return [[shape[len(shape) - 1 - j][i] for j in range(len(shape))] 
                   for i in range(len(shape[0]))]
        elif rotation == 2:  # 180 degrees
            return [[shape[len(shape) - 1 - i][len(shape[0]) - 1 - j] 
                    for j in range(len(shape[0]))] for i in range(len(shape))]
        elif rotation == 3:  # 270 degrees
            return [[shape[j][len(shape[0]) - 1 - i] for j in range(len(shape))] 
                   for i in range(len(shape[0]) - 1, -1, -1)]
        else:
            return shape
    
    def _calculate_position_synergy(self, x: int, y: int, items: List[Dict[str, Any]]) -> float:
        """Calculate synergy strength at a specific position"""
        synergy_score = 0.0
        
        # Find items adjacent to this position
        adjacent_positions = [(x-1, y), (x+1, y), (x, y-1), (x, y+1)]
        
        for item in items:
            item_pos = item.get("position", (0, 0))
            if item_pos in adjacent_positions:
                # Add synergy based on item tags/type
                synergy_score += 0.1  # Base synergy
                
                # Bonus for matching tags
                item_tags = set(item.get("tags", []))
                if len(item_tags) > 0:
                    synergy_score += 0.05 * len(item_tags)
        
        return min(1.0, synergy_score)
    
    def _count_total_synergies(self, items: List[Dict[str, Any]]) -> int:
        """Count total synergies between items"""
        synergy_count = 0
        
        for i, item1 in enumerate(items):
            for item2 in items[i+1:]:
                tags1 = set(item1.get("tags", []))
                tags2 = set(item2.get("tags", []))
                
                if len(tags1.intersection(tags2)) > 0:
                    synergy_count += 1
        
        return synergy_count
    
    def _calculate_adjacency_utilization(self, game_state: Dict[str, Any]) -> float:
        """Calculate how well adjacency is utilized"""
        occupied_cells = game_state.get("occupied_cells", set())
        
        if not occupied_cells:
            return 0.0
        
        adjacent_pairs = 0
        total_possible = 0
        
        for x, y in occupied_cells:
            for dx, dy in [(0, 1), (1, 0)]:  # Only check right and down to avoid double counting
                adj_x, adj_y = x + dx, y + dy
                total_possible += 1
                if (adj_x, adj_y) in occupied_cells:
                    adjacent_pairs += 1
        
        return adjacent_pairs / max(total_possible, 1)
    
    def _calculate_class_synergy(self, items: List[Dict[str, Any]], character_class: str) -> float:
        """Calculate synergy with character class"""
        if not items:
            return 0.0
        
        class_items = sum(1 for item in items 
                         if item.get("class", "neutral").lower() == character_class.lower())
        
        return class_items / len(items)
    
    def _calculate_combination_potential(self, items: List[Dict[str, Any]]) -> float:
        """Calculate potential for item combinations"""
        # Simplified: count items that could potentially combine
        combinable_items = sum(1 for item in items 
                             if not item.get("combination_locked", False))
        
        return combinable_items / max(len(items), 1)
    
    def _estimate_build_completion(self, items: List[Dict[str, Any]], game_state: Dict[str, Any]) -> float:
        """Estimate how complete the build is"""
        round_num = game_state.get("round", 1)
        expected_items = min(round_num * 2, 15)  # Rough estimate
        
        actual_items = len(items)
        completion = actual_items / max(expected_items, 1)
        
        return min(1.0, completion)
    
    def _calculate_build_flexibility(self, items: List[Dict[str, Any]]) -> float:
        """Calculate build flexibility (ability to adapt)"""
        # Count different item types
        types_present = set(item.get("type", "weapon") for item in items)
        flexibility = len(types_present) / max(len(items), 1)
        
        return min(1.0, flexibility)
    
    def _calculate_meta_alignment(self, items: List[Dict[str, Any]], game_state: Dict[str, Any]) -> float:
        """Calculate alignment with current meta"""
        # Simplified meta score based on item power levels
        if not items:
            return 0.0
        
        power_levels = [item.get("power_level", 1.0) for item in items]
        avg_power = np.mean(power_levels)
        
        # Normalize to 0-1 range
        return min(1.0, avg_power / 3.0)
    
    def _calculate_item_build_synergy(self, item: Dict[str, Any], game_state: Dict[str, Any]) -> float:
        """Calculate how well an item synergizes with current build"""
        backpack_items = game_state.get("backpack_items", [])
        
        if not backpack_items:
            return 0.5  # Neutral if no items
        
        item_tags = set(item.get("tags", []))
        synergy_score = 0.0
        
        for bp_item in backpack_items:
            bp_tags = set(bp_item.get("tags", []))
            common_tags = len(item_tags.intersection(bp_tags))
            synergy_score += common_tags * 0.1
        
        return min(1.0, synergy_score)
