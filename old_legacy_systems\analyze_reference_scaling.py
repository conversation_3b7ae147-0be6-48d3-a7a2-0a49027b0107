"""
Analyze reference images to determine optimal scaling factors for synthetic dataset generation.
This script will help us measure actual item sizes in the reference screenshots.
"""

import cv2
import numpy as np
from PIL import Image
import os

def analyze_reference_image(image_path):
    """Analyze a reference image to understand item scaling."""
    print(f"\nAnalyzing {image_path}...")
    
    # Load image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Could not load {image_path}")
        return
    
    height, width = img.shape[:2]
    print(f"Image dimensions: {width}x{height}")
    
    # Define the inventory region based on calibrated coordinates
    # From calibrated_grid_coordinates.py: INVENTORY_ROI = (114, 80, 1129, 833)
    roi_x1, roi_y1, roi_x2, roi_y2 = 114, 80, 1129, 833
    
    # Extract inventory region
    inventory_region = img[roi_y1:roi_y2, roi_x1:roi_x2]
    
    # Calculate grid cell dimensions
    grid_width = roi_x2 - roi_x1  # 1015 pixels
    grid_height = roi_y2 - roi_y1  # 753 pixels
    cell_width = grid_width / 9  # ~112.8 pixels per cell
    cell_height = grid_height / 7  # ~107.6 pixels per cell
    
    print(f"Grid dimensions: {grid_width}x{grid_height}")
    print(f"Cell dimensions: {cell_width:.1f}x{cell_height:.1f}")
    
    # Save the inventory region for manual inspection
    output_path = image_path.replace('.png', '_inventory_region.png')
    cv2.imwrite(output_path, inventory_region)
    print(f"Saved inventory region to: {output_path}")
    
    return {
        'cell_width': cell_width,
        'cell_height': cell_height,
        'grid_width': grid_width,
        'grid_height': grid_height
    }

def main():
    reference_dir = "data/item_scale_reference"
    
    if not os.path.exists(reference_dir):
        print(f"Reference directory not found: {reference_dir}")
        return
    
    # Analyze all reference images
    results = []
    for filename in os.listdir(reference_dir):
        if filename.endswith('.png'):
            image_path = os.path.join(reference_dir, filename)
            result = analyze_reference_image(image_path)
            if result:
                results.append(result)
    
    if results:
        # Calculate average cell dimensions
        avg_cell_width = sum(r['cell_width'] for r in results) / len(results)
        avg_cell_height = sum(r['cell_height'] for r in results) / len(results)
        
        print(f"\n=== ANALYSIS SUMMARY ===")
        print(f"Average cell width: {avg_cell_width:.1f} pixels")
        print(f"Average cell height: {avg_cell_height:.1f} pixels")
        print(f"Recommended padding factor: 0.92-0.95 (currently using 0.85)")
        print(f"Current calibrated cell size: 102x101 pixels")
        print(f"Difference: {avg_cell_width - 102:.1f}x{avg_cell_height - 101:.1f} pixels")

if __name__ == "__main__":
    main()
