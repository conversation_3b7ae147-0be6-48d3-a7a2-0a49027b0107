"""
Real-time Analysis Pipeline for Assist Mode

Implements continuous game state analysis and strategic recommendations by:
- Integrating vision system for real-time capture
- Processing game state through recommendation engine
- Providing continuous strategic analysis
- Managing analysis pipeline performance
- Delivering real-time recommendations to UI
"""

from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
import numpy as np
import time
import threading
import queue
from collections import deque
import json

from assist_mode.vision_system_integration import VisionSystemIntegrator, VisionConfig, GameStateCapture
from assist_mode.build_recommendation_engine import RecommendationEngine, Recommendation, BuildAnalysis
from simulator.enhanced_item_database import EnhancedItemDatabase


@dataclass
class AnalysisConfig:
    """Configuration for real-time analysis pipeline"""
    # Analysis frequency
    analysis_fps: int = 2  # Analyses per second
    vision_fps: int = 5    # Vision captures per second
    
    # Performance settings
    max_queue_size: int = 10
    analysis_timeout: float = 1.0  # Max time per analysis
    
    # Recommendation settings
    max_recommendations: int = 5
    recommendation_threshold: float = 0.6  # Minimum confidence
    
    # State tracking
    state_history_size: int = 20
    change_detection_threshold: float = 0.1
    
    # Debug and monitoring
    enable_performance_monitoring: bool = True
    enable_debug_logging: bool = False
    log_file_path: Optional[str] = None


@dataclass
class AnalysisResult:
    """Result of a single analysis cycle"""
    timestamp: float
    game_state_capture: Optional[GameStateCapture]
    build_analysis: Optional[BuildAnalysis]
    recommendations: List[Recommendation]
    processing_time: float
    confidence_score: float
    state_changed: bool
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "timestamp": self.timestamp,
            "has_game_state": self.game_state_capture is not None,
            "has_build_analysis": self.build_analysis is not None,
            "num_recommendations": len(self.recommendations),
            "recommendations": [rec.to_dict() for rec in self.recommendations],
            "processing_time": self.processing_time,
            "confidence_score": self.confidence_score,
            "state_changed": self.state_changed
        }


@dataclass
class PerformanceMetrics:
    """Performance monitoring metrics"""
    total_analyses: int = 0
    successful_analyses: int = 0
    failed_analyses: int = 0
    average_processing_time: float = 0.0
    max_processing_time: float = 0.0
    min_processing_time: float = float('inf')
    analyses_per_second: float = 0.0
    
    def update(self, processing_time: float, success: bool):
        """Update metrics with new analysis result"""
        self.total_analyses += 1
        
        if success:
            self.successful_analyses += 1
        else:
            self.failed_analyses += 1
        
        # Update timing metrics
        if processing_time > 0:
            self.max_processing_time = max(self.max_processing_time, processing_time)
            self.min_processing_time = min(self.min_processing_time, processing_time)
            
            # Update average (exponential moving average)
            alpha = 0.1
            if self.average_processing_time == 0:
                self.average_processing_time = processing_time
            else:
                self.average_processing_time = (alpha * processing_time + 
                                              (1 - alpha) * self.average_processing_time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "total_analyses": self.total_analyses,
            "successful_analyses": self.successful_analyses,
            "failed_analyses": self.failed_analyses,
            "success_rate": self.successful_analyses / max(self.total_analyses, 1),
            "average_processing_time": self.average_processing_time,
            "max_processing_time": self.max_processing_time,
            "min_processing_time": self.min_processing_time if self.min_processing_time != float('inf') else 0,
            "analyses_per_second": self.analyses_per_second
        }


class StateChangeDetector:
    """Detects significant changes in game state"""
    
    def __init__(self, config: AnalysisConfig):
        self.config = config
        self.previous_states = deque(maxlen=config.state_history_size)
    
    def detect_change(self, current_state: Optional[GameStateCapture]) -> bool:
        """Detect if current state represents a significant change"""
        if not current_state:
            return False
        
        if not self.previous_states:
            self.previous_states.append(current_state)
            return True
        
        # Compare with most recent state
        previous_state = self.previous_states[-1]
        
        # Check for significant changes
        change_detected = False
        
        # Item count changes
        prev_items = len(previous_state.backpack_items)
        curr_items = len(current_state.backpack_items)
        if abs(prev_items - curr_items) > 0:
            change_detected = True
        
        # Shop changes
        prev_shop = sum(1 for item in previous_state.shop_items if item is not None)
        curr_shop = sum(1 for item in current_state.shop_items if item is not None)
        if abs(prev_shop - curr_shop) > 0:
            change_detected = True
        
        # Phase changes
        if previous_state.game_phase != current_state.game_phase:
            change_detected = True
        
        # Player stats changes (significant)
        prev_stats = previous_state.player_stats
        curr_stats = current_state.player_stats
        
        for key in ["health", "gold", "round", "lives"]:
            prev_val = prev_stats.get(key, 0)
            curr_val = curr_stats.get(key, 0)
            
            if abs(prev_val - curr_val) > self.config.change_detection_threshold * max(prev_val, 1):
                change_detected = True
                break
        
        # Store current state
        self.previous_states.append(current_state)
        
        return change_detected


class AnalysisPipeline:
    """Main real-time analysis pipeline"""
    
    def __init__(self, config: AnalysisConfig):
        self.config = config
        
        # Initialize components
        vision_config = VisionConfig(
            capture_fps=config.vision_fps,
            debug_mode=config.enable_debug_logging
        )
        self.vision_system = VisionSystemIntegrator(vision_config)
        self.recommendation_engine = RecommendationEngine()
        self.change_detector = StateChangeDetector(config)
        
        # Pipeline state
        self.running = False
        self.analysis_thread = None
        self.result_queue = queue.Queue(maxsize=config.max_queue_size)
        
        # Performance monitoring
        self.performance_metrics = PerformanceMetrics()
        self.last_analysis_time = 0
        
        # Callbacks
        self.result_callbacks: List[Callable[[AnalysisResult], None]] = []
        
        # Logging
        self.log_file = None
        if config.log_file_path:
            self.log_file = open(config.log_file_path, 'a')
    
    def add_result_callback(self, callback: Callable[[AnalysisResult], None]):
        """Add callback to receive analysis results"""
        self.result_callbacks.append(callback)
    
    def start_analysis(self):
        """Start the real-time analysis pipeline"""
        if self.running:
            return
        
        self.running = True
        
        # Start vision system
        self.vision_system.start_real_time_capture()
        
        # Start analysis thread
        self.analysis_thread = threading.Thread(target=self._analysis_loop)
        self.analysis_thread.daemon = True
        self.analysis_thread.start()
        
        print("Real-time analysis pipeline started")
    
    def stop_analysis(self):
        """Stop the real-time analysis pipeline"""
        if not self.running:
            return
        
        self.running = False
        
        # Stop vision system
        self.vision_system.stop_real_time_capture()
        
        # Wait for analysis thread
        if self.analysis_thread:
            self.analysis_thread.join(timeout=2.0)
        
        # Close log file
        if self.log_file:
            self.log_file.close()
            self.log_file = None
        
        print("Real-time analysis pipeline stopped")
    
    def _analysis_loop(self):
        """Main analysis loop"""
        analysis_interval = 1.0 / self.config.analysis_fps
        
        while self.running:
            loop_start = time.time()
            
            try:
                # Perform analysis
                result = self._perform_analysis()
                
                # Update performance metrics
                processing_time = result.processing_time if result else 0
                self.performance_metrics.update(processing_time, result is not None)
                
                # Calculate analyses per second
                current_time = time.time()
                if self.last_analysis_time > 0:
                    time_diff = current_time - self.last_analysis_time
                    if time_diff > 0:
                        self.performance_metrics.analyses_per_second = 1.0 / time_diff
                self.last_analysis_time = current_time
                
                # Queue result
                if result:
                    try:
                        self.result_queue.put_nowait(result)
                    except queue.Full:
                        # Remove oldest result and add new one
                        try:
                            self.result_queue.get_nowait()
                            self.result_queue.put_nowait(result)
                        except queue.Empty:
                            pass
                    
                    # Call callbacks
                    for callback in self.result_callbacks:
                        try:
                            callback(result)
                        except Exception as e:
                            if self.config.enable_debug_logging:
                                print(f"Callback error: {e}")
                    
                    # Log result
                    if self.log_file and self.config.enable_debug_logging:
                        self.log_file.write(json.dumps(result.to_dict()) + "\n")
                        self.log_file.flush()
                
            except Exception as e:
                if self.config.enable_debug_logging:
                    print(f"Analysis error: {e}")
                self.performance_metrics.update(0, False)
            
            # Control analysis rate
            loop_time = time.time() - loop_start
            sleep_time = max(0, analysis_interval - loop_time)
            if sleep_time > 0:
                time.sleep(sleep_time)
    
    def _perform_analysis(self) -> Optional[AnalysisResult]:
        """Perform a single analysis cycle"""
        start_time = time.time()
        
        try:
            # Capture game state
            game_state_capture = self.vision_system.capture_game_state()
            
            if not game_state_capture:
                return None
            
            # Check for state changes
            state_changed = self.change_detector.detect_change(game_state_capture)
            
            # Convert to simulation format
            simulation_state = game_state_capture.to_simulation_state()
            
            # Generate build analysis and recommendations
            build_analysis = self.recommendation_engine.build_optimizer.analyze_build(simulation_state)
            recommendations = self.recommendation_engine.generate_recommendations(
                simulation_state, 
                max_recommendations=self.config.max_recommendations
            )
            
            # Filter recommendations by confidence threshold
            filtered_recommendations = [
                rec for rec in recommendations 
                if rec.confidence >= self.config.recommendation_threshold
            ]
            
            processing_time = time.time() - start_time
            
            # Create analysis result
            result = AnalysisResult(
                timestamp=start_time,
                game_state_capture=game_state_capture,
                build_analysis=build_analysis,
                recommendations=filtered_recommendations,
                processing_time=processing_time,
                confidence_score=game_state_capture.confidence_score,
                state_changed=state_changed
            )
            
            return result
            
        except Exception as e:
            if self.config.enable_debug_logging:
                print(f"Analysis error: {e}")
            return None
    
    def get_latest_result(self) -> Optional[AnalysisResult]:
        """Get the latest analysis result"""
        try:
            return self.result_queue.get_nowait()
        except queue.Empty:
            return None
    
    def get_all_results(self) -> List[AnalysisResult]:
        """Get all queued analysis results"""
        results = []
        while True:
            try:
                result = self.result_queue.get_nowait()
                results.append(result)
            except queue.Empty:
                break
        return results
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        return self.performance_metrics
    
    def get_status(self) -> Dict[str, Any]:
        """Get pipeline status information"""
        return {
            "running": self.running,
            "queue_size": self.result_queue.qsize(),
            "max_queue_size": self.config.max_queue_size,
            "performance": self.performance_metrics.to_dict(),
            "config": {
                "analysis_fps": self.config.analysis_fps,
                "vision_fps": self.config.vision_fps,
                "max_recommendations": self.config.max_recommendations,
                "recommendation_threshold": self.config.recommendation_threshold
            }
        }


class RealtimeAssistant:
    """High-level interface for real-time game assistance"""
    
    def __init__(self, config: Optional[AnalysisConfig] = None):
        if config is None:
            config = AnalysisConfig()
        
        self.config = config
        self.pipeline = AnalysisPipeline(config)
        self.current_recommendations: List[Recommendation] = []
        self.current_analysis: Optional[BuildAnalysis] = None
        self.last_update_time = 0
        
        # Add callback to update current state
        self.pipeline.add_result_callback(self._update_current_state)
    
    def start(self):
        """Start the real-time assistant"""
        self.pipeline.start_analysis()
    
    def stop(self):
        """Stop the real-time assistant"""
        self.pipeline.stop_analysis()
    
    def get_current_recommendations(self) -> List[Recommendation]:
        """Get current recommendations"""
        return self.current_recommendations.copy()
    
    def get_current_build_analysis(self) -> Optional[BuildAnalysis]:
        """Get current build analysis"""
        return self.current_analysis
    
    def get_status(self) -> Dict[str, Any]:
        """Get assistant status"""
        status = self.pipeline.get_status()
        status.update({
            "current_recommendations": len(self.current_recommendations),
            "has_build_analysis": self.current_analysis is not None,
            "last_update_time": self.last_update_time
        })
        return status
    
    def _update_current_state(self, result: AnalysisResult):
        """Update current state from analysis result"""
        if result.state_changed or not self.current_recommendations:
            self.current_recommendations = result.recommendations
            self.current_analysis = result.build_analysis
            self.last_update_time = result.timestamp
    
    def force_analysis(self) -> Optional[AnalysisResult]:
        """Force an immediate analysis"""
        return self.pipeline._perform_analysis()


def create_default_config() -> AnalysisConfig:
    """Create default analysis configuration"""
    return AnalysisConfig(
        analysis_fps=2,
        vision_fps=5,
        max_recommendations=5,
        recommendation_threshold=0.6,
        enable_performance_monitoring=True,
        enable_debug_logging=False
    )


def create_high_performance_config() -> AnalysisConfig:
    """Create high-performance analysis configuration"""
    return AnalysisConfig(
        analysis_fps=5,
        vision_fps=10,
        max_recommendations=8,
        recommendation_threshold=0.5,
        max_queue_size=20,
        enable_performance_monitoring=True,
        enable_debug_logging=False
    )


def create_debug_config() -> AnalysisConfig:
    """Create debug analysis configuration"""
    return AnalysisConfig(
        analysis_fps=1,
        vision_fps=2,
        max_recommendations=10,
        recommendation_threshold=0.3,
        enable_performance_monitoring=True,
        enable_debug_logging=True,
        log_file_path="analysis_debug.log"
    )
