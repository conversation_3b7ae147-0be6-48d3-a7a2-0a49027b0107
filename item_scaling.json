{"Ace_of_Spades": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Acorn_Collar": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amethyst": {"grid_size": [1, 1], "pixel_dimensions": [20, 20], "research_based": true, "rotation_support": true, "scale": 0.204}, "Amethyst_Egg": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amethyst_Whelp": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amulet_of_Alchemy": {"grid_size": [27, 1], "pixel_dimensions": [2340, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amulet_of_Darkness": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amulet_of_Energy": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amulet_of_Feasting": {"grid_size": [25, 1], "pixel_dimensions": [2167, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amulet_of_Life": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amulet_of_Steel": {"grid_size": [27, 1], "pixel_dimensions": [2340, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Amulet_of_the_Wild": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Anvil": {"grid_size": [12, 1], "pixel_dimensions": [629, 51], "research_based": true, "rotation_support": true, "scale": 0.514}, "Armored_Courage_Puppy": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Armored_Power_Puppy": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Armored_Wisdom_Puppy": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Artifact_Stone_Cold": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.827}, "Artifact_Stone_Death": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.85}, "Artifact_Stone_Heat": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.833}, "Axe": {"grid_size": [2, 2], "pixel_dimensions": [72, 71], "research_based": true, "rotation_support": true, "scale": 0.354}, "Badger_Rune": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Bag_of_Stones": {"grid_size": [7, 1], "pixel_dimensions": [606, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Banana": {"grid_size": [10, 1], "pixel_dimensions": [273, 27], "research_based": true, "rotation_support": true, "scale": 0.268}, "Belladonna27s_Shade": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.835}, "Belladonna27s_Whisper": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.844}, "Big_Bowl_of_Treats": {"grid_size": [10, 1], "pixel_dimensions": [867, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Blood_Amulet": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Blood_Goobert": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Blood_Harvester": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Bloodthorne": {"grid_size": [3, 1], "pixel_dimensions": [54, 18], "research_based": true, "rotation_support": true, "scale": 0.179}, "Bloody_Dagger": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Blueberries": {"grid_size": [5, 1], "pixel_dimensions": [273, 54], "research_based": true, "rotation_support": true, "scale": 0.537}, "Book_of_Ice": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Bow_and_Arrow": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Box_of_Prosperity": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Box_of_Riches": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Brass_Knuckles": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Broom": {"grid_size": [2, 2], "pixel_dimensions": [56, 56], "research_based": true, "rotation_support": true, "scale": 0.278}, "Bunch_of_Coins": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Burning_Banner": {"grid_size": [18, 1], "pixel_dimensions": [1560, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Burning_Blade": {"grid_size": [11, 1], "pixel_dimensions": [953, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Burning_Coal": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Burning_Sword": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Burning_Torch": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Busted_Blade": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Cap_of_Discomfort": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Cap_of_Resilience": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Carrot": {"grid_size": [8, 1], "pixel_dimensions": [667, 82], "research_based": true, "rotation_support": true, "scale": 0.818}, "Carrot_Goobert": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Cauldron": {"grid_size": [10, 1], "pixel_dimensions": [288, 28], "research_based": true, "rotation_support": true, "scale": 0.282}, "Chain_Whip": {"grid_size": [5, 1], "pixel_dimensions": [433, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Cheese": {"grid_size": [8, 1], "pixel_dimensions": [409, 50], "research_based": true, "rotation_support": true, "scale": 0.502}, "Cheese_Goobert": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Chili_Goobert": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Chili_Pepper": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Chtulhu": {"grid_size": [16, 1], "pixel_dimensions": [462, 28], "research_based": true, "rotation_support": true, "scale": 0.283}, "Claws_of_Attack": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Corrupted_Armor": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Corrupted_Crystal": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Courage_Puppy": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Critwood_Staff": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Crossblades": {"grid_size": [1, 1], "pixel_dimensions": [17, 0], "research_based": true, "rotation_support": true, "scale": 0.174}, "Cubert": {"grid_size": [12, 1], "pixel_dimensions": [362, 29], "research_based": true, "rotation_support": true, "scale": 0.296}, "Cursed_Dagger": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Customer_Card": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Dagger": {"grid_size": [2, 1], "pixel_dimensions": [54, 27], "research_based": true, "rotation_support": true, "scale": 0.268}, "Dancing_Dragon": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Dark_Lantern": {"grid_size": [10, 1], "pixel_dimensions": [867, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Darkest_Lotus": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Darksaber": {"grid_size": [2, 2], "pixel_dimensions": [55, 54], "research_based": true, "rotation_support": true, "scale": 0.271}, "Death_Scythe": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Deck_of_Cards": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Deerwood_Guardian": {"grid_size": [11, 1], "pixel_dimensions": [953, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Demonic_Flask": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Divine_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Djinn_Lamp": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Doom_Cap": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Double_Axe": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Draconic_Orb": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Dragon_Claws": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Dragon_Nest": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Dragonscale_Armor": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Dragonskin_Boots": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Duffle_Bag": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Eggscalibur": {"grid_size": [14, 1], "pixel_dimensions": [386, 27], "research_based": true, "rotation_support": true, "scale": 0.271}, "Elephant_Rune": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Emerald": {"grid_size": [1, 1], "pixel_dimensions": [20, 20], "research_based": true, "rotation_support": true, "scale": 0.204}, "Emerald_Egg": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Emerald_Whelp": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Falcon_Blade": {"grid_size": [11, 1], "pixel_dimensions": [953, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Fancy_Fencing_Rapier": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Fanfare": {"grid_size": [12, 1], "pixel_dimensions": [656, 54], "research_based": true, "rotation_support": true, "scale": 0.537}, "Fanny_Pack": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Fire_Pit": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Flame": {"grid_size": [1, 1], "pixel_dimensions": [50, 49], "research_based": true, "rotation_support": true, "scale": 0.491}, "Flame_Badge": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Flame_Whip": {"grid_size": [5, 1], "pixel_dimensions": [433, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Flute": {"grid_size": [12, 1], "pixel_dimensions": [656, 54], "research_based": true, "rotation_support": true, "scale": 0.537}, "Fly_Agaric": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Forging_Hammer": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Fortuna27s_Grace": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.842}, "Fortuna27s_Hope": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.823}, "Friendly_Fire": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Frostbite": {"grid_size": [3, 1], "pixel_dimensions": [52, 17], "research_based": true, "rotation_support": true, "scale": 0.173}, "Frozen_Buckler": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Frozen_Flame": {"grid_size": [10, 1], "pixel_dimensions": [867, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Garlic": {"grid_size": [8, 1], "pixel_dimensions": [225, 27], "research_based": true, "rotation_support": true, "scale": 0.276}, "Gingerbread_Jerry": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Gloves_of_Haste": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Gloves_of_Power": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Glowing_Crown": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Goobert": {"grid_size": [8, 1], "pixel_dimensions": [226, 28], "research_based": true, "rotation_support": true, "scale": 0.278}, "Goobling": {"grid_size": [3, 1], "pixel_dimensions": [182, 60], "research_based": true, "rotation_support": true, "scale": 0.596}, "Hammer": {"grid_size": [5, 1], "pixel_dimensions": [91, 18], "research_based": true, "rotation_support": true, "scale": 0.179}, "Hawk_Rune": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Healing_Herbs": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Health_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Heart_Container": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Heart_of_Darkness": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Hedgehog": {"grid_size": [5, 1], "pixel_dimensions": [162, 32], "research_based": true, "rotation_support": true, "scale": 0.318}, "Hero_Longsword": {"grid_size": [11, 1], "pixel_dimensions": [953, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Hero_Sword": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Heroic_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Holo_Fire_Lizard": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Holy_Armor": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Holy_Spear": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Hungry_Blade": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Ice_Armor": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Ice_Dragon": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Impractically_Large_Greatsword": {"grid_size": [7, 1], "pixel_dimensions": [606, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Jimbo": {"grid_size": [3, 1], "pixel_dimensions": [103, 34], "research_based": true, "rotation_support": true, "scale": 0.339}, "Jynx_torquilla": {"grid_size": [19, 1], "pixel_dimensions": [1647, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Katana": {"grid_size": [2, 2], "pixel_dimensions": [54, 53], "research_based": true, "rotation_support": true, "scale": 0.265}, "King_Crown": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "King_Goobert": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Leaf_Badge": {"grid_size": [5, 1], "pixel_dimensions": [433, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Leather_Armor": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Leather_Bag": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Leather_Boots": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Light_Goobert": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Lightsaber": {"grid_size": [2, 2], "pixel_dimensions": [56, 55], "research_based": true, "rotation_support": true, "scale": 0.276}, "Lucky_Clover": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Lucky_Piggy": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Lump_of_Coal": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Magic_Staff": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Magic_Torch": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Mana_Orb": {"grid_size": [5, 1], "pixel_dimensions": [433, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Mana_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Manathirst": {"grid_size": [3, 1], "pixel_dimensions": [55, 18], "research_based": true, "rotation_support": true, "scale": 0.182}, "Maneki-neko": {"grid_size": [8, 1], "pixel_dimensions": [220, 27], "research_based": true, "rotation_support": true, "scale": 0.27}, "Mega_Clover": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Miss_Fortune": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Molten_Dagger": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Molten_Spear": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Moon_Armor": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Moon_Shield": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Mr_Struggles": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.816}, "Mrs_Struggles": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.817}, "Nocturnal_Lock_Lifter": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Obsidian_Dragon": {"grid_size": [7, 1], "pixel_dimensions": [606, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Offering_Bowl": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Oil_Lamp": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Pan": {"grid_size": [10, 1], "pixel_dimensions": [273, 27], "research_based": true, "rotation_support": true, "scale": 0.268}, "Pandamonium": {"grid_size": [11, 1], "pixel_dimensions": [569, 51], "research_based": true, "rotation_support": true, "scale": 0.508}, "Pestilence_Flask": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Phoenix": {"grid_size": [3, 2], "pixel_dimensions": [110, 73], "research_based": true, "rotation_support": true, "scale": 0.362}, "Piercing_Arrow": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Piggybank": {"grid_size": [3, 2], "pixel_dimensions": [248, 164], "research_based": true, "rotation_support": true, "scale": 0.813}, "Pineapple": {"grid_size": [11, 1], "pixel_dimensions": [208, 18], "research_based": true, "rotation_support": true, "scale": 0.186}, "Platinum_Customer_Card": {"grid_size": [5, 1], "pixel_dimensions": [433, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Pocket_Sand": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Poison_Dagger": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Poison_Goobert": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Poison_Ivy": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Pop": {"grid_size": [2, 2], "pixel_dimensions": [116, 115], "research_based": true, "rotation_support": true, "scale": 0.572}, "Potion_Belt": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Power_Puppy": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Present": {"grid_size": [2, 2], "pixel_dimensions": [185, 183], "research_based": true, "rotation_support": true, "scale": 0.908}, "Prismatic_Orb": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Prismatic_Sword": {"grid_size": [11, 1], "pixel_dimensions": [953, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Protective_Purse": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Pumpkin": {"grid_size": [12, 1], "pixel_dimensions": [330, 27], "research_based": true, "rotation_support": true, "scale": 0.27}, "Rainbow_Badge": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Rainbow_Goobert_Deathslushy_Mansquisher": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Rainbow_Goobert_Epicglob_Uberviscous": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Rainbow_Goobert_Megasludge_Alphapuddle": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Rainbow_Goobert_Omegaooze_Primeslime": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Ranger_Bag": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Rat": {"grid_size": [8, 1], "pixel_dimensions": [515, 63], "research_based": true, "rotation_support": true, "scale": 0.631}, "Rat_Chef": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Relic_Case": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Reverse": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.839}, "Ripsaw_Blade": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Ruby": {"grid_size": [1, 1], "pixel_dimensions": [20, 20], "research_based": true, "rotation_support": true, "scale": 0.204}, "Ruby_Chonk": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Ruby_Egg": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Ruby_Whelp": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Sack_of_Surprises": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Sapphire": {"grid_size": [1, 1], "pixel_dimensions": [20, 20], "research_based": true, "rotation_support": true, "scale": 0.204}, "Sapphire_Egg": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Sapphire_Whelp": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Serpent_Staff": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Shaman_Mask": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Shell_Totem": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Shepherd27s_Crook": {"legacy_method": "bounding_box", "research_based": false, "scale": 0.85}, "Shield_of_Valor": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Shiny_Shell": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Shortbow": {"grid_size": [2, 1], "pixel_dimensions": [52, 25], "research_based": true, "rotation_support": true, "scale": 0.256}, "Shovel": {"grid_size": [2, 2], "pixel_dimensions": [55, 55], "research_based": true, "rotation_support": true, "scale": 0.273}, "Skull_Badge": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Snake": {"grid_size": [9, 1], "pixel_dimensions": [175, 19], "research_based": true, "rotation_support": true, "scale": 0.191}, "Snow_Stick": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Snowball": {"grid_size": [1, 1], "pixel_dimensions": [64, 63], "research_based": true, "rotation_support": true, "scale": 0.628}, "Snowcake": {"grid_size": [2, 2], "pixel_dimensions": [110, 109], "research_based": true, "rotation_support": true, "scale": 0.542}, "Spear": {"grid_size": [9, 1], "pixel_dimensions": [123, 13], "research_based": true, "rotation_support": true, "scale": 0.134}, "Spectral_Dagger": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Spiked_Collar": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Spiked_Shield": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Spiked_Staff": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Squirrel": {"grid_size": [10, 1], "pixel_dimensions": [262, 25], "research_based": true, "rotation_support": true, "scale": 0.257}, "Squirrel_Archer": {"grid_size": [10, 1], "pixel_dimensions": [867, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Stable_Recombobulator": {"grid_size": [17, 1], "pixel_dimensions": [1473, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Staff_of_Fire": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Staff_of_Unhealing": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Stamina_Sack": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Steel_Goobert": {"grid_size": [12, 1], "pixel_dimensions": [1040, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Stone": {"grid_size": [1, 1], "pixel_dimensions": [54, 54], "research_based": true, "rotation_support": true, "scale": 0.537}, "Stone_Armor": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Stone_Badge": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Stone_Golem": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Stone_Helm": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Stone_Shoes": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Stone_Skin_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Storage_Coffin": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Strong_Demonic_Flask": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Strong_Divine_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Strong_Health_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Strong_Heroic_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Strong_Mana_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Strong_Pestilence_Flask": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Strong_Stone_Skin_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Strong_Vampiric_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Sun_Armor": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Sun_Shield": {"grid_size": [16, 1], "pixel_dimensions": [1387, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "The_Fool": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "The_Lovers": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Thorn_Whip": {"grid_size": [5, 1], "pixel_dimensions": [433, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Thornbloom": {"grid_size": [3, 2], "pixel_dimensions": [110, 72], "research_based": true, "rotation_support": true, "scale": 0.361}, "Tim": {"grid_size": [1, 1], "pixel_dimensions": [75, 74], "research_based": true, "rotation_support": true, "scale": 0.74}, "Time_Dilator": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Toad": {"grid_size": [5, 1], "pixel_dimensions": [250, 49], "research_based": true, "rotation_support": true, "scale": 0.491}, "Topaz": {"grid_size": [1, 1], "pixel_dimensions": [20, 20], "research_based": true, "rotation_support": true, "scale": 0.204}, "Torch": {"grid_size": [2, 1], "pixel_dimensions": [45, 22], "research_based": true, "rotation_support": true, "scale": 0.224}, "Tusk_Piercer": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Tusk_Poker": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Unidentified_Amulet": {"grid_size": [1, 1], "pixel_dimensions": [86, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Unsettling_Presence": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Unstable_Recombobulator": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Utility_Pouch": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Vampiric_Armor": {"grid_size": [3, 2], "pixel_dimensions": [260, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Vampiric_Gloves": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Vampiric_Potion": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Villain_Sword": {"grid_size": [18, 1], "pixel_dimensions": [1560, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Vineweave_Basket": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Walrus_Tusk": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Whetstone": {"grid_size": [3, 1], "pixel_dimensions": [164, 54], "research_based": true, "rotation_support": true, "scale": 0.537}, "White-Eyes_Blue_Dragon": {"grid_size": [3, 1], "pixel_dimensions": [260, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Wisdom_Puppy": {"grid_size": [8, 1], "pixel_dimensions": [693, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Wolf_Badge": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Wolf_Emblem": {"grid_size": [21, 1], "pixel_dimensions": [1820, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Wolpertinger": {"grid_size": [12, 1], "pixel_dimensions": [305, 25], "research_based": true, "rotation_support": true, "scale": 0.25}, "Wonky_Snowman": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Wooden_Buckler": {"grid_size": [2, 2], "pixel_dimensions": [173, 171], "research_based": true, "rotation_support": true, "scale": 0.85}, "Wooden_Sword": {"grid_size": [2, 1], "pixel_dimensions": [173, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "Yggdrasil_Leaf": {"grid_size": [9, 1], "pixel_dimensions": [780, 85], "research_based": true, "rotation_support": true, "scale": 0.85}, "default": {"offset_x": 0, "offset_y": 0, "scale": 0.95}}