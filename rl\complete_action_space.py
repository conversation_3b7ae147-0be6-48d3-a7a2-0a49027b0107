"""
Complete RL Action Space

Implements the full action space for RL training including:
- Item placement with position and rotation
- Item selling and management
- Shop actions (buy, reroll, lock, reserve)
- Combination locks and recipe management
- Strategic decision making actions
"""

from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import numpy as np

from simulator.enhanced_item_database import EnhancedItem


class ActionType(Enum):
    # Shop phase actions
    BUY_ITEM = "buy_item"
    SELL_ITEM = "sell_item"
    REROLL_SHOP = "reroll_shop"
    LOCK_ITEM = "lock_item"
    RESERVE_ITEM = "reserve_item"
    
    # Backpack management actions
    PLACE_ITEM = "place_item"
    MOVE_ITEM = "move_item"
    ROTATE_ITEM = "rotate_item"
    REMOVE_ITEM = "remove_item"
    
    # Recipe and combination actions
    TOGGLE_COMBINATION_LOCK = "toggle_combination_lock"
    FORCE_COMBINATION = "force_combination"
    
    # Gemstone actions
    SOCKET_GEM = "socket_gem"
    UNSOCKET_GEM = "unsocket_gem"
    COMBINE_GEMS = "combine_gems"
    
    # Strategic actions
    END_TURN = "end_turn"
    PASS = "pass"


@dataclass
class Action:
    """Represents a single action in the action space"""
    action_type: ActionType
    parameters: Dict[str, Any]
    
    def to_vector(self, action_space_size: int) -> np.ndarray:
        """Convert action to vector representation for RL"""
        # This would encode the action as a vector
        # Implementation depends on specific RL framework
        pass


class ActionSpace:
    """Defines the complete action space for RL training"""
    
    def __init__(self, grid_width: int = 9, grid_height: int = 7, max_shop_items: int = 5):
        self.grid_width = grid_width
        self.grid_height = grid_height
        self.max_shop_items = max_shop_items
        
        # Calculate action space dimensions
        self.placement_actions = grid_width * grid_height * 4  # 4 rotations
        self.shop_actions = max_shop_items * 4  # buy, lock, reserve, sell
        self.management_actions = 20  # Various management actions
        
        self.total_actions = (
            self.placement_actions + 
            self.shop_actions + 
            self.management_actions
        )
    
    def get_valid_actions(self, game_state: Dict[str, Any]) -> List[Action]:
        """Get all valid actions for the current game state"""
        valid_actions = []
        
        # Add shop actions if in shop phase
        if game_state.get("phase") == "shop":
            valid_actions.extend(self._get_shop_actions(game_state))
        
        # Add backpack management actions
        valid_actions.extend(self._get_backpack_actions(game_state))
        
        # Add strategic actions
        valid_actions.extend(self._get_strategic_actions(game_state))
        
        return valid_actions
    
    def _get_shop_actions(self, game_state: Dict[str, Any]) -> List[Action]:
        """Get valid shop actions"""
        actions = []
        shop_items = game_state.get("shop_items", [])
        player_gold = game_state.get("gold", 0)
        
        for i, item in enumerate(shop_items):
            if item and not item.get("locked", False):
                # Buy action (if affordable)
                if player_gold >= item.get("cost", 0):
                    actions.append(Action(
                        action_type=ActionType.BUY_ITEM,
                        parameters={"shop_index": i, "item_name": item.get("name")}
                    ))
                
                # Lock action
                actions.append(Action(
                    action_type=ActionType.LOCK_ITEM,
                    parameters={"shop_index": i}
                ))
                
                # Reserve action (if available)
                if game_state.get("reserves_available", 0) > 0:
                    actions.append(Action(
                        action_type=ActionType.RESERVE_ITEM,
                        parameters={"shop_index": i}
                    ))
        
        # Reroll action (if affordable)
        reroll_cost = game_state.get("reroll_cost", 1)
        if player_gold >= reroll_cost:
            actions.append(Action(
                action_type=ActionType.REROLL_SHOP,
                parameters={"cost": reroll_cost}
            ))
        
        return actions
    
    def _get_backpack_actions(self, game_state: Dict[str, Any]) -> List[Action]:
        """Get valid backpack management actions"""
        actions = []
        backpack_items = game_state.get("backpack_items", [])
        inventory_items = game_state.get("inventory_items", [])
        
        # Item placement actions
        for item in inventory_items:
            for x in range(self.grid_width):
                for y in range(self.grid_height):
                    for rotation in range(4):  # 0, 90, 180, 270 degrees
                        if self._is_valid_placement(item, x, y, rotation, game_state):
                            actions.append(Action(
                                action_type=ActionType.PLACE_ITEM,
                                parameters={
                                    "item_name": item.get("name"),
                                    "x": x,
                                    "y": y,
                                    "rotation": rotation
                                }
                            ))
        
        # Item movement actions
        for item in backpack_items:
            current_pos = item.get("position", (0, 0))
            for x in range(self.grid_width):
                for y in range(self.grid_height):
                    if (x, y) != current_pos and self._is_valid_placement(item, x, y, 0, game_state):
                        actions.append(Action(
                            action_type=ActionType.MOVE_ITEM,
                            parameters={
                                "item_name": item.get("name"),
                                "from_x": current_pos[0],
                                "from_y": current_pos[1],
                                "to_x": x,
                                "to_y": y
                            }
                        ))
        
        # Item rotation actions
        for item in backpack_items:
            if item.get("can_rotate", True):
                actions.append(Action(
                    action_type=ActionType.ROTATE_ITEM,
                    parameters={"item_name": item.get("name")}
                ))
        
        # Item selling actions
        for item in backpack_items:
            if item.get("can_sell", True):
                actions.append(Action(
                    action_type=ActionType.SELL_ITEM,
                    parameters={
                        "item_name": item.get("name"),
                        "sell_value": item.get("sell_value", 1)
                    }
                ))
        
        # Combination lock actions
        for item in backpack_items:
            actions.append(Action(
                action_type=ActionType.TOGGLE_COMBINATION_LOCK,
                parameters={"item_name": item.get("name")}
            ))
        
        return actions
    
    def _get_strategic_actions(self, game_state: Dict[str, Any]) -> List[Action]:
        """Get strategic decision actions"""
        actions = []
        
        # Always allow pass action
        actions.append(Action(
            action_type=ActionType.PASS,
            parameters={}
        ))
        
        # End turn action (when appropriate)
        if game_state.get("can_end_turn", True):
            actions.append(Action(
                action_type=ActionType.END_TURN,
                parameters={}
            ))
        
        # Gemstone actions
        gems = game_state.get("gems", [])
        items_with_sockets = game_state.get("items_with_sockets", [])
        
        # Socket gem actions
        for gem in gems:
            if not gem.get("socketed", False):
                for item in items_with_sockets:
                    if item.get("available_sockets", 0) > 0:
                        actions.append(Action(
                            action_type=ActionType.SOCKET_GEM,
                            parameters={
                                "gem_id": gem.get("id"),
                                "item_name": item.get("name")
                            }
                        ))
        
        # Combine gem actions
        gem_pairs = self._find_combinable_gem_pairs(gems)
        for gem1, gem2 in gem_pairs:
            actions.append(Action(
                action_type=ActionType.COMBINE_GEMS,
                parameters={
                    "gem1_id": gem1.get("id"),
                    "gem2_id": gem2.get("id")
                }
            ))
        
        return actions
    
    def _is_valid_placement(self, item: Dict[str, Any], x: int, y: int, 
                          rotation: int, game_state: Dict[str, Any]) -> bool:
        """Check if item placement is valid"""
        # Get item shape (considering rotation)
        shape = self._get_rotated_shape(item.get("shape", [[True]]), rotation)
        
        # Check bounds
        item_width = len(shape[0])
        item_height = len(shape)
        
        if x + item_width > self.grid_width or y + item_height > self.grid_height:
            return False
        
        # Check for collisions with existing items
        occupied_cells = game_state.get("occupied_cells", set())
        
        for dy, row in enumerate(shape):
            for dx, cell in enumerate(row):
                if cell and (x + dx, y + dy) in occupied_cells:
                    return False
        
        return True
    
    def _get_rotated_shape(self, shape: List[List[bool]], rotation: int) -> List[List[bool]]:
        """Get item shape after rotation"""
        if rotation == 0:
            return shape
        elif rotation == 1:  # 90 degrees
            return [[shape[len(shape) - 1 - j][i] for j in range(len(shape))] 
                   for i in range(len(shape[0]))]
        elif rotation == 2:  # 180 degrees
            return [[shape[len(shape) - 1 - i][len(shape[0]) - 1 - j] 
                    for j in range(len(shape[0]))] for i in range(len(shape))]
        elif rotation == 3:  # 270 degrees
            return [[shape[j][len(shape[0]) - 1 - i] for j in range(len(shape))] 
                   for i in range(len(shape[0]) - 1, -1, -1)]
        else:
            return shape
    
    def _find_combinable_gem_pairs(self, gems: List[Dict[str, Any]]) -> List[Tuple[Dict[str, Any], Dict[str, Any]]]:
        """Find pairs of gems that can be combined"""
        pairs = []
        
        for i, gem1 in enumerate(gems):
            for gem2 in gems[i+1:]:
                if (gem1.get("type") == gem2.get("type") and 
                    gem1.get("rarity") == gem2.get("rarity") and
                    gem1.get("rarity") != "Perfect"):
                    pairs.append((gem1, gem2))
        
        return pairs


class ActionExecutor:
    """Executes actions in the game environment"""
    
    def __init__(self, game_manager):
        self.game_manager = game_manager
    
    def execute_action(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute an action and return new state, reward, and done flag"""
        
        if action.action_type == ActionType.BUY_ITEM:
            return self._execute_buy_item(action, game_state)
        elif action.action_type == ActionType.PLACE_ITEM:
            return self._execute_place_item(action, game_state)
        elif action.action_type == ActionType.SELL_ITEM:
            return self._execute_sell_item(action, game_state)
        elif action.action_type == ActionType.REROLL_SHOP:
            return self._execute_reroll_shop(action, game_state)
        elif action.action_type == ActionType.ROTATE_ITEM:
            return self._execute_rotate_item(action, game_state)
        elif action.action_type == ActionType.TOGGLE_COMBINATION_LOCK:
            return self._execute_toggle_lock(action, game_state)
        elif action.action_type == ActionType.END_TURN:
            return self._execute_end_turn(action, game_state)
        elif action.action_type == ActionType.PASS:
            return self._execute_pass(action, game_state)
        else:
            # Unknown action, return current state with penalty
            return game_state, -0.1, False
    
    def _execute_buy_item(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute buy item action"""
        shop_index = action.parameters["shop_index"]
        
        # Validate action
        shop_items = game_state.get("shop_items", [])
        if shop_index >= len(shop_items) or not shop_items[shop_index]:
            return game_state, -0.5, False  # Invalid action penalty
        
        item = shop_items[shop_index]
        cost = item.get("cost", 0)
        
        if game_state.get("gold", 0) < cost:
            return game_state, -0.3, False  # Can't afford penalty
        
        # Execute purchase
        new_state = game_state.copy()
        new_state["gold"] -= cost
        new_state["inventory_items"].append(item)
        new_state["shop_items"][shop_index] = None
        
        # Reward based on item value
        reward = 0.1 + (item.get("power_level", 1.0) * 0.05)
        
        return new_state, reward, False
    
    def _execute_place_item(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute place item action"""
        item_name = action.parameters["item_name"]
        x, y = action.parameters["x"], action.parameters["y"]
        rotation = action.parameters["rotation"]
        
        # Find item in inventory
        inventory = game_state.get("inventory_items", [])
        item = None
        for i, inv_item in enumerate(inventory):
            if inv_item.get("name") == item_name:
                item = inventory.pop(i)
                break
        
        if not item:
            return game_state, -0.5, False  # Item not found
        
        # Place item in backpack
        new_state = game_state.copy()
        item["position"] = (x, y)
        item["rotation"] = rotation
        new_state["backpack_items"].append(item)
        
        # Update occupied cells
        occupied = set(new_state.get("occupied_cells", set()))
        shape = self._get_rotated_shape(item.get("shape", [[True]]), rotation)
        for dy, row in enumerate(shape):
            for dx, cell in enumerate(row):
                if cell:
                    occupied.add((x + dx, y + dy))
        new_state["occupied_cells"] = occupied
        
        # Reward for successful placement
        reward = 0.2
        
        # Bonus for synergistic placement
        synergy_bonus = self._calculate_placement_synergy(item, x, y, new_state)
        reward += synergy_bonus
        
        return new_state, reward, False
    
    def _execute_sell_item(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute sell item action"""
        item_name = action.parameters["item_name"]
        
        # Find and remove item
        backpack = game_state.get("backpack_items", [])
        item = None
        for i, bp_item in enumerate(backpack):
            if bp_item.get("name") == item_name:
                item = backpack.pop(i)
                break
        
        if not item:
            return game_state, -0.3, False  # Item not found
        
        # Execute sale
        new_state = game_state.copy()
        sell_value = action.parameters.get("sell_value", 1)
        new_state["gold"] += sell_value
        
        # Remove from occupied cells
        self._remove_item_from_occupied(item, new_state)
        
        # Small reward for selling (resource management)
        reward = 0.05
        
        return new_state, reward, False
    
    def _execute_reroll_shop(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute reroll shop action"""
        cost = action.parameters["cost"]
        
        if game_state.get("gold", 0) < cost:
            return game_state, -0.3, False
        
        new_state = game_state.copy()
        new_state["gold"] -= cost
        
        # Generate new shop (this would call the shop system)
        # For now, just mark that reroll happened
        new_state["rerolls_used"] = new_state.get("rerolls_used", 0) + 1
        
        # Small reward for strategic rerolling
        reward = 0.02
        
        return new_state, reward, False
    
    def _execute_rotate_item(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute rotate item action"""
        item_name = action.parameters["item_name"]
        
        # Find item and rotate
        backpack = game_state.get("backpack_items", [])
        for item in backpack:
            if item.get("name") == item_name:
                current_rotation = item.get("rotation", 0)
                item["rotation"] = (current_rotation + 1) % 4
                break
        
        # Small reward for optimization
        reward = 0.01
        
        return game_state, reward, False
    
    def _execute_toggle_lock(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute toggle combination lock action"""
        item_name = action.parameters["item_name"]
        
        # Find item and toggle lock
        backpack = game_state.get("backpack_items", [])
        for item in backpack:
            if item.get("name") == item_name:
                item["combination_locked"] = not item.get("combination_locked", False)
                break
        
        # Small reward for strategic locking
        reward = 0.01
        
        return game_state, reward, False
    
    def _execute_end_turn(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute end turn action"""
        new_state = game_state.copy()
        new_state["phase"] = "battle"  # Transition to battle
        
        # Reward based on build quality
        build_quality = self._evaluate_build_quality(new_state)
        reward = build_quality * 0.5
        
        return new_state, reward, True  # Turn ends
    
    def _execute_pass(self, action: Action, game_state: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Execute pass action"""
        # No change to state, small time penalty
        return game_state, -0.01, False
    
    def _calculate_placement_synergy(self, item: Dict[str, Any], x: int, y: int, 
                                   game_state: Dict[str, Any]) -> float:
        """Calculate synergy bonus for item placement"""
        synergy_bonus = 0.0
        
        # Check for adjacent items with synergies
        adjacent_positions = [(x-1, y), (x+1, y), (x, y-1), (x, y+1)]
        
        for adj_x, adj_y in adjacent_positions:
            for bp_item in game_state.get("backpack_items", []):
                item_pos = bp_item.get("position", (0, 0))
                if item_pos == (adj_x, adj_y):
                    # Check for synergy between items
                    if self._items_have_synergy(item, bp_item):
                        synergy_bonus += 0.1
        
        return synergy_bonus
    
    def _items_have_synergy(self, item1: Dict[str, Any], item2: Dict[str, Any]) -> bool:
        """Check if two items have synergy"""
        # Simple synergy check based on tags/types
        tags1 = set(item1.get("tags", []))
        tags2 = set(item2.get("tags", []))
        
        return len(tags1.intersection(tags2)) > 0
    
    def _remove_item_from_occupied(self, item: Dict[str, Any], game_state: Dict[str, Any]):
        """Remove item's cells from occupied set"""
        occupied = set(game_state.get("occupied_cells", set()))
        position = item.get("position", (0, 0))
        rotation = item.get("rotation", 0)
        shape = self._get_rotated_shape(item.get("shape", [[True]]), rotation)
        
        for dy, row in enumerate(shape):
            for dx, cell in enumerate(row):
                if cell:
                    occupied.discard((position[0] + dx, position[1] + dy))
        
        game_state["occupied_cells"] = occupied
    
    def _evaluate_build_quality(self, game_state: Dict[str, Any]) -> float:
        """Evaluate the quality of the current build"""
        # Simple build evaluation
        backpack_items = game_state.get("backpack_items", [])
        
        if not backpack_items:
            return 0.0
        
        # Count synergies
        synergy_count = 0
        for i, item1 in enumerate(backpack_items):
            for item2 in backpack_items[i+1:]:
                if self._items_have_synergy(item1, item2):
                    synergy_count += 1
        
        # Calculate quality score
        item_count = len(backpack_items)
        synergy_ratio = synergy_count / max(item_count, 1)
        
        return min(1.0, synergy_ratio + (item_count * 0.1))
    
    def _get_rotated_shape(self, shape: List[List[bool]], rotation: int) -> List[List[bool]]:
        """Get item shape after rotation (same as in ActionSpace)"""
        if rotation == 0:
            return shape
        elif rotation == 1:  # 90 degrees
            return [[shape[len(shape) - 1 - j][i] for j in range(len(shape))] 
                   for i in range(len(shape[0]))]
        elif rotation == 2:  # 180 degrees
            return [[shape[len(shape) - 1 - i][len(shape[0]) - 1 - j] 
                    for j in range(len(shape[0]))] for i in range(len(shape))]
        elif rotation == 3:  # 270 degrees
            return [[shape[j][len(shape[0]) - 1 - i] for j in range(len(shape))] 
                   for i in range(len(shape[0]) - 1, -1, -1)]
        else:
            return shape
