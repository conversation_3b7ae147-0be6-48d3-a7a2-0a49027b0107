"""
Progression-Based Opponents System

Implements realistic opponent generation that scales with round and rank
progression as specified in the research. Replaces random generation with
intelligent build construction based on:

- Round progression (1-8+ with increasing difficulty)
- Rank/MMR scaling (Bronze to Diamond+)
- Class-specific build archetypes
- Item synergy and power level scaling
- Realistic decision-making patterns
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import random

from simulator.enhanced_item_database import EnhancedItem, ItemRarity, ItemClass, ItemType
from simulator.character_classes import CharacterClass
from simulator.subclass_system import SubclassManager


class OpponentRank(Enum):
    BRONZE = "Bronze"
    SILVER = "Silver"
    GOLD = "Gold"
    PLATINUM = "Platinum"
    DIAMOND = "Diamond"
    MASTER = "Master"
    GRANDMASTER = "Grandmaster"


class BuildArchetype(Enum):
    AGGRESSIVE = "Aggressive"
    DEFENSIVE = "Defensive"
    SYNERGY = "Synergy"
    BALANCED = "Balanced"
    EXPERIMENTAL = "Experimental"


@dataclass
class OpponentProfile:
    """Represents an opponent's characteristics and preferences"""
    rank: OpponentRank
    character_class: CharacterClass
    archetype: BuildArchetype
    skill_level: float  # 0.0 to 1.0
    gold_efficiency: float  # How well they spend gold
    synergy_awareness: float  # How well they build synergies
    risk_tolerance: float  # Willingness to take risks
    adaptation_speed: float  # How quickly they adapt to meta
    
    def get_power_level(self, round_number: int) -> float:
        """Calculate expected power level for this opponent at given round"""
        base_power = {
            OpponentRank.BRONZE: 0.6,
            OpponentRank.SILVER: 0.7,
            OpponentRank.GOLD: 0.8,
            OpponentRank.PLATINUM: 0.9,
            OpponentRank.DIAMOND: 0.95,
            OpponentRank.MASTER: 0.98,
            OpponentRank.GRANDMASTER: 1.0
        }[self.rank]
        
        # Scale with round progression
        round_scaling = min(1.0, round_number / 8.0)
        
        return base_power * (0.5 + 0.5 * round_scaling) * self.skill_level


@dataclass
class BuildTemplate:
    """Template for generating builds of a specific archetype"""
    name: str
    character_class: CharacterClass
    archetype: BuildArchetype
    core_items: List[str]
    preferred_items: List[str]
    synergy_tags: List[str]
    min_round: int
    power_scaling: float
    description: str


class ProgressionOpponentGenerator:
    """Generates realistic opponents based on progression and skill"""
    
    def __init__(self):
        self.subclass_manager = SubclassManager()
        self.build_templates = self._initialize_build_templates()
        self.item_power_levels = self._initialize_item_power_levels()
        self.meta_preferences = self._initialize_meta_preferences()
    
    def _initialize_build_templates(self) -> List[BuildTemplate]:
        """Initialize build templates for different archetypes"""
        return [
            # Berserker builds
            BuildTemplate(
                name="Weapon Master",
                character_class=CharacterClass.BERSERKER,
                archetype=BuildArchetype.AGGRESSIVE,
                core_items=["Hero Sword", "Whetstone", "Axe"],
                preferred_items=["Double Axe", "Crossblades", "Battle Axe"],
                synergy_tags=["weapon", "damage", "melee"],
                min_round=2,
                power_scaling=1.2,
                description="High damage weapon-focused build"
            ),
            BuildTemplate(
                name="Berserker Tank",
                character_class=CharacterClass.BERSERKER,
                archetype=BuildArchetype.DEFENSIVE,
                core_items=["Leather Armor", "Wooden Buckler", "Health Potion"],
                preferred_items=["Spiked Shield", "Vampiric Armor", "Dragonscale Armor"],
                synergy_tags=["armor", "health", "block"],
                min_round=1,
                power_scaling=0.9,
                description="Tanky sustain-focused build"
            ),
            
            # Pyromancer builds
            BuildTemplate(
                name="Fire Mage",
                character_class=CharacterClass.PYROMANCER,
                archetype=BuildArchetype.SYNERGY,
                core_items=["Torch", "Flame", "Mana Potion"],
                preferred_items=["Burning Torch", "Magic Torch", "Sun Armor"],
                synergy_tags=["fire", "mana", "magic"],
                min_round=2,
                power_scaling=1.1,
                description="Fire synergy and mana management"
            ),
            BuildTemplate(
                name="Heat Engine",
                character_class=CharacterClass.PYROMANCER,
                archetype=BuildArchetype.AGGRESSIVE,
                core_items=["Flame", "Flame", "Fire Pit"],
                preferred_items=["Molten Spear", "Burning Banner", "Dragon Nest"],
                synergy_tags=["heat", "fire", "damage"],
                min_round=3,
                power_scaling=1.3,
                description="Heat generation and burst damage"
            ),
            
            # Reaper builds
            BuildTemplate(
                name="Poison Master",
                character_class=CharacterClass.REAPER,
                archetype=BuildArchetype.SYNERGY,
                core_items=["Poison Dagger", "Pestilence Flask", "Fly Agaric"],
                preferred_items=["Snake", "Poison Goobert", "Cursed Dagger"],
                synergy_tags=["poison", "dark", "dot"],
                min_round=2,
                power_scaling=1.0,
                description="Poison damage over time strategy"
            ),
            BuildTemplate(
                name="Vampire Lord",
                character_class=CharacterClass.REAPER,
                archetype=BuildArchetype.BALANCED,
                core_items=["Bloody Dagger", "Blood Amulet", "Vampiric Armor"],
                preferred_items=["Bloodthorne", "Nocturnal Lock Lifter", "Manathirst"],
                synergy_tags=["vampirism", "lifesteal", "sustain"],
                min_round=3,
                power_scaling=1.1,
                description="Lifesteal and sustain focused"
            ),
            
            # Ranger builds
            BuildTemplate(
                name="Archer",
                character_class=CharacterClass.RANGER,
                archetype=BuildArchetype.AGGRESSIVE,
                core_items=["Bow and Arrow", "Quiver", "Lucky Clover"],
                preferred_items=["Fortuna's Grace", "Piercing Arrow", "Hunter"],
                synergy_tags=["ranged", "luck", "crit"],
                min_round=1,
                power_scaling=1.0,
                description="Ranged damage and critical hits"
            ),
            BuildTemplate(
                name="Beast Master",
                character_class=CharacterClass.RANGER,
                archetype=BuildArchetype.SYNERGY,
                core_items=["Goobert", "Big Bowl of Treats", "Healing Herbs"],
                preferred_items=["Steel Goobert", "Rat", "Squirrel"],
                synergy_tags=["pets", "nature", "support"],
                min_round=2,
                power_scaling=0.95,
                description="Pet synergy and support"
            ),
            
            # Advanced builds for higher rounds
            BuildTemplate(
                name="Subclass Specialist",
                character_class=CharacterClass.BERSERKER,
                archetype=BuildArchetype.EXPERIMENTAL,
                core_items=["Brass Knuckles", "Anvil", "Wolf Emblem"],
                preferred_items=["Shaman Mask", "Deerwood Guardian"],
                synergy_tags=["subclass", "specialized", "unique"],
                min_round=8,
                power_scaling=1.5,
                description="Subclass-focused specialized build"
            )
        ]
    
    def _initialize_item_power_levels(self) -> Dict[str, float]:
        """Initialize power level ratings for items"""
        return {
            # Basic items (power 1.0-2.0)
            "Wooden Sword": 1.0,
            "Dagger": 1.2,
            "Health Potion": 1.1,
            "Leather Armor": 1.3,
            
            # Intermediate items (power 2.0-3.0)
            "Hero Sword": 2.2,
            "Torch": 2.0,
            "Poison Dagger": 2.5,
            "Spiked Shield": 2.3,
            
            # Advanced items (power 3.0-4.0)
            "Hero Longsword": 3.2,
            "Crossblades": 3.8,
            "Burning Torch": 3.5,
            "Vampiric Armor": 3.4,
            
            # Elite items (power 4.0+)
            "Bloodthorne": 4.5,
            "Fortuna's Grace": 4.2,
            "Sun Armor": 4.8,
            "Dragonscale Armor": 4.6,
            
            # Subclass items (power 5.0+)
            "Brass Knuckles": 5.2,
            "Dark Lantern": 5.8,
            "Cursed Dagger": 5.5,
            "Mega Clover": 6.0
        }
    
    def _initialize_meta_preferences(self) -> Dict[OpponentRank, Dict[str, float]]:
        """Initialize meta preferences by rank"""
        return {
            OpponentRank.BRONZE: {
                "simple_builds": 0.8,
                "complex_synergies": 0.2,
                "meta_following": 0.3,
                "experimentation": 0.1
            },
            OpponentRank.SILVER: {
                "simple_builds": 0.6,
                "complex_synergies": 0.4,
                "meta_following": 0.5,
                "experimentation": 0.2
            },
            OpponentRank.GOLD: {
                "simple_builds": 0.4,
                "complex_synergies": 0.6,
                "meta_following": 0.7,
                "experimentation": 0.3
            },
            OpponentRank.PLATINUM: {
                "simple_builds": 0.3,
                "complex_synergies": 0.7,
                "meta_following": 0.8,
                "experimentation": 0.4
            },
            OpponentRank.DIAMOND: {
                "simple_builds": 0.2,
                "complex_synergies": 0.8,
                "meta_following": 0.9,
                "experimentation": 0.6
            },
            OpponentRank.MASTER: {
                "simple_builds": 0.1,
                "complex_synergies": 0.9,
                "meta_following": 0.95,
                "experimentation": 0.8
            },
            OpponentRank.GRANDMASTER: {
                "simple_builds": 0.05,
                "complex_synergies": 0.95,
                "meta_following": 1.0,
                "experimentation": 0.9
            }
        }
    
    def generate_opponent(self, round_number: int, target_rank: OpponentRank, 
                         character_class: Optional[CharacterClass] = None) -> Dict[str, Any]:
        """Generate a realistic opponent for the given round and rank"""
        
        # Select character class if not specified
        if not character_class:
            character_class = random.choice(list(CharacterClass))
        
        # Create opponent profile
        profile = self._create_opponent_profile(target_rank, character_class)
        
        # Select build template
        template = self._select_build_template(profile, round_number)
        
        # Generate the build
        build = self._generate_build(profile, template, round_number)
        
        # Add subclass if round 8+
        if round_number >= 8:
            subclass = self._select_subclass(profile, character_class)
            build["subclass"] = subclass
        
        return {
            "profile": profile,
            "template": template,
            "build": build,
            "power_level": profile.get_power_level(round_number),
            "round": round_number
        }
    
    def _create_opponent_profile(self, rank: OpponentRank, character_class: CharacterClass) -> OpponentProfile:
        """Create an opponent profile with realistic characteristics"""
        
        # Base skill levels by rank
        skill_ranges = {
            OpponentRank.BRONZE: (0.3, 0.5),
            OpponentRank.SILVER: (0.45, 0.65),
            OpponentRank.GOLD: (0.6, 0.8),
            OpponentRank.PLATINUM: (0.75, 0.9),
            OpponentRank.DIAMOND: (0.85, 0.95),
            OpponentRank.MASTER: (0.9, 0.98),
            OpponentRank.GRANDMASTER: (0.95, 1.0)
        }
        
        skill_min, skill_max = skill_ranges[rank]
        
        # Generate characteristics with some randomness
        return OpponentProfile(
            rank=rank,
            character_class=character_class,
            archetype=random.choice(list(BuildArchetype)),
            skill_level=random.uniform(skill_min, skill_max),
            gold_efficiency=random.uniform(skill_min, skill_max),
            synergy_awareness=random.uniform(skill_min * 0.8, skill_max * 1.1),
            risk_tolerance=random.uniform(0.2, 0.8),
            adaptation_speed=random.uniform(skill_min * 0.9, skill_max * 1.0)
        )
    
    def _select_build_template(self, profile: OpponentProfile, round_number: int) -> BuildTemplate:
        """Select an appropriate build template for the opponent"""
        
        # Filter templates by class and round availability
        available_templates = [
            template for template in self.build_templates
            if (template.character_class == profile.character_class and 
                template.min_round <= round_number)
        ]
        
        if not available_templates:
            # Fallback to any template for the class
            available_templates = [
                template for template in self.build_templates
                if template.character_class == profile.character_class
            ]
        
        # Weight templates by archetype preference and meta following
        meta_prefs = self.meta_preferences[profile.rank]
        
        weights = []
        for template in available_templates:
            weight = 1.0
            
            # Prefer templates matching archetype
            if template.archetype == profile.archetype:
                weight *= 2.0
            
            # Adjust for meta preferences
            if template.archetype == BuildArchetype.EXPERIMENTAL:
                weight *= meta_prefs["experimentation"]
            elif template.archetype == BuildArchetype.SYNERGY:
                weight *= meta_prefs["complex_synergies"]
            else:
                weight *= meta_prefs["simple_builds"]
            
            weights.append(weight)
        
        # Select template based on weights
        if weights:
            template = random.choices(available_templates, weights=weights)[0]
        else:
            template = available_templates[0] if available_templates else self.build_templates[0]
        
        return template
    
    def _generate_build(self, profile: OpponentProfile, template: BuildTemplate, 
                       round_number: int) -> Dict[str, Any]:
        """Generate the actual build based on template and profile"""
        
        # Calculate available gold (rough estimate)
        base_gold = round_number * 10
        efficiency_multiplier = 0.7 + (profile.gold_efficiency * 0.3)
        available_gold = int(base_gold * efficiency_multiplier)
        
        # Start with core items
        selected_items = template.core_items.copy()
        used_gold = sum(self._get_item_cost(item) for item in selected_items)
        
        # Add preferred items based on gold and synergy awareness
        for item in template.preferred_items:
            item_cost = self._get_item_cost(item)
            
            # Check if we can afford it
            if used_gold + item_cost <= available_gold:
                # Check if opponent recognizes the synergy
                synergy_chance = profile.synergy_awareness
                if random.random() < synergy_chance:
                    selected_items.append(item)
                    used_gold += item_cost
        
        # Fill remaining slots with appropriate items
        remaining_gold = available_gold - used_gold
        filler_items = self._select_filler_items(profile, template, remaining_gold, round_number)
        selected_items.extend(filler_items)
        
        return {
            "items": selected_items,
            "gold_spent": used_gold,
            "synergy_score": self._calculate_synergy_score(selected_items, template),
            "power_estimate": self._estimate_build_power(selected_items)
        }
    
    def _select_subclass(self, profile: OpponentProfile, character_class: CharacterClass) -> Optional[str]:
        """Select a subclass for Round 8+ opponents"""
        subclass_choices = self.subclass_manager.get_round_8_shop(character_class)
        
        if not subclass_choices:
            return None
        
        # Higher skill opponents make better subclass choices
        if profile.skill_level > 0.8:
            # Pick based on synergy with build archetype
            archetype_preferences = {
                BuildArchetype.AGGRESSIVE: ["Fighter", "Hunter", "Ashbringer"],
                BuildArchetype.DEFENSIVE: ["Crusader", "Lifebinder", "Vampiress"],
                BuildArchetype.SYNERGY: ["Beastmaster", "Scalewarden", "Alchemist"],
                BuildArchetype.EXPERIMENTAL: ["Shaman", "Cryomancer", "Witch"]
            }
            
            preferred_names = archetype_preferences.get(profile.archetype, [])
            for choice in subclass_choices:
                if choice.subclass_name in preferred_names:
                    return choice.subclass_name
        
        # Random selection for lower skill or no preference match
        return random.choice(subclass_choices).subclass_name
    
    def _get_item_cost(self, item_name: str) -> int:
        """Get the gold cost of an item"""
        # This would integrate with the item database
        # For now, use power level as a rough cost estimate
        power = self.item_power_levels.get(item_name, 2.0)
        return int(power * 5)  # Rough cost scaling
    
    def _select_filler_items(self, profile: OpponentProfile, template: BuildTemplate, 
                           remaining_gold: int, round_number: int) -> List[str]:
        """Select filler items to complete the build"""
        filler_items = []
        
        # Basic filler items by round
        basic_items = {
            1: ["Health Potion", "Whetstone", "Healing Herbs"],
            2: ["Mana Potion", "Lucky Clover", "Leather Armor"],
            3: ["Gloves of Haste", "Wooden Buckler", "Flame"],
            4: ["Blood Amulet", "Pestilence Flask", "Goobert"]
        }
        
        available_fillers = []
        for round_num in range(1, min(round_number + 1, 5)):
            available_fillers.extend(basic_items.get(round_num, []))
        
        # Add items until gold runs out
        for item in available_fillers:
            cost = self._get_item_cost(item)
            if remaining_gold >= cost and item not in template.core_items:
                filler_items.append(item)
                remaining_gold -= cost
                
                if len(filler_items) >= 3:  # Limit filler items
                    break
        
        return filler_items
    
    def _calculate_synergy_score(self, items: List[str], template: BuildTemplate) -> float:
        """Calculate how well the items synergize"""
        synergy_score = 0.0
        
        # Count items that match template synergy tags
        for item in items:
            item_lower = item.lower()
            for tag in template.synergy_tags:
                if tag in item_lower:
                    synergy_score += 1.0
        
        # Normalize by number of items
        return synergy_score / max(len(items), 1)
    
    def _estimate_build_power(self, items: List[str]) -> float:
        """Estimate the total power level of the build"""
        total_power = 0.0
        
        for item in items:
            power = self.item_power_levels.get(item, 2.0)
            total_power += power
        
        # Add synergy bonus (simplified)
        synergy_bonus = len(set(items)) * 0.1  # Diversity bonus
        
        return total_power + synergy_bonus
    
    def generate_opponent_pool(self, round_number: int, pool_size: int = 8) -> List[Dict[str, Any]]:
        """Generate a pool of opponents for matchmaking"""
        opponents = []
        
        # Distribute ranks realistically
        rank_distribution = {
            OpponentRank.BRONZE: 0.25,
            OpponentRank.SILVER: 0.25,
            OpponentRank.GOLD: 0.20,
            OpponentRank.PLATINUM: 0.15,
            OpponentRank.DIAMOND: 0.10,
            OpponentRank.MASTER: 0.04,
            OpponentRank.GRANDMASTER: 0.01
        }
        
        for _ in range(pool_size):
            # Select rank based on distribution
            rank = random.choices(
                list(rank_distribution.keys()),
                weights=list(rank_distribution.values())
            )[0]
            
            opponent = self.generate_opponent(round_number, rank)
            opponents.append(opponent)
        
        return opponents
