"""
Test the Subclass System Implementation
"""

from simulator.subclass_system import Subclass<PERSON>anager, CharacterClass

def test_subclass_system():
    print("🎭 TESTING SUBCLASS SYSTEM")
    print("=" * 60)
    
    manager = SubclassManager()
    
    # Test Round 8 shop for each class
    for char_class in CharacterClass:
        print(f"\n🏛️ {char_class.value.upper()} SUBCLASSES:")
        print("-" * 40)
        
        round_8_shop = manager.get_round_8_shop(char_class)
        
        for i, subclass_item in enumerate(round_8_shop, 1):
            print(f"  {i}. {subclass_item.name} ({subclass_item.subclass_name})")
            print(f"     Cost: {subclass_item.cost} gold")
            print(f"     Effect: {subclass_item.effect_description}")
            print(f"     Unlocks: {', '.join(subclass_item.unlocked_items[:3])}{'...' if len(subclass_item.unlocked_items) > 3 else ''}")
            print()
    
    # Test subclass selection and effects
    print("\n🧪 TESTING SUBCLASS EFFECTS:")
    print("=" * 40)
    
    # Test Ranger Beastmaster
    print("\n🏹 RANGER - BEASTMASTER:")
    ranger_shop = manager.get_round_8_shop(CharacterClass.RANGER)
    beastmaster_item = next(item for item in ranger_shop if item.subclass_name == "Beastmaster")
    
    selection_result = manager.select_subclass(beastmaster_item)
    print(f"  Selected: {selection_result['subclass_name']}")
    print(f"  Unlocked items: {len(selection_result['unlocked_items'])}")
    print(f"  Item pool expanded: {selection_result['item_pool_expanded']}")
    
    # Test effect application
    player_state = {"forest_friends_count": 3}
    effects = manager.apply_subclass_effects("Beastmaster", player_state)
    print(f"  With 3 forest friends:")
    print(f"    Pet activation bonus: +{effects.get('pet_activation_bonus', 0):.0%}")
    print(f"    Food synergy bonus: +{effects.get('food_synergy_bonus', 0)}")
    
    # Test Berserker Pack Leader
    print("\n⚔️ BERSERKER - PACK LEADER:")
    berserker_shop = manager.get_round_8_shop(CharacterClass.BERSERKER)
    pack_leader_item = next(item for item in berserker_shop if item.subclass_name == "Pack Leader")
    
    player_state = {"pet_count": 4}
    effects = manager.apply_subclass_effects("Pack Leader", player_state)
    print(f"  With 4 pets:")
    print(f"    Crit chance bonus: +{effects.get('crit_chance_bonus', 0):.0%}")
    print(f"    Wolf companions unlocked: {effects.get('wolf_companions_unlocked', False)}")
    
    # Test Pyromancer Ashbringer
    print("\n🔥 PYROMANCER - ASHBRINGER:")
    pyro_shop = manager.get_round_8_shop(CharacterClass.PYROMANCER)
    ashbringer_item = next(item for item in pyro_shop if item.subclass_name == "Ashbringer")
    
    # Test battle start effect
    ashbringer_effect = manager.subclass_effects["Ashbringer"]
    player_state = {"max_health": 100}
    battle_start = ashbringer_effect.on_battle_start(player_state)
    print(f"  Battle start effects:")
    print(f"    Starting health: {battle_start.get('starting_health', 100)}")
    print(f"    Reincarnate available: {battle_start.get('reincarnate_available', False)}")
    print(f"    Reincarnate heal: {battle_start.get('reincarnate_heal', 0)}")
    
    # Test Reaper Hexblade
    print("\n💀 REAPER - HEXBLADE:")
    reaper_shop = manager.get_round_8_shop(CharacterClass.REAPER)
    hexblade_item = next(item for item in reaper_shop if item.subclass_name == "Hexblade")
    
    effects = manager.apply_subclass_effects("Hexblade", {})
    print(f"  Cursed Dagger stats:")
    print(f"    Damage: {effects.get('cursed_dagger_damage', 0)}")
    print(f"    Stamina cost: {effects.get('stamina_cost', 1)}")
    print(f"    Debuff chance: {effects.get('debuff_application_chance', 0):.0%}")
    print(f"    Available debuffs: {', '.join(effects.get('available_debuffs', []))}")
    
    # Test item unlocking
    print("\n🔓 ITEM UNLOCKING TEST:")
    print("-" * 30)
    
    test_cases = [
        ("Beastmaster", "Rat"),
        ("Scalewarden", "Amethyst Dragon Egg"),
        ("Witch", "Mrs. Struggles"),
        ("Hunter", "Piercing Arrow"),
        ("Shaman", "Hawk Rune")
    ]
    
    for subclass, item in test_cases:
        is_unlocked = manager.is_item_unlocked(item, subclass)
        print(f"  {subclass}: {item} = {'✅ Unlocked' if is_unlocked else '❌ Locked'}")
    
    # Test statistics
    print(f"\n📊 SUBCLASS STATISTICS:")
    print("-" * 30)
    total_subclasses = sum(len(items) for items in manager.subclass_items.values())
    print(f"  Total subclasses: {total_subclasses}")
    print(f"  Classes with subclasses: {len(manager.subclass_items)}")
    print(f"  Subclasses per class: {total_subclasses // len(manager.subclass_items)}")
    
    total_unlocked_items = sum(len(items) for items in manager.unlocked_items_by_subclass.values())
    print(f"  Total unlocked items: {total_unlocked_items}")
    
    print("\n" + "=" * 60)
    print("✅ SUBCLASS SYSTEM TEST COMPLETE")

if __name__ == "__main__":
    test_subclass_system()
