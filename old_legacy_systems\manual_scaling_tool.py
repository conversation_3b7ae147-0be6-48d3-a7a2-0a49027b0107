import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageDraw
import os
import json
import math

class ManualScalingTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Manual Item Scaling Tool")
        self.root.geometry("1400x900")
        
        # Data storage
        self.reference_images = []
        self.item_images = {}
        self.current_ref_index = 0
        self.current_item = None
        self.scaling_data = {}
        
        # UI state
        self.overlay_scale = 1.0
        self.overlay_alpha = 0.5
        self.overlay_x = 100
        self.overlay_y = 100
        self.dragging = False
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        self.zoom_level = 1.0  # For high-res image viewing
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control panel (left side)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Reference image selection
        ttk.Label(control_frame, text="Reference Image:").pack(anchor=tk.W)
        self.ref_var = tk.StringVar()
        self.ref_combo = ttk.Combobox(control_frame, textvariable=self.ref_var, width=30)
        self.ref_combo.pack(fill=tk.X, pady=(0, 10))
        self.ref_combo.bind('<<ComboboxSelected>>', self.on_reference_changed)
        
        # Item selection with progress tracking
        item_frame = ttk.Frame(control_frame)
        item_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(item_frame, text="Item to Scale:").pack(anchor=tk.W)
        self.item_var = tk.StringVar()
        self.item_combo = ttk.Combobox(item_frame, textvariable=self.item_var, width=30)
        self.item_combo.pack(fill=tk.X, pady=(0, 5))
        self.item_combo.bind('<<ComboboxSelected>>', self.on_item_changed)

        # Progress info
        self.progress_label = ttk.Label(item_frame, text="Progress: 0/0 items completed",
                                       font=('Arial', 8), foreground='blue')
        self.progress_label.pack(anchor=tk.W)

        # Navigation buttons
        nav_frame = ttk.Frame(item_frame)
        nav_frame.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(nav_frame, text="← Prev", command=self.prev_item, width=8).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(nav_frame, text="Next →", command=self.next_item, width=8).pack(side=tk.LEFT)
        ttk.Button(nav_frame, text="Next Missing", command=self.next_missing_item, width=12).pack(side=tk.RIGHT)
        
        # Scale controls
        ttk.Label(control_frame, text="Scale:").pack(anchor=tk.W)
        self.scale_var = tk.DoubleVar(value=1.0)
        self.scale_slider = ttk.Scale(control_frame, from_=0.1, to=3.0, 
                                     variable=self.scale_var, orient=tk.HORIZONTAL)
        self.scale_slider.pack(fill=tk.X, pady=(0, 5))
        self.scale_slider.bind('<Motion>', self.on_scale_changed)
        
        self.scale_label = ttk.Label(control_frame, text="Scale: 1.00")
        self.scale_label.pack(anchor=tk.W)
        
        # Alpha controls
        ttk.Label(control_frame, text="Transparency:").pack(anchor=tk.W, pady=(10, 0))
        self.alpha_var = tk.DoubleVar(value=0.5)
        self.alpha_slider = ttk.Scale(control_frame, from_=0.1, to=1.0,
                                     variable=self.alpha_var, orient=tk.HORIZONTAL)
        self.alpha_slider.pack(fill=tk.X, pady=(0, 5))
        self.alpha_slider.bind('<Motion>', self.on_alpha_changed)

        # Zoom controls for high-res images
        ttk.Label(control_frame, text="Image Zoom:").pack(anchor=tk.W, pady=(10, 0))
        self.zoom_var = tk.DoubleVar(value=1.0)
        self.zoom_slider = ttk.Scale(control_frame, from_=0.25, to=2.0,
                                    variable=self.zoom_var, orient=tk.HORIZONTAL)
        self.zoom_slider.pack(fill=tk.X, pady=(0, 5))
        self.zoom_slider.bind('<Motion>', self.on_zoom_changed)

        self.zoom_label = ttk.Label(control_frame, text="Zoom: 1.00x")
        self.zoom_label.pack(anchor=tk.W)
        
        # Position info
        self.pos_label = ttk.Label(control_frame, text="Position: (0, 0)")
        self.pos_label.pack(anchor=tk.W, pady=(10, 0))
        
        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="Save Scale", command=self.save_current_scale).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Reset Position", command=self.reset_position).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Export Data", command=self.export_scaling_data).pack(fill=tk.X, pady=(0, 5))

        # Auto-save indicator
        self.autosave_label = ttk.Label(button_frame, text="✓ Auto-save enabled",
                                       font=('Arial', 8), foreground='green')
        self.autosave_label.pack(pady=(5, 0))
        
        # Instructions
        instructions = """
Instructions:
1. Select a reference image (2559x1439 high-res)
2. Use Image Zoom to see details clearly
3. Select item to scale (✓ = done, grayed out)
4. Drag overlay to position over same item
5. Scale to match size perfectly
6. Click 'Save Scale' (auto-saves)
7. Use 'Next Missing' to find uncompleted items
8. Export when all items done

Controls:
- Image Zoom: See high-res details
- Drag overlay to move
- Scale slider to resize overlay
- Transparency to see through overlay
- Scrollbars for large images
        """
        ttk.Label(control_frame, text=instructions, justify=tk.LEFT, 
                 wraplength=250, font=('Arial', 8)).pack(pady=(20, 0))
        
        # Canvas (right side) with scrollbars for high-res images
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Create canvas with scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg='white')

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack scrollbars and canvas
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Bind mouse events
        self.canvas.bind('<Button-1>', self.on_mouse_down)
        self.canvas.bind('<B1-Motion>', self.on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_mouse_up)
        
    def load_data(self):
        """Load reference images and item images."""
        # Load reference images
        ref_dir = "data/item_scale_reference"
        if os.path.exists(ref_dir):
            ref_files = [f for f in os.listdir(ref_dir) if f.endswith('.png')]
            self.reference_images = ref_files
            self.ref_combo['values'] = ref_files
            if ref_files:
                self.ref_combo.set(ref_files[0])
        
        # Load item images
        item_dir = "data/item_images"
        if os.path.exists(item_dir):
            item_files = [f for f in os.listdir(item_dir) if f.endswith('.png')]
            # Focus on key items first
            priority_items = ['Axe.png', 'Dagger.png', 'Hero_Sword.png', 'Banana.png', 
                            'Shield.png', 'Bow.png', 'Sword.png', 'Apple.png']
            
            # Put priority items first, then others
            sorted_items = []
            for item in priority_items:
                if item in item_files:
                    sorted_items.append(item)
            for item in item_files:
                if item not in priority_items:
                    sorted_items.append(item)
            
            self.item_combo['values'] = sorted_items
            if sorted_items:
                self.item_combo.set(sorted_items[0])
        
        # Load existing scaling data if available
        if os.path.exists('manual_scaling_data.json'):
            with open('manual_scaling_data.json', 'r') as f:
                self.scaling_data = json.load(f)

        # Update item list with completion status
        self.update_item_list_display()

        # Initial load
        self.load_reference_image()
        self.load_item_image()
        
    def load_reference_image(self):
        """Load and display the current reference image."""
        if not self.reference_images:
            return

        ref_file = self.ref_var.get()
        if not ref_file:
            return

        ref_path = os.path.join("data/item_scale_reference", ref_file)
        self.ref_image = Image.open(ref_path)

        print(f"Loaded reference image: {self.ref_image.size[0]}x{self.ref_image.size[1]} pixels")

        # Apply zoom level
        zoom = self.zoom_var.get() if hasattr(self, 'zoom_var') else 1.0

        # For high-res images, start with a reasonable base scale
        base_scale = min(1200 / self.ref_image.width, 800 / self.ref_image.height, 1.0)

        # Apply zoom on top of base scale
        final_scale = base_scale * zoom

        display_width = int(self.ref_image.width * final_scale)
        display_height = int(self.ref_image.height * final_scale)

        self.display_scale = final_scale
        self.ref_display = self.ref_image.resize((display_width, display_height), Image.LANCZOS)
        self.ref_photo = ImageTk.PhotoImage(self.ref_display)

        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.ref_photo)
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        # Update zoom label
        if hasattr(self, 'zoom_label'):
            self.zoom_label.config(text=f"Zoom: {zoom:.2f}x (Base: {base_scale:.2f}x)")

        self.update_overlay()
        
    def load_item_image(self):
        """Load the current item image."""
        display_name = self.item_var.get()
        if not display_name:
            return

        # Get clean filename
        item_file = self.get_clean_item_name(display_name)
        item_path = os.path.join("data/item_images", item_file)

        if not os.path.exists(item_path):
            return

        self.item_image = Image.open(item_path).convert('RGBA')

        # Load existing scale if available
        item_name = item_file.replace('.png', '')
        if item_name in self.scaling_data:
            self.scale_var.set(self.scaling_data[item_name]['scale'])
            self.overlay_x = self.scaling_data[item_name].get('x', 100)
            self.overlay_y = self.scaling_data[item_name].get('y', 100)
        else:
            # Reset to defaults for new items
            self.scale_var.set(1.0)
            self.overlay_x = 100
            self.overlay_y = 100

        self.update_overlay()

    def update_item_list_display(self):
        """Update the item combo box to show completion status."""
        if not hasattr(self, 'item_combo'):
            return

        current_values = list(self.item_combo['values'])
        display_values = []

        completed_count = 0
        total_count = len(current_values)

        for item_file in current_values:
            item_name = item_file.replace('.png', '')
            if item_name in self.scaling_data:
                # Mark as completed with checkmark and gray it out
                display_values.append(f"✓ {item_file}")
                completed_count += 1
            else:
                # Normal display for incomplete items
                display_values.append(f"  {item_file}")

        self.item_combo['values'] = display_values

        # Update progress label
        if hasattr(self, 'progress_label'):
            self.progress_label.config(text=f"Progress: {completed_count}/{total_count} items completed")

        # Update current selection to maintain position
        current_item = self.item_var.get()
        if current_item:
            # Find the display version of current item
            for display_item in display_values:
                if current_item in display_item:
                    self.item_var.set(display_item)
                    break

    def get_clean_item_name(self, display_name):
        """Extract clean item filename from display name (removes ✓ and spaces)."""
        return display_name.replace('✓ ', '').replace('  ', '').strip()

    def prev_item(self):
        """Navigate to previous item."""
        current_values = list(self.item_combo['values'])
        current_item = self.item_var.get()

        if current_item in current_values:
            current_index = current_values.index(current_item)
            if current_index > 0:
                self.item_combo.set(current_values[current_index - 1])
                self.on_item_changed()

    def next_item(self):
        """Navigate to next item."""
        current_values = list(self.item_combo['values'])
        current_item = self.item_var.get()

        if current_item in current_values:
            current_index = current_values.index(current_item)
            if current_index < len(current_values) - 1:
                self.item_combo.set(current_values[current_index + 1])
                self.on_item_changed()

    def next_missing_item(self):
        """Navigate to next item that hasn't been completed."""
        current_values = list(self.item_combo['values'])
        current_item = self.item_var.get()

        # Find current position
        start_index = 0
        if current_item in current_values:
            start_index = current_values.index(current_item) + 1

        # Look for next incomplete item
        for i in range(start_index, len(current_values)):
            if not current_values[i].startswith('✓'):
                self.item_combo.set(current_values[i])
                self.on_item_changed()
                return

        # If no incomplete items found after current, search from beginning
        for i in range(0, start_index):
            if not current_values[i].startswith('✓'):
                self.item_combo.set(current_values[i])
                self.on_item_changed()
                return

        messagebox.showinfo("Complete!", "All items have been completed!")

    def auto_save(self):
        """Auto-save current progress."""
        if self.scaling_data:
            try:
                with open('manual_scaling_data.json', 'w') as f:
                    json.dump(self.scaling_data, f, indent=2)

                # Update autosave indicator
                if hasattr(self, 'autosave_label'):
                    self.autosave_label.config(text="✓ Auto-saved", foreground='green')
                    self.root.after(2000, lambda: self.autosave_label.config(text="✓ Auto-save enabled"))

            except Exception as e:
                if hasattr(self, 'autosave_label'):
                    self.autosave_label.config(text="❌ Save failed", foreground='red')

    def update_overlay(self):
        """Update the overlay display."""
        if not hasattr(self, 'item_image') or not hasattr(self, 'ref_display'):
            return
            
        # Remove old overlay
        self.canvas.delete("overlay")
        
        # Calculate scaled size
        scale = self.scale_var.get()
        display_scale = scale * self.display_scale
        
        scaled_width = int(self.item_image.width * display_scale)
        scaled_height = int(self.item_image.height * display_scale)
        
        if scaled_width <= 0 or scaled_height <= 0:
            return
            
        # Create scaled item image
        scaled_item = self.item_image.resize((scaled_width, scaled_height), Image.LANCZOS)
        
        # Apply transparency
        alpha = int(self.alpha_var.get() * 255)
        if scaled_item.mode == 'RGBA':
            # Modify alpha channel
            r, g, b, a = scaled_item.split()
            a = a.point(lambda x: min(x, alpha))
            scaled_item = Image.merge('RGBA', (r, g, b, a))
        else:
            # Add alpha channel
            scaled_item.putalpha(alpha)
        
        # Convert to PhotoImage
        self.overlay_photo = ImageTk.PhotoImage(scaled_item)
        
        # Display overlay
        display_x = int(self.overlay_x * self.display_scale)
        display_y = int(self.overlay_y * self.display_scale)
        
        self.canvas.create_image(display_x, display_y, anchor=tk.NW, 
                               image=self.overlay_photo, tags="overlay")
        
        # Update labels
        self.scale_label.config(text=f"Scale: {scale:.2f}")
        self.pos_label.config(text=f"Position: ({self.overlay_x}, {self.overlay_y})")
        
    def on_reference_changed(self, event=None):
        """Handle reference image change."""
        self.load_reference_image()
        
    def on_item_changed(self, event=None):
        """Handle item change."""
        self.load_item_image()
        
    def on_scale_changed(self, event=None):
        """Handle scale change."""
        self.update_overlay()
        
    def on_alpha_changed(self, event=None):
        """Handle alpha change."""
        self.update_overlay()

    def on_zoom_changed(self, event=None):
        """Handle zoom change."""
        self.load_reference_image()
        
    def on_mouse_down(self, event):
        """Handle mouse down for dragging."""
        self.dragging = True
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
        
    def on_mouse_drag(self, event):
        """Handle mouse drag to move overlay."""
        if self.dragging:
            dx = event.x - self.last_mouse_x
            dy = event.y - self.last_mouse_y
            
            # Convert to original image coordinates
            self.overlay_x += dx / self.display_scale
            self.overlay_y += dy / self.display_scale
            
            self.last_mouse_x = event.x
            self.last_mouse_y = event.y
            
            self.update_overlay()
            
    def on_mouse_up(self, event):
        """Handle mouse up."""
        self.dragging = False
        
    def reset_position(self):
        """Reset overlay position to center."""
        self.overlay_x = 100
        self.overlay_y = 100
        self.update_overlay()
        
    def save_current_scale(self):
        """Save the current scale settings."""
        display_name = self.item_var.get()
        if not display_name:
            messagebox.showwarning("Warning", "No item selected!")
            return

        # Get clean filename
        item_file = self.get_clean_item_name(display_name)
        item_name = item_file.replace('.png', '')

        # Calculate actual pixel dimensions
        scale = self.scale_var.get()
        actual_width = int(self.item_image.width * scale)
        actual_height = int(self.item_image.height * scale)

        self.scaling_data[item_name] = {
            'scale': scale,
            'width': actual_width,
            'height': actual_height,
            'x': self.overlay_x,
            'y': self.overlay_y,
            'reference_image': self.ref_var.get()
        }

        # Auto-save
        self.auto_save()

        # Update display to show completion
        self.update_item_list_display()

        messagebox.showinfo("Saved", f"Saved scaling for {item_name}:\n"
                                   f"Scale: {scale:.3f}\n"
                                   f"Size: {actual_width}x{actual_height} pixels\n\n"
                                   f"Auto-saved to file!")

        # Auto-advance to next missing item
        self.root.after(500, self.next_missing_item)
        
    def export_scaling_data(self):
        """Export all scaling data to JSON."""
        if not self.scaling_data:
            messagebox.showwarning("Warning", "No scaling data to export!")
            return
            
        # Save to file
        with open('manual_scaling_data.json', 'w') as f:
            json.dump(self.scaling_data, f, indent=2)
            
        # Also create a simplified lookup for the main script
        simple_lookup = {}
        for item_name, data in self.scaling_data.items():
            simple_lookup[item_name] = {
                'width': data['width'],
                'height': data['height']
            }
            
        with open('manual_item_sizes.json', 'w') as f:
            json.dump(simple_lookup, f, indent=2)
            
        messagebox.showinfo("Exported", f"Exported scaling data for {len(self.scaling_data)} items to:\n"
                                      f"- manual_scaling_data.json (detailed)\n"
                                      f"- manual_item_sizes.json (for main script)")
        
    def run(self):
        """Run the application."""
        self.root.mainloop()

if __name__ == "__main__":
    app = ManualScalingTool()
    app.run()
