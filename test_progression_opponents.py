"""
Test the Progression-Based Opponents System
"""

from simulator.progression_opponents import (
    ProgressionOpponentGenerator, OpponentRank, BuildArchetype
)
from simulator.character_classes import CharacterClass

def test_progression_opponents():
    print("🤖 TESTING PROGRESSION-BASED OPPONENTS SYSTEM")
    print("=" * 60)
    
    generator = ProgressionOpponentGenerator()
    
    # Test build templates
    print("\n📋 TESTING BUILD TEMPLATES:")
    print("-" * 30)
    
    print(f"  Loaded {len(generator.build_templates)} build templates:")
    for i, template in enumerate(generator.build_templates[:5]):
        print(f"    {i+1}. {template.name} ({template.character_class.value})")
        print(f"       Archetype: {template.archetype.value}")
        print(f"       Core items: {', '.join(template.core_items[:3])}...")
        print(f"       Min round: {template.min_round}")
        print(f"       Power scaling: {template.power_scaling}")
        print()
    
    # Test opponent generation by rank
    print("\n🏆 TESTING OPPONENTS BY RANK:")
    print("-" * 30)
    
    test_round = 4
    for rank in OpponentRank:
        opponent = generator.generate_opponent(test_round, rank, CharacterClass.BERSERKER)
        profile = opponent["profile"]
        build = opponent["build"]
        
        print(f"  {rank.value} Berserker (Round {test_round}):")
        print(f"    Skill level: {profile.skill_level:.2f}")
        print(f"    Archetype: {profile.archetype.value}")
        print(f"    Power level: {opponent['power_level']:.2f}")
        print(f"    Items: {len(build['items'])}")
        print(f"    Gold spent: {build['gold_spent']}")
        print(f"    Synergy score: {build['synergy_score']:.2f}")
        print(f"    Power estimate: {build['power_estimate']:.1f}")
        print()
    
    # Test progression across rounds
    print("\n📈 TESTING ROUND PROGRESSION:")
    print("-" * 30)
    
    test_rank = OpponentRank.GOLD
    for round_num in [1, 3, 5, 8]:
        opponent = generator.generate_opponent(round_num, test_rank, CharacterClass.PYROMANCER)
        profile = opponent["profile"]
        build = opponent["build"]
        template = opponent["template"]
        
        print(f"  Round {round_num} Gold Pyromancer:")
        print(f"    Template: {template.name}")
        print(f"    Power level: {opponent['power_level']:.2f}")
        print(f"    Items: {', '.join(build['items'][:4])}{'...' if len(build['items']) > 4 else ''}")
        print(f"    Gold spent: {build['gold_spent']}")
        
        if round_num >= 8 and "subclass" in opponent["build"]:
            print(f"    Subclass: {opponent['build']['subclass']}")
        print()
    
    # Test different character classes
    print("\n🏛️ TESTING CHARACTER CLASSES:")
    print("-" * 30)
    
    test_round = 5
    test_rank = OpponentRank.PLATINUM
    
    for char_class in CharacterClass:
        opponent = generator.generate_opponent(test_round, test_rank, char_class)
        profile = opponent["profile"]
        template = opponent["template"]
        build = opponent["build"]
        
        print(f"  {char_class.value}:")
        print(f"    Template: {template.name}")
        print(f"    Archetype: {template.archetype.value}")
        print(f"    Core synergies: {', '.join(template.synergy_tags[:3])}")
        print(f"    Build power: {build['power_estimate']:.1f}")
        print()
    
    # Test build archetypes
    print("\n🎯 TESTING BUILD ARCHETYPES:")
    print("-" * 30)
    
    archetype_counts = {archetype: 0 for archetype in BuildArchetype}
    
    # Generate many opponents to see archetype distribution
    for _ in range(50):
        opponent = generator.generate_opponent(4, OpponentRank.GOLD)
        archetype_counts[opponent["profile"].archetype] += 1
    
    print("  Archetype distribution (50 opponents):")
    for archetype, count in archetype_counts.items():
        percentage = (count / 50) * 100
        print(f"    {archetype.value}: {count} ({percentage:.1f}%)")
    
    # Test subclass selection
    print("\n🎭 TESTING SUBCLASS SELECTION:")
    print("-" * 30)
    
    round_8_opponents = []
    for char_class in CharacterClass:
        for _ in range(3):  # Generate 3 opponents per class
            opponent = generator.generate_opponent(8, OpponentRank.DIAMOND, char_class)
            if "subclass" in opponent["build"]:
                round_8_opponents.append((char_class, opponent["build"]["subclass"]))
    
    print("  Round 8 subclass selections:")
    for char_class, subclass in round_8_opponents:
        print(f"    {char_class.value}: {subclass}")
    
    # Test opponent pool generation
    print("\n👥 TESTING OPPONENT POOL GENERATION:")
    print("-" * 30)
    
    pool = generator.generate_opponent_pool(round_number=6, pool_size=8)
    
    print(f"  Generated pool of {len(pool)} opponents for Round 6:")
    
    rank_counts = {}
    class_counts = {}
    
    for i, opponent in enumerate(pool):
        profile = opponent["profile"]
        template = opponent["template"]
        
        # Count ranks and classes
        rank_counts[profile.rank] = rank_counts.get(profile.rank, 0) + 1
        class_counts[profile.character_class] = class_counts.get(profile.character_class, 0) + 1
        
        print(f"    {i+1}. {profile.rank.value} {profile.character_class.value}")
        print(f"       Template: {template.name}")
        print(f"       Power: {opponent['power_level']:.2f}")
        print(f"       Items: {len(opponent['build']['items'])}")
    
    print(f"\n  Rank distribution:")
    for rank, count in rank_counts.items():
        print(f"    {rank.value}: {count}")
    
    print(f"\n  Class distribution:")
    for char_class, count in class_counts.items():
        print(f"    {char_class.value}: {count}")
    
    # Test skill scaling
    print("\n🧠 TESTING SKILL SCALING:")
    print("-" * 30)
    
    skill_test_opponents = []
    for rank in [OpponentRank.BRONZE, OpponentRank.GOLD, OpponentRank.GRANDMASTER]:
        for _ in range(5):
            opponent = generator.generate_opponent(5, rank)
            profile = opponent["profile"]
            skill_test_opponents.append((rank, profile.skill_level, profile.synergy_awareness))
    
    print("  Skill levels by rank (5 samples each):")
    for rank in [OpponentRank.BRONZE, OpponentRank.GOLD, OpponentRank.GRANDMASTER]:
        rank_skills = [skill for r, skill, _ in skill_test_opponents if r == rank]
        rank_synergy = [synergy for r, _, synergy in skill_test_opponents if r == rank]
        
        avg_skill = sum(rank_skills) / len(rank_skills)
        avg_synergy = sum(rank_synergy) / len(rank_synergy)
        
        print(f"    {rank.value}:")
        print(f"      Avg skill: {avg_skill:.3f}")
        print(f"      Avg synergy awareness: {avg_synergy:.3f}")
    
    # Test power scaling
    print("\n⚡ TESTING POWER SCALING:")
    print("-" * 30)
    
    print("  Power levels across rounds and ranks:")
    print("    Round |  Bronze |  Gold   | Diamond")
    print("    ------|---------|---------|--------")
    
    for round_num in [1, 3, 5, 8]:
        bronze_power = generator.generate_opponent(round_num, OpponentRank.BRONZE)["power_level"]
        gold_power = generator.generate_opponent(round_num, OpponentRank.GOLD)["power_level"]
        diamond_power = generator.generate_opponent(round_num, OpponentRank.DIAMOND)["power_level"]
        
        print(f"      {round_num}   |  {bronze_power:.3f}  |  {gold_power:.3f}  |  {diamond_power:.3f}")
    
    print("\n" + "=" * 60)
    print("✅ PROGRESSION-BASED OPPONENTS TEST COMPLETE")

if __name__ == "__main__":
    test_progression_opponents()
