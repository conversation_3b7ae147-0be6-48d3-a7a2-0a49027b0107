"""
Subclass System Implementation

Implements the Round 8 subclass selection system as specified in Section 5.2
of DeepResearch.txt. At Round 8, the shop is replaced with 5 unique class-specific
subclass items. Purchasing one locks in the subclass and unlocks new items.

All 20 subclasses are implemented with their effects and item pool expansions.
"""

from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

from simulator.character_classes import CharacterClass


@dataclass
class SubclassItem:
    """Represents a subclass selection item"""
    name: str
    subclass_name: str
    character_class: CharacterClass
    cost: int
    description: str
    effect_description: str
    unlocked_items: List[str]  # Items unlocked by this subclass
    grid_shape: List[List[bool]]
    
    def get_grid_size(self) -> tuple:
        """Get the (width, height) of the subclass item"""
        if not self.grid_shape:
            return (2, 2)  # Default size
        return (len(self.grid_shape[0]), len(self.grid_shape))


class SubclassEffect:
    """Base class for subclass effects"""
    
    def __init__(self, subclass_name: str, item_name: str):
        self.subclass_name = subclass_name
        self.item_name = item_name
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        """Apply ongoing passive effects"""
        return {}
    
    def on_battle_start(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        """Apply start-of-battle effects"""
        return {}
    
    def on_item_activation(self, item: Any, player_state: Dict[str, Any]) -> Dict[str, Any]:
        """Apply effects when items activate"""
        return {}


# Berserker Subclasses
class FighterEffect(SubclassEffect):
    """Brass Knuckles (Fighter): Grants stun chance and scales with Battle Rage"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        battle_rage_active = player_state.get("battle_rage_active", False)
        base_stun_chance = 0.15  # 15% base stun chance
        
        if battle_rage_active:
            return {"stun_chance": base_stun_chance * 1.5}  # 50% increase during rage
        return {"stun_chance": base_stun_chance}


class BlacksmithEffect(SubclassEffect):
    """Anvil (Blacksmith): Massively buffs a single weapon based on adjacent crafted items"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        # Count adjacent crafted items (would need grid analysis)
        adjacent_crafted = player_state.get("adjacent_crafted_items", 0)
        weapon_damage_bonus = adjacent_crafted * 5  # +5 damage per crafted item
        
        return {
            "weapon_damage_bonus": weapon_damage_bonus,
            "affected_weapon": "adjacent_weapon"
        }


class ChieftainEffect(SubclassEffect):
    """Deerwood Guardian (Chieftain): Extends Battle Rage duration with Nature items"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        nature_items = player_state.get("nature_items_count", 0)
        rage_duration_bonus = nature_items * 1.0  # +1 second per Nature item
        
        return {
            "battle_rage_duration_bonus": rage_duration_bonus,
            "nature_healing_bonus": nature_items * 2  # +2 healing per Nature item
        }


class PackLeaderEffect(SubclassEffect):
    """Wolf Emblem (Pack Leader): Unlocks Wolf companions and grants crit based on pets"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        pet_count = player_state.get("pet_count", 0)
        crit_bonus = pet_count * 0.05  # +5% crit per pet
        
        return {
            "crit_chance_bonus": crit_bonus,
            "wolf_companions_unlocked": True
        }


class ShamanEffect(SubclassEffect):
    """Shaman Mask (Shaman): Unlocks Runes and converts Luck into random buffs"""
    
    def on_battle_start(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        luck_stacks = player_state.get("luck_stacks", 0)
        random_buffs = luck_stacks // 2  # 1 random buff per 2 Luck
        
        return {
            "random_buffs_granted": random_buffs,
            "runes_unlocked": True
        }


# Pyromancer Subclasses
class AshbringerEffect(SubclassEffect):
    """Dark Lantern (Ashbringer): Starts at 50% HP but grants Reincarnate"""
    
    def on_battle_start(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        max_hp = player_state.get("max_health", 100)
        return {
            "starting_health": max_hp * 0.5,
            "reincarnate_available": True,
            "reincarnate_heal": max_hp * 0.3  # Heal for 30% on reincarnate
        }


class ScalewardenEffect(SubclassEffect):
    """Dragon Nest (Scalewarden): Unlocks Dragon Eggs and provides pet buffs"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        dragon_eggs = player_state.get("dragon_eggs_count", 0)
        
        return {
            "dragon_eggs_unlocked": True,
            "pet_damage_bonus": dragon_eggs * 3,  # +3 damage per dragon egg
            "pet_health_bonus": dragon_eggs * 5   # +5 health per dragon egg
        }


class FirebenderEffect(SubclassEffect):
    """Friendly Fire (Firebender): Consumes Mana to generate large amounts of Heat"""
    
    def on_item_activation(self, item: Any, player_state: Dict[str, Any]) -> Dict[str, Any]:
        current_mana = player_state.get("mana", 0)
        if current_mana >= 2:
            return {
                "mana_consumed": 2,
                "heat_generated": 8  # Generate 8 Heat for 2 Mana
            }
        return {}


class CrusaderEffect(SubclassEffect):
    """Burning Banner (Crusader): Defensive subclass focused on Holy items"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        holy_items = player_state.get("holy_items_count", 0)
        
        return {
            "block_bonus": holy_items * 2,      # +2 Block per Holy item
            "buff_duration_bonus": holy_items * 0.5,  # +0.5s buff duration per Holy item
            "debuff_resistance": holy_items * 0.1     # +10% debuff resistance per Holy item
        }


class CryomancerEffect(SubclassEffect):
    """Frozen Flame (Cryomancer): Unlocks Ice items and converts Heat to Cold"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        heat_stacks = player_state.get("heat_stacks", 0)
        cold_conversion = heat_stacks // 2  # Convert 2 Heat to 1 Cold on opponent
        
        return {
            "ice_items_unlocked": True,
            "opponent_cold_stacks": cold_conversion,
            "heat_to_cold_ratio": 0.5
        }


# Reaper Subclasses
class AlchemistEffect(SubclassEffect):
    """Cauldron (Alchemist): Upgrades adjacent potions and provides random resources"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        adjacent_potions = player_state.get("adjacent_potions_count", 0)
        
        return {
            "potion_upgrade_bonus": adjacent_potions,
            "random_resource_generation": True,
            "potion_effect_multiplier": 1.5  # 50% stronger potion effects
        }


class VampiressEffect(SubclassEffect):
    """Nocturnal Lock Lifter (Vampiress): Greatly enhances Vampirism and lifesteal"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        vampirism_stacks = player_state.get("vampirism_stacks", 0)
        
        return {
            "vampirism_multiplier": 2.0,  # Double vampirism effectiveness
            "lifesteal_overflow": True,   # Lifesteal can exceed max HP temporarily
            "night_bonus": vampirism_stacks * 0.1  # +10% damage per Vampirism stack at night
        }


class WitchEffect(SubclassEffect):
    """Mr. Struggles (Witch): Focuses on Fatigue damage and unlocks Plushies"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        plushies_count = player_state.get("plushies_count", 0)
        
        return {
            "plushies_unlocked": True,
            "fatigue_damage_bonus": plushies_count * 2,  # +2 fatigue damage per plushie
            "fatigue_start_time_reduction": plushies_count * 5  # Start fatigue 5s earlier per plushie
        }


class HexbladeEffect(SubclassEffect):
    """Cursed Dagger (Hexblade): Powerful stamina-free weapon that applies debuffs"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "cursed_dagger_damage": 12,  # High base damage
            "stamina_cost": 0,           # No stamina cost
            "debuff_application_chance": 0.8,  # 80% chance to apply debuff
            "available_debuffs": ["Poison", "Blind", "Cold"]
        }


class VenomancerEffect(SubclassEffect):
    """Snake (Venomancer): Prevents opponent's Poison cleanse based on Luck"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        luck_stacks = player_state.get("luck_stacks", 0)
        poison_cleanse_prevention = min(0.9, luck_stacks * 0.1)  # Max 90% prevention
        
        return {
            "poison_cleanse_prevention": poison_cleanse_prevention,
            "pet_poison_synergy": True,
            "snake_companion_unlocked": True
        }


# Ranger Subclasses
class BeastmasterEffect(SubclassEffect):
    """Big Bowl of Treats (Beastmaster): Unlocks Friends of the Forest pets"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        forest_friends = player_state.get("forest_friends_count", 0)
        
        return {
            "forest_friends_unlocked": True,
            "pet_activation_bonus": forest_friends * 0.2,  # +20% faster per friend
            "food_synergy_bonus": forest_friends * 1.5     # +1.5 food effect per friend
        }


class LifebinderEffect(SubclassEffect):
    """Yggdrasil Leaf (Lifebinder): Powerful healing and support using Mana and Nature"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        nature_items = player_state.get("nature_items_count", 0)
        mana_available = player_state.get("mana", 0)
        
        healing_power = (nature_items * 3) + (mana_available * 2)
        
        return {
            "healing_power_bonus": healing_power,
            "mana_to_healing_conversion": 2,  # 2 healing per mana
            "nature_synergy_multiplier": 1.5
        }


class HunterEffect(SubclassEffect):
    """Piercing Arrow (Hunter): Maximizes damage of a single powerful weapon"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        # Focus all bonuses on the highest damage weapon
        return {
            "single_weapon_focus": True,
            "weapon_damage_multiplier": 2.5,  # 150% damage bonus to focused weapon
            "critical_damage_multiplier": 3.0,  # Triple critical damage
            "piercing_shots": True  # Shots pierce through targets
        }


class PathfinderEffect(SubclassEffect):
    """Poison Ivy (Pathfinder): Damage-over-time strategy using Spikes and Poison"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        poison_stacks = player_state.get("poison_stacks_on_opponent", 0)
        spikes_stacks = player_state.get("spikes_stacks", 0)
        
        return {
            "poison_damage_multiplier": 1.5,  # 50% more poison damage
            "spikes_poison_synergy": True,    # Spikes can apply poison
            "dot_acceleration": poison_stacks * 0.1  # Faster DoT ticks per poison stack
        }


class GrovekeeperEffect(SubclassEffect):
    """Mega Clover (Grovekeeper): Generates Lucky Clovers and massive Luck buffs"""
    
    def apply_passive_effect(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        luck_stacks = player_state.get("luck_stacks", 0)
        
        if luck_stacks >= 10:  # Threshold for massive buff shower
            return {
                "clover_generation": True,
                "luck_threshold_reached": True,
                "massive_buff_shower": 15,  # Grant 15 random buffs
                "luck_multiplier": 2.0      # Double all Luck effects
            }
        
        return {
            "clover_generation": True,
            "luck_accumulation_bonus": 0.2  # 20% faster Luck gain
        }


class SubclassManager:
    """Manages the subclass selection system and effects"""

    def __init__(self):
        self.subclass_items = self._initialize_subclass_items()
        self.subclass_effects = self._initialize_subclass_effects()
        self.unlocked_items_by_subclass = self._initialize_unlocked_items()

    def _initialize_subclass_items(self) -> Dict[CharacterClass, List[SubclassItem]]:
        """Initialize all subclass items for Round 8 selection"""
        return {
            CharacterClass.BERSERKER: [
                SubclassItem(
                    name="Brass Knuckles",
                    subclass_name="Fighter",
                    character_class=CharacterClass.BERSERKER,
                    cost=15,
                    description="Grants stun chance and scales with Battle Rage",
                    effect_description="15% stun chance, +50% during Battle Rage",
                    unlocked_items=[],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Anvil",
                    subclass_name="Blacksmith",
                    character_class=CharacterClass.BERSERKER,
                    cost=18,
                    description="Massively buffs a single weapon based on adjacent crafted items",
                    effect_description="+5 damage per adjacent crafted item",
                    unlocked_items=["Forging Hammer", "Masterwork items"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Deerwood Guardian",
                    subclass_name="Chieftain",
                    character_class=CharacterClass.BERSERKER,
                    cost=16,
                    description="Extends Battle Rage duration with Nature items",
                    effect_description="+1s Battle Rage per Nature item, +2 healing",
                    unlocked_items=["Nature items", "Druid equipment"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Wolf Emblem",
                    subclass_name="Pack Leader",
                    character_class=CharacterClass.BERSERKER,
                    cost=17,
                    description="Unlocks Wolf companions and grants crit based on pets",
                    effect_description="+5% crit per pet, unlocks Wolf companions",
                    unlocked_items=["Wolf", "Pack items"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Shaman Mask",
                    subclass_name="Shaman",
                    character_class=CharacterClass.BERSERKER,
                    cost=19,
                    description="Unlocks Runes and converts Luck into random buffs",
                    effect_description="1 random buff per 2 Luck, unlocks Runes",
                    unlocked_items=["Hawk Rune", "Badger Rune", "Bear Rune"],
                    grid_shape=[[True, True], [True, True]]
                )
            ],

            CharacterClass.PYROMANCER: [
                SubclassItem(
                    name="Dark Lantern",
                    subclass_name="Ashbringer",
                    character_class=CharacterClass.PYROMANCER,
                    cost=20,
                    description="High-risk subclass: starts at 50% HP but grants Reincarnate",
                    effect_description="Start at 50% HP, Reincarnate ability",
                    unlocked_items=["Dark items", "Ash equipment"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Dragon Nest",
                    subclass_name="Scalewarden",
                    character_class=CharacterClass.PYROMANCER,
                    cost=18,
                    description="Unlocks Dragon Eggs and provides buffs for pets",
                    effect_description="Unlocks Dragon Eggs, +3 damage +5 HP per egg",
                    unlocked_items=["Amethyst Dragon Egg", "Emerald Dragon Egg", "Sapphire Dragon Egg"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Friendly Fire",
                    subclass_name="Firebender",
                    character_class=CharacterClass.PYROMANCER,
                    cost=16,
                    description="Consumes Mana to generate large amounts of Heat",
                    effect_description="2 Mana → 8 Heat conversion",
                    unlocked_items=["Advanced Fire spells"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Burning Banner",
                    subclass_name="Crusader",
                    character_class=CharacterClass.PYROMANCER,
                    cost=17,
                    description="Defensive subclass focused on Holy items",
                    effect_description="+2 Block per Holy item, buff/debuff manipulation",
                    unlocked_items=["Holy items", "Banner equipment"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Frozen Flame",
                    subclass_name="Cryomancer",
                    character_class=CharacterClass.PYROMANCER,
                    cost=19,
                    description="Unlocks Ice items and converts Heat to opponent Cold",
                    effect_description="Heat → Cold conversion, unlocks Ice items",
                    unlocked_items=["Book of Ice", "Frozen equipment"],
                    grid_shape=[[True, True], [True, True]]
                )
            ],

            CharacterClass.REAPER: [
                SubclassItem(
                    name="Cauldron",
                    subclass_name="Alchemist",
                    character_class=CharacterClass.REAPER,
                    cost=16,
                    description="Upgrades adjacent potions and provides random resources",
                    effect_description="50% stronger potions, random resource generation",
                    unlocked_items=["Advanced potions", "Alchemical equipment"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Nocturnal Lock Lifter",
                    subclass_name="Vampiress",
                    character_class=CharacterClass.REAPER,
                    cost=18,
                    description="Greatly enhances Vampirism and lifesteal builds",
                    effect_description="Double Vampirism, lifesteal overflow",
                    unlocked_items=["Vampiric equipment", "Night items"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Mr. Struggles",
                    subclass_name="Witch",
                    character_class=CharacterClass.REAPER,
                    cost=17,
                    description="Focuses on Fatigue damage and unlocks Plushies",
                    effect_description="Enhanced fatigue, unlocks Plushie items",
                    unlocked_items=["Mrs. Struggles", "Miss Fortune", "Plushie collection"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Cursed Dagger",
                    subclass_name="Hexblade",
                    character_class=CharacterClass.REAPER,
                    cost=20,
                    description="Powerful stamina-free weapon that excels at debuffs",
                    effect_description="12 damage, no stamina, 80% debuff chance",
                    unlocked_items=["Cursed equipment", "Hex items"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Snake",
                    subclass_name="Venomancer",
                    character_class=CharacterClass.REAPER,
                    cost=15,
                    description="Prevents opponent Poison cleanse and enhances pets",
                    effect_description="Poison cleanse prevention, pet synergies",
                    unlocked_items=["Snake companion", "Venom equipment"],
                    grid_shape=[[True, True], [True, True]]
                )
            ],

            CharacterClass.RANGER: [
                SubclassItem(
                    name="Big Bowl of Treats",
                    subclass_name="Beastmaster",
                    character_class=CharacterClass.RANGER,
                    cost=16,
                    description="Unlocks Friends of the Forest pets and enhances Food/Pet items",
                    effect_description="Unlocks forest pets, +20% activation per friend",
                    unlocked_items=["Rat", "Squirrel", "Hedgehog", "Forest friends"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Yggdrasil Leaf",
                    subclass_name="Lifebinder",
                    character_class=CharacterClass.RANGER,
                    cost=19,
                    description="Powerful healing and support using Mana and Nature items",
                    effect_description="Mana → healing conversion, Nature synergies",
                    unlocked_items=["Nature healing items", "Life equipment"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Piercing Arrow",
                    subclass_name="Hunter",
                    character_class=CharacterClass.RANGER,
                    cost=18,
                    description="Maximizes damage of a single powerful weapon",
                    effect_description="150% damage, 3x crit damage to one weapon",
                    unlocked_items=["Hunter equipment", "Piercing items"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Poison Ivy",
                    subclass_name="Pathfinder",
                    character_class=CharacterClass.RANGER,
                    cost=17,
                    description="Damage-over-time strategy using Spikes and Poison",
                    effect_description="50% more poison damage, Spikes-Poison synergy",
                    unlocked_items=["Poison equipment", "DoT items"],
                    grid_shape=[[True, True], [True, True]]
                ),
                SubclassItem(
                    name="Mega Clover",
                    subclass_name="Grovekeeper",
                    character_class=CharacterClass.RANGER,
                    cost=20,
                    description="Generates Lucky Clovers and provides massive Luck buffs",
                    effect_description="Clover generation, 15 buffs at 10+ Luck",
                    unlocked_items=["Lucky Clover variants", "Luck equipment"],
                    grid_shape=[[True, True], [True, True]]
                )
            ]
        }

    def _initialize_subclass_effects(self) -> Dict[str, SubclassEffect]:
        """Initialize all subclass effect implementations"""
        return {
            # Berserker
            "Fighter": FighterEffect("Fighter", "Brass Knuckles"),
            "Blacksmith": BlacksmithEffect("Blacksmith", "Anvil"),
            "Chieftain": ChieftainEffect("Chieftain", "Deerwood Guardian"),
            "Pack Leader": PackLeaderEffect("Pack Leader", "Wolf Emblem"),
            "Shaman": ShamanEffect("Shaman", "Shaman Mask"),

            # Pyromancer
            "Ashbringer": AshbringerEffect("Ashbringer", "Dark Lantern"),
            "Scalewarden": ScalewardenEffect("Scalewarden", "Dragon Nest"),
            "Firebender": FirebenderEffect("Firebender", "Friendly Fire"),
            "Crusader": CrusaderEffect("Crusader", "Burning Banner"),
            "Cryomancer": CryomancerEffect("Cryomancer", "Frozen Flame"),

            # Reaper
            "Alchemist": AlchemistEffect("Alchemist", "Cauldron"),
            "Vampiress": VampiressEffect("Vampiress", "Nocturnal Lock Lifter"),
            "Witch": WitchEffect("Witch", "Mr. Struggles"),
            "Hexblade": HexbladeEffect("Hexblade", "Cursed Dagger"),
            "Venomancer": VenomancerEffect("Venomancer", "Snake"),

            # Ranger
            "Beastmaster": BeastmasterEffect("Beastmaster", "Big Bowl of Treats"),
            "Lifebinder": LifebinderEffect("Lifebinder", "Yggdrasil Leaf"),
            "Hunter": HunterEffect("Hunter", "Piercing Arrow"),
            "Pathfinder": PathfinderEffect("Pathfinder", "Poison Ivy"),
            "Grovekeeper": GrovekeeperEffect("Grovekeeper", "Mega Clover")
        }

    def _initialize_unlocked_items(self) -> Dict[str, Set[str]]:
        """Map subclasses to their unlocked items"""
        unlocked_items = {}

        for class_items in self.subclass_items.values():
            for item in class_items:
                unlocked_items[item.subclass_name] = set(item.unlocked_items)

        return unlocked_items

    def get_round_8_shop(self, character_class: CharacterClass) -> List[SubclassItem]:
        """Get the 5 subclass items for Round 8 selection"""
        return self.subclass_items.get(character_class, [])

    def select_subclass(self, subclass_item: SubclassItem) -> Dict[str, Any]:
        """Select a subclass and return the unlocked items and effects"""
        effect = self.subclass_effects.get(subclass_item.subclass_name)
        unlocked_items = self.unlocked_items_by_subclass.get(subclass_item.subclass_name, set())

        return {
            "subclass_name": subclass_item.subclass_name,
            "subclass_item": subclass_item,
            "effect": effect,
            "unlocked_items": list(unlocked_items),
            "item_pool_expanded": True
        }

    def apply_subclass_effects(self, subclass_name: str, player_state: Dict[str, Any]) -> Dict[str, Any]:
        """Apply the effects of the selected subclass"""
        effect = self.subclass_effects.get(subclass_name)
        if effect:
            return effect.apply_passive_effect(player_state)
        return {}

    def get_unlocked_items(self, subclass_name: str) -> Set[str]:
        """Get the items unlocked by a specific subclass"""
        return self.unlocked_items_by_subclass.get(subclass_name, set())

    def is_item_unlocked(self, item_name: str, subclass_name: Optional[str]) -> bool:
        """Check if an item is unlocked by the current subclass"""
        if not subclass_name:
            return True  # No subclass restriction

        unlocked = self.get_unlocked_items(subclass_name)
        return item_name in unlocked or not unlocked  # If no specific unlocks, allow all
