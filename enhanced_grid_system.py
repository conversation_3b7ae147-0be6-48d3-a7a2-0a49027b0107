"""
Enhanced Grid System

Implements Section 1.3 improvements from DeepResearch.txt:
- Proper adjacency checking (edge-sharing, not diagonal)
- Rotation-aware placement validation
- Bag expansion mechanics
- Research-compliant grid coordinate system
"""

from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass
from enum import Enum
import json
from research_based_scaling import ResearchCompliantItem, GridDimensions


class AdjacencyType(Enum):
    EDGE = "edge"  # Standard adjacency (shares edge)
    DIAGONAL = "diagonal"  # Diagonal adjacency (shares corner)
    NONE = "none"  # Not adjacent


@dataclass
class GridCell:
    """Represents a single cell in the grid"""
    x: int
    y: int
    occupied_by: Optional[int] = None  # Item instance ID
    is_available: bool = True  # Whether this cell can be used
    bag_source: Optional[str] = None  # Which bag provides this cell


@dataclass
class PlacementResult:
    """Result of attempting to place an item"""
    success: bool
    error_message: str = ""
    conflicting_items: List[int] = None
    
    def __post_init__(self):
        if self.conflicting_items is None:
            self.conflicting_items = []


class EnhancedBackpack:
    """
    Research-compliant backpack implementation following Section 1.3 specifications
    """
    
    def __init__(self, starting_width: int = 3, starting_height: int = 4):
        self.grid_dims = GridDimensions()
        
        # Initialize with starting bag size (class-specific)
        self.cells: Dict[Tuple[int, int], GridCell] = {}
        self.items: Dict[int, ResearchCompliantItem] = {}
        self.item_positions: Dict[int, Tuple[int, int]] = {}  # item_id -> (x, y)
        self.item_rotations: Dict[int, int] = {}  # item_id -> rotation
        
        # Initialize starting cells
        self._initialize_starting_cells(starting_width, starting_height)
        
        # Track bags for expansion
        self.placed_bags: List[int] = []
    
    def _initialize_starting_cells(self, width: int, height: int):
        """Initialize the starting grid cells"""
        for y in range(height):
            for x in range(width):
                self.cells[(x, y)] = GridCell(x, y, bag_source="starting_bag")
    
    def get_available_cells(self) -> Set[Tuple[int, int]]:
        """Get all available (unoccupied) cell coordinates"""
        return {pos for pos, cell in self.cells.items() if cell.is_available and not cell.occupied_by}
    
    def get_occupied_cells(self) -> Set[Tuple[int, int]]:
        """Get all occupied cell coordinates"""
        return {pos for pos, cell in self.cells.items() if cell.occupied_by is not None}
    
    def is_valid_position(self, x: int, y: int) -> bool:
        """Check if a position is within the available grid"""
        return (x, y) in self.cells and self.cells[(x, y)].is_available
    
    def can_place_item(self, item: ResearchCompliantItem, x: int, y: int, 
                      rotation: int = 0, exclude_item: Optional[int] = None) -> PlacementResult:
        """
        Check if an item can be placed at the specified position with rotation
        
        Args:
            item: The item to place
            x, y: Grid coordinates (top-left anchor)
            rotation: Rotation angle (0, 90, 180, 270)
            exclude_item: Item ID to ignore during collision check (for moving items)
        
        Returns:
            PlacementResult with success status and details
        """
        # Get rotated shape
        rotated_shape = item.get_rotated_shape(rotation)
        
        if not rotated_shape:
            return PlacementResult(False, "Item has no valid shape")
        
        shape_height = len(rotated_shape)
        shape_width = len(rotated_shape[0]) if shape_height > 0 else 0
        
        # Check bounds
        conflicting_items = []
        
        for dy in range(shape_height):
            for dx in range(shape_width):
                if rotated_shape[dy][dx]:  # This cell is occupied by the item
                    cell_x, cell_y = x + dx, y + dy
                    
                    # Check if position exists in grid
                    if not self.is_valid_position(cell_x, cell_y):
                        return PlacementResult(False, f"Position ({cell_x}, {cell_y}) is outside available grid")
                    
                    # Check for collision
                    cell = self.cells[(cell_x, cell_y)]
                    if cell.occupied_by is not None and cell.occupied_by != exclude_item:
                        conflicting_items.append(cell.occupied_by)
        
        if conflicting_items:
            return PlacementResult(False, "Collision with existing items", conflicting_items)
        
        return PlacementResult(True)
    
    def place_item(self, item: ResearchCompliantItem, item_id: int, x: int, y: int, 
                  rotation: int = 0) -> PlacementResult:
        """
        Place an item in the backpack
        
        Args:
            item: The item to place
            item_id: Unique identifier for this item instance
            x, y: Grid coordinates (top-left anchor)
            rotation: Rotation angle (0, 90, 180, 270)
        
        Returns:
            PlacementResult with success status
        """
        # Check if placement is valid
        result = self.can_place_item(item, x, y, rotation)
        if not result.success:
            return result
        
        # Get rotated shape
        rotated_shape = item.get_rotated_shape(rotation)
        
        # Place the item
        for dy in range(len(rotated_shape)):
            for dx in range(len(rotated_shape[0])):
                if rotated_shape[dy][dx]:
                    cell_x, cell_y = x + dx, y + dy
                    self.cells[(cell_x, cell_y)].occupied_by = item_id
        
        # Store item data
        self.items[item_id] = item
        self.item_positions[item_id] = (x, y)
        self.item_rotations[item_id] = rotation
        
        # Handle bag expansion
        if item.item_type.lower() == "bag":
            self._expand_grid_for_bag(item, item_id, x, y, rotation)
        
        return PlacementResult(True)
    
    def remove_item(self, item_id: int) -> bool:
        """
        Remove an item from the backpack
        
        Args:
            item_id: ID of the item to remove
        
        Returns:
            True if item was removed, False if not found
        """
        if item_id not in self.items:
            return False
        
        item = self.items[item_id]
        x, y = self.item_positions[item_id]
        rotation = self.item_rotations[item_id]
        
        # Get rotated shape
        rotated_shape = item.get_rotated_shape(rotation)
        
        # Clear grid cells
        for dy in range(len(rotated_shape)):
            for dx in range(len(rotated_shape[0])):
                if rotated_shape[dy][dx]:
                    cell_x, cell_y = x + dx, y + dy
                    if (cell_x, cell_y) in self.cells:
                        self.cells[(cell_x, cell_y)].occupied_by = None
        
        # Remove from tracking
        del self.items[item_id]
        del self.item_positions[item_id]
        del self.item_rotations[item_id]
        
        # Handle bag removal
        if item.item_type.lower() == "bag" and item_id in self.placed_bags:
            self._contract_grid_for_bag(item_id)
        
        return True
    
    def move_item(self, item_id: int, new_x: int, new_y: int, 
                 new_rotation: int = None) -> PlacementResult:
        """
        Move an item to a new position
        
        Args:
            item_id: ID of the item to move
            new_x, new_y: New grid coordinates
            new_rotation: New rotation (None to keep current)
        
        Returns:
            PlacementResult with success status
        """
        if item_id not in self.items:
            return PlacementResult(False, "Item not found")
        
        item = self.items[item_id]
        old_rotation = self.item_rotations[item_id]
        rotation = new_rotation if new_rotation is not None else old_rotation
        
        # Check if new position is valid (excluding current item)
        result = self.can_place_item(item, new_x, new_y, rotation, exclude_item=item_id)
        if not result.success:
            return result
        
        # Remove from old position
        self.remove_item(item_id)
        
        # Place at new position
        return self.place_item(item, item_id, new_x, new_y, rotation)
    
    def check_adjacency(self, item1_id: int, item2_id: int, 
                       adjacency_type: AdjacencyType = AdjacencyType.EDGE) -> bool:
        """
        Check if two items are adjacent according to Section 1.3 rules
        
        Args:
            item1_id, item2_id: IDs of items to check
            adjacency_type: Type of adjacency to check
        
        Returns:
            True if items are adjacent
        """
        if item1_id not in self.items or item2_id not in self.items:
            return False
        
        # Get occupied cells for each item
        cells1 = self._get_item_cells(item1_id)
        cells2 = self._get_item_cells(item2_id)
        
        # Check adjacency
        for x1, y1 in cells1:
            for x2, y2 in cells2:
                if adjacency_type == AdjacencyType.EDGE:
                    # Edge adjacency: Manhattan distance = 1
                    if abs(x1 - x2) + abs(y1 - y2) == 1:
                        return True
                elif adjacency_type == AdjacencyType.DIAGONAL:
                    # Diagonal adjacency: Chebyshev distance = 1
                    if max(abs(x1 - x2), abs(y1 - y2)) == 1 and (x1 != x2 or y1 != y2):
                        return True
        
        return False
    
    def get_adjacent_items(self, item_id: int, 
                          adjacency_type: AdjacencyType = AdjacencyType.EDGE) -> List[int]:
        """
        Get all items adjacent to the specified item
        
        Args:
            item_id: ID of the item to check
            adjacency_type: Type of adjacency to check
        
        Returns:
            List of adjacent item IDs
        """
        adjacent = []
        for other_id in self.items:
            if other_id != item_id and self.check_adjacency(item_id, other_id, adjacency_type):
                adjacent.append(other_id)
        return adjacent
    
    def _get_item_cells(self, item_id: int) -> Set[Tuple[int, int]]:
        """Get all cells occupied by an item"""
        if item_id not in self.items:
            return set()
        
        item = self.items[item_id]
        x, y = self.item_positions[item_id]
        rotation = self.item_rotations[item_id]
        
        rotated_shape = item.get_rotated_shape(rotation)
        cells = set()
        
        for dy in range(len(rotated_shape)):
            for dx in range(len(rotated_shape[0])):
                if rotated_shape[dy][dx]:
                    cells.add((x + dx, y + dy))
        
        return cells
    
    def _expand_grid_for_bag(self, bag_item: ResearchCompliantItem, bag_id: int, 
                           x: int, y: int, rotation: int):
        """
        Expand the grid when a bag is placed (Section 1.3 bag expansion)
        """
        # This is a simplified implementation
        # In a full implementation, each bag type would have specific expansion rules
        
        # For now, add cells around the bag
        bag_cells = self._get_item_cells(bag_id)
        
        # Add expansion cells (simplified - would be bag-specific in full implementation)
        expansion_cells = []
        for bx, by in bag_cells:
            # Add cells around the bag
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    new_x, new_y = bx + dx, by + dy
                    if (new_x, new_y) not in self.cells and 0 <= new_x < self.grid_dims.MAX_WIDTH and 0 <= new_y < self.grid_dims.MAX_HEIGHT:
                        expansion_cells.append((new_x, new_y))
        
        # Add new cells
        for ex, ey in expansion_cells:
            self.cells[(ex, ey)] = GridCell(ex, ey, bag_source=f"bag_{bag_id}")
        
        self.placed_bags.append(bag_id)
    
    def _contract_grid_for_bag(self, bag_id: int):
        """
        Remove grid cells when a bag is removed
        """
        # Remove cells provided by this bag
        cells_to_remove = []
        for pos, cell in self.cells.items():
            if cell.bag_source == f"bag_{bag_id}":
                cells_to_remove.append(pos)
        
        for pos in cells_to_remove:
            del self.cells[pos]
        
        if bag_id in self.placed_bags:
            self.placed_bags.remove(bag_id)
    
    def get_grid_bounds(self) -> Tuple[int, int, int, int]:
        """Get the current grid bounds (min_x, min_y, max_x, max_y)"""
        if not self.cells:
            return (0, 0, 0, 0)
        
        positions = list(self.cells.keys())
        min_x = min(pos[0] for pos in positions)
        max_x = max(pos[0] for pos in positions)
        min_y = min(pos[1] for pos in positions)
        max_y = max(pos[1] for pos in positions)
        
        return (min_x, min_y, max_x, max_y)
    
    def to_dict(self) -> Dict:
        """Export backpack state to dictionary"""
        return {
            "cells": {f"{x},{y}": {
                "occupied_by": cell.occupied_by,
                "is_available": cell.is_available,
                "bag_source": cell.bag_source
            } for (x, y), cell in self.cells.items()},
            "items": {str(item_id): {
                "item_name": item.item_name,
                "position": self.item_positions[item_id],
                "rotation": self.item_rotations[item_id]
            } for item_id, item in self.items.items()},
            "grid_bounds": self.get_grid_bounds()
        }


def demonstrate_enhanced_grid():
    """Demonstrate the enhanced grid system capabilities"""
    print("=== Enhanced Grid System Demonstration ===")
    
    # Create a backpack
    backpack = EnhancedBackpack()
    
    # Create some test items (simplified)
    from research_based_scaling import ResearchCompliantItem, ItemRarity, ItemClass
    
    sword = ResearchCompliantItem(
        item_id="sword_1",
        item_name="Wooden Sword",
        rarity=ItemRarity.COMMON,
        item_class=ItemClass.NEUTRAL,
        item_type="Weapon",
        subtype=[],
        cost=3,
        grid_shape=[[True, True]],  # 2x1 sword
        sockets=0,
        in_shop=True,
        stats={},
        effects=[]
    )
    
    shield = ResearchCompliantItem(
        item_id="shield_1",
        item_name="Wooden Shield",
        rarity=ItemRarity.COMMON,
        item_class=ItemClass.NEUTRAL,
        item_type="Shield",
        subtype=[],
        cost=2,
        grid_shape=[[True], [True]],  # 1x2 shield
        sockets=0,
        in_shop=True,
        stats={},
        effects=[]
    )
    
    # Test placement
    print(f"Available cells: {len(backpack.get_available_cells())}")
    
    # Place sword
    result = backpack.place_item(sword, 1, 0, 0)
    print(f"Placed sword: {result.success}")
    
    # Place shield adjacent to sword
    result = backpack.place_item(shield, 2, 2, 0)
    print(f"Placed shield: {result.success}")
    
    # Check adjacency
    adjacent = backpack.check_adjacency(1, 2)
    print(f"Sword and shield adjacent: {adjacent}")
    
    # Test rotation
    result = backpack.move_item(1, 0, 1, 90)  # Rotate sword 90 degrees
    print(f"Rotated sword: {result.success}")
    
    # Check adjacency after rotation
    adjacent = backpack.check_adjacency(1, 2)
    print(f"Sword and shield adjacent after rotation: {adjacent}")
    
    print(f"Final grid bounds: {backpack.get_grid_bounds()}")


if __name__ == "__main__":
    demonstrate_enhanced_grid()
