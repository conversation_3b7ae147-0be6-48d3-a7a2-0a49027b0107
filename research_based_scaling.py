"""
Research-Based Item Scaling System

Implements the exact specifications from DeepResearch.txt Section 4.1 and Section 1.3
for item scaling, grid management, and placement validation.

Key Research Findings Implemented:
- Section 4.1: Item Data Schema with GridShape as 2D boolean arrays
- Section 1.3: Grid system (9x7 maximum, adjacency rules, rotation)
- Standardized cell dimensions and placement rules
- Proper rotation handling for both placement and scaling
"""

import json
import os
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np
from PIL import Image


class ItemRarity(Enum):
    COMMON = "Common"
    RARE = "Rare"
    EPIC = "Epic"
    LEGENDARY = "Legendary"
    GODLY = "Godly"
    UNIQUE = "Unique"


class ItemClass(Enum):
    NEUTRAL = "Neutral"
    BERSERKER = "Berserker"
    PYROMANCER = "Pyromancer"
    REAPER = "Reaper"
    RANGER = "Ranger"


@dataclass
class GridDimensions:
    """Standardized grid dimensions based on research findings"""
    # From Section 1.3: Maximum potential backpack space is 9x7
    MAX_WIDTH: int = 9
    MAX_HEIGHT: int = 7
    
    # Standard cell dimensions (calibrated from reference images)
    CELL_WIDTH: float = 102.0  # pixels
    CELL_HEIGHT: float = 101.0  # pixels
    
    # Grid positioning (from calibrated coordinates)
    GRID_OFFSET_X: int = 146
    GRID_OFFSET_Y: int = 118


@dataclass
class ResearchCompliantItem:
    """
    Item representation following exact DeepResearch.txt Section 4.1 schema
    """
    item_id: str
    item_name: str
    rarity: ItemRarity
    item_class: ItemClass
    item_type: str
    subtype: List[str]
    cost: int
    grid_shape: List[List[bool]]  # 2D boolean array as specified
    sockets: int
    in_shop: bool
    stats: Dict[str, Any]
    effects: List[Dict[str, Any]]
    
    # Additional fields for scaling
    base_scale: float = 1.0
    rotation_angle: int = 0  # 0, 90, 180, 270 degrees
    
    def get_grid_size(self) -> Tuple[int, int]:
        """Get the (width, height) of the item in grid cells"""
        if not self.grid_shape or not isinstance(self.grid_shape, list) or len(self.grid_shape) == 0:
            return (1, 1)
        height = len(self.grid_shape)
        width = len(self.grid_shape[0]) if height > 0 and len(self.grid_shape[0]) > 0 else 1
        return (width, height)
    
    def get_rotated_shape(self, rotation: int = None) -> List[List[bool]]:
        """
        Get the item shape rotated by specified degrees (0, 90, 180, 270)
        Based on Section 1.3: Items can be rotated in 90-degree increments
        """
        if rotation is None:
            rotation = self.rotation_angle

        shape = self.grid_shape

        # Handle empty or invalid shapes
        if not shape or not isinstance(shape, list) or len(shape) == 0:
            return [[True]]  # Default to 1x1 shape

        # Handle shapes with empty rows
        if len(shape[0]) == 0:
            return [[True]]  # Default to 1x1 shape

        # Normalize rotation to 0, 90, 180, 270
        rotation = rotation % 360

        if rotation == 0:
            return shape
        elif rotation == 90:
            # Rotate 90 degrees clockwise
            height = len(shape)
            width = len(shape[0])
            return [[shape[height - 1 - j][i] for j in range(height)]
                    for i in range(width)]
        elif rotation == 180:
            # Rotate 180 degrees
            height = len(shape)
            width = len(shape[0])
            return [[shape[height - 1 - i][width - 1 - j]
                    for j in range(width)] for i in range(height)]
        elif rotation == 270:
            # Rotate 270 degrees clockwise (90 counter-clockwise)
            height = len(shape)
            width = len(shape[0])
            return [[shape[j][width - 1 - i] for j in range(height)]
                    for i in range(width - 1, -1, -1)]
        else:
            return shape


class ResearchBasedScalingSystem:
    """
    Unified scaling system implementing DeepResearch.txt specifications
    """
    
    def __init__(self, item_images_dir: str = "data/item_images"):
        self.item_images_dir = item_images_dir
        self.grid_dims = GridDimensions()
        self.items: Dict[str, ResearchCompliantItem] = {}
        self.scaling_cache: Dict[str, Dict[str, float]] = {}
        
        # Load existing scaling data if available
        self._load_existing_scaling_data()
    
    def _load_existing_scaling_data(self):
        """Load existing scaling configurations"""
        scaling_files = [
            "item_scaling.json",
            "reference_scaling_data.json"
        ]
        
        for file_path in scaling_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        self.scaling_cache.update(data)
                except Exception as e:
                    print(f"Warning: Could not load {file_path}: {e}")
    
    def calculate_research_based_scale(self, item: ResearchCompliantItem, 
                                     target_cell_fill: float = 0.85) -> float:
        """
        Calculate item scale based on research findings
        
        Args:
            item: The item to scale
            target_cell_fill: How much of the grid cell the item should fill (0.85 = 85%)
        
        Returns:
            Scale factor for the item
        """
        # Get item's grid dimensions
        width_cells, height_cells = item.get_grid_size()
        
        # Calculate expected pixel dimensions
        expected_width = width_cells * self.grid_dims.CELL_WIDTH
        expected_height = height_cells * self.grid_dims.CELL_HEIGHT
        
        # Check if we have image data for more precise scaling
        image_path = os.path.join(self.item_images_dir, f"{item.item_name}.png")
        if os.path.exists(image_path):
            try:
                with Image.open(image_path) as img:
                    # Get actual image dimensions
                    img_width, img_height = img.size
                    
                    # Calculate scale to fit in grid cells with target fill
                    scale_x = (expected_width * target_cell_fill) / img_width
                    scale_y = (expected_height * target_cell_fill) / img_height
                    
                    # Use the smaller scale to ensure item fits
                    scale = min(scale_x, scale_y)
                    
                    return max(0.1, min(2.0, scale))  # Clamp to reasonable range
                    
            except Exception as e:
                print(f"Warning: Could not analyze image for {item.item_name}: {e}")
        
        # Fallback to research-based default
        return target_cell_fill
    
    def get_item_pixel_dimensions(self, item: ResearchCompliantItem, 
                                rotation: int = 0) -> Tuple[int, int]:
        """
        Get the pixel dimensions for an item at a specific rotation
        
        Args:
            item: The item
            rotation: Rotation angle (0, 90, 180, 270)
        
        Returns:
            (width, height) in pixels
        """
        # Get rotated shape
        rotated_shape = item.get_rotated_shape(rotation)
        
        # Calculate dimensions
        height_cells = len(rotated_shape)
        width_cells = len(rotated_shape[0]) if height_cells > 0 else 1
        
        # Get scale factor
        scale = self.calculate_research_based_scale(item)
        
        # Calculate pixel dimensions
        pixel_width = int(width_cells * self.grid_dims.CELL_WIDTH * scale)
        pixel_height = int(height_cells * self.grid_dims.CELL_HEIGHT * scale)
        
        return (pixel_width, pixel_height)
    
    def get_grid_position_pixels(self, grid_x: int, grid_y: int) -> Tuple[int, int]:
        """
        Convert grid coordinates to pixel coordinates
        
        Args:
            grid_x: Grid column (0-8)
            grid_y: Grid row (0-6)
        
        Returns:
            (pixel_x, pixel_y) coordinates
        """
        pixel_x = self.grid_dims.GRID_OFFSET_X + (grid_x * self.grid_dims.CELL_WIDTH)
        pixel_y = self.grid_dims.GRID_OFFSET_Y + (grid_y * self.grid_dims.CELL_HEIGHT)
        
        return (int(pixel_x), int(pixel_y))
    
    def validate_placement(self, item: ResearchCompliantItem, grid_x: int, grid_y: int,
                          rotation: int = 0, grid_state: List[List[int]] = None) -> bool:
        """
        Validate if an item can be placed at the specified position
        
        Args:
            item: The item to place
            grid_x: Grid column
            grid_y: Grid row
            rotation: Rotation angle
            grid_state: Current grid state (None for no collision check)
        
        Returns:
            True if placement is valid
        """
        # Get rotated shape
        rotated_shape = item.get_rotated_shape(rotation)
        
        # Check bounds
        shape_height = len(rotated_shape)
        shape_width = len(rotated_shape[0]) if shape_height > 0 else 1
        
        if (grid_x + shape_width > self.grid_dims.MAX_WIDTH or 
            grid_y + shape_height > self.grid_dims.MAX_HEIGHT):
            return False
        
        # Check collision if grid state provided
        if grid_state is not None:
            for y in range(shape_height):
                for x in range(shape_width):
                    if rotated_shape[y][x] and grid_state[grid_y + y][grid_x + x] != 0:
                        return False
        
        return True
    
    def check_adjacency(self, item1_pos: Tuple[int, int], item1_shape: List[List[bool]],
                       item2_pos: Tuple[int, int], item2_shape: List[List[bool]]) -> bool:
        """
        Check if two items are adjacent (edge-sharing, not diagonal)
        Based on Section 1.3: Standard adjacency is defined as sharing a grid edge
        
        Args:
            item1_pos: (x, y) position of first item
            item1_shape: Shape of first item
            item2_pos: (x, y) position of second item
            item2_shape: Shape of second item
        
        Returns:
            True if items are adjacent
        """
        x1, y1 = item1_pos
        x2, y2 = item2_pos
        
        # Get all occupied cells for each item
        cells1 = set()
        for y in range(len(item1_shape)):
            for x in range(len(item1_shape[0])):
                if item1_shape[y][x]:
                    cells1.add((x1 + x, y1 + y))
        
        cells2 = set()
        for y in range(len(item2_shape)):
            for x in range(len(item2_shape[0])):
                if item2_shape[y][x]:
                    cells2.add((x2 + x, y2 + y))
        
        # Check for edge adjacency (not diagonal)
        for x, y in cells1:
            # Check four adjacent directions
            adjacent_cells = [(x+1, y), (x-1, y), (x, y+1), (x, y-1)]
            for adj_cell in adjacent_cells:
                if adj_cell in cells2:
                    return True
        
        return False
    
    def save_scaling_configuration(self, output_path: str = "research_based_scaling.json"):
        """Save the current scaling configuration"""
        config = {
            "grid_dimensions": {
                "max_width": self.grid_dims.MAX_WIDTH,
                "max_height": self.grid_dims.MAX_HEIGHT,
                "cell_width": self.grid_dims.CELL_WIDTH,
                "cell_height": self.grid_dims.CELL_HEIGHT,
                "grid_offset_x": self.grid_dims.GRID_OFFSET_X,
                "grid_offset_y": self.grid_dims.GRID_OFFSET_Y
            },
            "scaling_cache": self.scaling_cache,
            "research_compliance": {
                "section_1_3": "Grid system with 9x7 maximum, edge adjacency",
                "section_4_1": "Item schema with GridShape as 2D boolean arrays"
            }
        }
        
        with open(output_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Saved research-based scaling configuration to {output_path}")


def migrate_existing_items_to_research_schema(json_path: str = "data/Backpack Battle Items.json") -> Dict[str, ResearchCompliantItem]:
    """
    Migrate existing item data to research-compliant schema
    
    Args:
        json_path: Path to existing item data
    
    Returns:
        Dictionary of research-compliant items
    """
    if not os.path.exists(json_path):
        print(f"Warning: Item data file not found at {json_path}")
        return {}
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        items = {}
        
        if 'enhanced_items' in data:
            for item_data in data['enhanced_items']:
                name = item_data.get('name')
                if not name:
                    continue
                
                # Convert grid_layout to boolean array
                grid_layout = item_data.get('grid_layout', [])
                grid_shape = []

                # Validate and convert grid layout
                if grid_layout and isinstance(grid_layout, list):
                    for row in grid_layout:
                        if isinstance(row, list):
                            bool_row = []
                            for cell in row:
                                # Convert to boolean: 'cell' = True, anything else = False
                                bool_row.append(cell == 'cell')
                            if bool_row:  # Only add non-empty rows
                                grid_shape.append(bool_row)

                # Ensure we have a valid grid shape
                if not grid_shape:
                    grid_shape = [[True]]  # Default to 1x1 if no valid shape found
                
                # Parse rarity safely
                rarity_str = item_data.get('rarity', 'Common')
                try:
                    rarity = ItemRarity(rarity_str)
                except ValueError:
                    # Handle non-standard rarities
                    rarity_map = {
                        'Varies': ItemRarity.COMMON,
                        'varies': ItemRarity.COMMON,
                        'Variable': ItemRarity.COMMON,
                        'N/A': ItemRarity.COMMON
                    }
                    rarity = rarity_map.get(rarity_str, ItemRarity.COMMON)

                # Parse cost safely
                cost_str = item_data.get('cost', '0')
                try:
                    cost = int(cost_str) if cost_str.isdigit() else 0
                except (ValueError, AttributeError):
                    cost = 0

                # Create research-compliant item
                item = ResearchCompliantItem(
                    item_id=str(hash(name) % 10000),
                    item_name=name,
                    rarity=rarity,
                    item_class=ItemClass.NEUTRAL,  # Default, can be enhanced
                    item_type=item_data.get('type', 'Unknown'),
                    subtype=[],
                    cost=cost,
                    grid_shape=grid_shape,
                    sockets=0,
                    in_shop=True,
                    stats={},
                    effects=[]
                )
                
                items[name] = item
        
        print(f"Migrated {len(items)} items to research-compliant schema")
        return items
        
    except Exception as e:
        print(f"Error migrating items: {e}")
        return {}


if __name__ == "__main__":
    # Example usage
    scaling_system = ResearchBasedScalingSystem()
    
    # Migrate existing items
    items = migrate_existing_items_to_research_schema()
    
    # Test scaling for a few items
    for name, item in list(items.items())[:5]:
        scale = scaling_system.calculate_research_based_scale(item)
        dimensions = scaling_system.get_item_pixel_dimensions(item)
        print(f"{name}: scale={scale:.3f}, dimensions={dimensions}")
    
    # Save configuration
    scaling_system.save_scaling_configuration()
