"""
Test the Character Classes and Starting Bags System
"""

from simulator.character_classes import CharacterClassManager, CharacterClass
from simulator.status_effects import StatusEffectManager

def test_character_classes():
    print("🏛️ TESTING CHARACTER CLASSES SYSTEM")
    print("=" * 60)
    
    manager = CharacterClassManager()
    
    # Test all character classes
    for char_class in CharacterClass:
        print(f"\n🎭 {char_class.value.upper()}:")
        print("-" * 30)
        
        choices = manager.get_starting_bag_choices(char_class)
        
        for i, choice in enumerate(choices, 1):
            print(f"  Option {i}: {choice.name}")
            print(f"    Description: {choice.description}")
            print(f"    Grid Size: {choice.grid_size}")
            print(f"    Base Slots: {choice.base_slots}")
            print(f"    Effect: {choice.effect_description}")
            print()
    
    # Test specific bag effects
    print("\n🧪 TESTING BAG EFFECTS:")
    print("=" * 40)
    
    status_manager = StatusEffectManager()
    
    # Test Ranger Bag with different Luck levels
    print("\n🏹 RANGER BAG EFFECT:")
    ranger_bag = manager.get_bag_effect("Ranger Bag")
    
    # Test with 0 Luck
    player_state = {"health": 100, "max_health": 100}
    items_in_bag = [1, 2, 3]  # Mock items
    
    result = ranger_bag.apply_effect(player_state, status_manager, items_in_bag)
    print(f"  With 0 Luck: {result['crit_chance_bonus']:.1%} crit chance")
    
    # Test with 4 Luck stacks
    status_manager.add_effect("Luck", stacks=4)
    result = ranger_bag.apply_effect(player_state, status_manager, items_in_bag)
    print(f"  With 4 Luck: {result['crit_chance_bonus']:.1%} crit chance")
    
    # Test Storage Coffin poison chance
    print("\n⚰️ STORAGE COFFIN EFFECT:")
    coffin = manager.get_bag_effect("Storage Coffin")
    
    poison_triggers = 0
    trials = 1000
    for _ in range(trials):
        result = coffin.on_item_activation(None, player_state)
        if result.get("inflict_poison"):
            poison_triggers += 1
    
    poison_rate = poison_triggers / trials
    print(f"  Poison trigger rate: {poison_rate:.1%} (expected: 25%)")
    
    # Test Duffle Bag Battle Rage
    print("\n🎒 DUFFLE BAG EFFECT:")
    duffle = manager.get_bag_effect("Duffle Bag")
    
    # Test at full health
    player_state = {"health": 100, "max_health": 100}
    result = duffle.apply_effect(player_state, status_manager, items_in_bag)
    print(f"  At full health: Battle Rage = {result.get('battle_rage_active', False)}")
    
    # Test at low health
    player_state = {"health": 40, "max_health": 100}
    result = duffle.apply_effect(player_state, status_manager, items_in_bag)
    print(f"  At 40% health: Battle Rage = {result.get('battle_rage_active', False)}")
    if result.get('battle_rage_active'):
        print(f"    Cooldown multiplier: {result.get('cooldown_multiplier', 1.0)}")
        print(f"    Damage reduction: {result.get('damage_reduction', 0.0):.0%}")
    
    # Test Fire Pit HP bonus
    print("\n🔥 FIRE PIT EFFECT:")
    fire_pit = manager.get_bag_effect("Fire Pit")
    
    # Mock Fire items
    class MockFireItem:
        def __init__(self):
            self.subtype = ['Fire']
    
    fire_items = [MockFireItem() for _ in range(3)]
    result = fire_pit.apply_effect(player_state, status_manager, fire_items)
    print(f"  With 3 Fire items: +{result.get('max_health_bonus', 0)} max health")
    
    # Test battle start effect
    start_result = fire_pit.on_battle_start(player_state)
    print(f"  Battle start: Generate Flame = {start_result.get('generate_flame_offer', False)}")
    print(f"  Flame cost: {start_result.get('flame_cost', 0)} gold")
    
    print("\n" + "=" * 60)
    print("✅ CHARACTER CLASSES TEST COMPLETE")

if __name__ == "__main__":
    test_character_classes()
