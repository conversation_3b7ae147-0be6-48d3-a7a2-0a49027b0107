"""
Integrated Scaling System Replacement

This module provides a unified interface that replaces all the fragmented scaling approaches
with the research-compliant system. It maintains backward compatibility while providing
enhanced functionality based on DeepResearch.txt findings.

Key Improvements:
- Replaces multiple inconsistent scaling systems
- Provides unified API for all scaling operations
- Maintains backward compatibility with existing code
- Implements research-based scaling as the default
- Provides migration utilities for existing data
"""

import os
import json
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from unified_research_system import UnifiedResearchSystem, UnifiedSystemConfig
from research_based_scaling import ResearchBasedScalingSystem, ResearchCompliantItem


@dataclass
class LegacyScalingData:
    """Represents legacy scaling data for backward compatibility"""
    scale: float
    offset_x: int = 0
    offset_y: int = 0
    legacy_method: str = "unknown"


class IntegratedScalingSystem:
    """
    Unified scaling system that replaces all fragmented approaches
    
    This class provides a single interface for all scaling operations while
    maintaining backward compatibility with existing systems.
    """
    
    def __init__(self, config: UnifiedSystemConfig = None):
        self.config = config or UnifiedSystemConfig()
        
        # Initialize the research-based system
        self.research_system = UnifiedResearchSystem(config)
        
        # Load legacy scaling data for compatibility
        self.legacy_data = self._load_legacy_scaling_data()
        
        # Migration tracking
        self.migration_log = []
        
        print(f"Integrated Scaling System initialized")
        print(f"Research-compliant items: {len(self.research_system.items)}")
        print(f"Legacy scaling entries: {len(self.legacy_data)}")
    
    def _load_legacy_scaling_data(self) -> Dict[str, LegacyScalingData]:
        """Load existing scaling data for backward compatibility"""
        legacy_data = {}
        
        # Load from various legacy sources
        legacy_files = [
            self.config.scaling_config_path,
            "reference_scaling_data.json",
            "manual_scaling_data.json"
        ]
        
        for file_path in legacy_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                    
                    for item_name, item_data in data.items():
                        if item_name == "default" or not isinstance(item_data, dict):
                            continue
                        
                        # Convert to standardized format
                        scale = item_data.get('scale', 1.0)
                        offset_x = item_data.get('offset_x', 0)
                        offset_y = item_data.get('offset_y', 0)
                        method = item_data.get('legacy_method', f'from_{os.path.basename(file_path)}')
                        
                        legacy_data[item_name] = LegacyScalingData(
                            scale=scale,
                            offset_x=offset_x,
                            offset_y=offset_y,
                            legacy_method=method
                        )
                        
                except Exception as e:
                    print(f"Warning: Could not load legacy data from {file_path}: {e}")
        
        return legacy_data
    
    def get_item_scale(self, item_name: str, use_research_based: bool = True) -> float:
        """
        Get the scale factor for an item
        
        Args:
            item_name: Name of the item
            use_research_based: Whether to use research-based scaling (recommended)
        
        Returns:
            Scale factor for the item
        """
        # Normalize item name (handle underscores)
        normalized_name = item_name.replace('_', ' ')
        
        if use_research_based:
            # Try research-based scaling first
            item = self.research_system.get_item_by_name(item_name) or self.research_system.get_item_by_name(normalized_name)
            if item:
                return self.research_system.scaling_system.calculate_research_based_scale(item)
        
        # Fall back to legacy data
        legacy = self.legacy_data.get(item_name) or self.legacy_data.get(normalized_name)
        if legacy:
            if use_research_based:
                # Log migration opportunity
                self.migration_log.append(f"Using legacy scale for {item_name}: {legacy.scale}")
            return legacy.scale
        
        # Default fallback
        return 0.85  # Research-based default
    
    def get_item_dimensions(self, item_name: str, rotation: int = 0, 
                           use_research_based: bool = True) -> Tuple[int, int]:
        """
        Get pixel dimensions for an item
        
        Args:
            item_name: Name of the item
            rotation: Rotation angle (0, 90, 180, 270)
            use_research_based: Whether to use research-based calculations
        
        Returns:
            (width, height) in pixels
        """
        normalized_name = item_name.replace('_', ' ')
        
        if use_research_based:
            item = self.research_system.get_item_by_name(item_name) or self.research_system.get_item_by_name(normalized_name)
            if item:
                return self.research_system.scaling_system.get_item_pixel_dimensions(item, rotation)
        
        # Legacy fallback - estimate based on scale and default size
        scale = self.get_item_scale(item_name, use_research_based=False)
        default_size = 102  # Default cell size
        
        # Simple estimation for legacy items
        return (int(default_size * scale), int(default_size * scale))
    
    def get_grid_position(self, grid_x: int, grid_y: int) -> Tuple[int, int]:
        """
        Convert grid coordinates to pixel coordinates
        
        Args:
            grid_x, grid_y: Grid coordinates
        
        Returns:
            (pixel_x, pixel_y) coordinates
        """
        return self.research_system.scaling_system.get_grid_position_pixels(grid_x, grid_y)
    
    def validate_item_placement(self, item_name: str, grid_x: int, grid_y: int, 
                              rotation: int = 0, grid_state: List[List[int]] = None) -> bool:
        """
        Validate if an item can be placed at the specified position
        
        Args:
            item_name: Name of the item
            grid_x, grid_y: Grid coordinates
            rotation: Rotation angle
            grid_state: Current grid state (for collision detection)
        
        Returns:
            True if placement is valid
        """
        normalized_name = item_name.replace('_', ' ')
        item = self.research_system.get_item_by_name(item_name) or self.research_system.get_item_by_name(normalized_name)
        
        if item:
            return self.research_system.scaling_system.validate_placement(
                item, grid_x, grid_y, rotation, grid_state
            )
        
        # Legacy fallback - basic bounds checking
        if grid_state is None:
            return 0 <= grid_x < 9 and 0 <= grid_y < 7  # Basic grid bounds
        
        # Simple collision check for legacy items (assume 1x1)
        return (0 <= grid_x < len(grid_state[0]) and 
                0 <= grid_y < len(grid_state) and 
                grid_state[grid_y][grid_x] == 0)
    
    def check_item_adjacency(self, item1_name: str, pos1: Tuple[int, int], 
                           item2_name: str, pos2: Tuple[int, int]) -> bool:
        """
        Check if two items are adjacent using research-based rules
        
        Args:
            item1_name, item2_name: Names of the items
            pos1, pos2: Grid positions of the items
        
        Returns:
            True if items are adjacent (edge-sharing)
        """
        item1 = self.research_system.get_item_by_name(item1_name.replace('_', ' '))
        item2 = self.research_system.get_item_by_name(item2_name.replace('_', ' '))
        
        if item1 and item2:
            return self.research_system.scaling_system.check_adjacency(
                pos1, item1.grid_shape, pos2, item2.grid_shape
            )
        
        # Legacy fallback - simple adjacency check
        x1, y1 = pos1
        x2, y2 = pos2
        return abs(x1 - x2) + abs(y1 - y2) == 1  # Manhattan distance = 1
    
    def migrate_legacy_system(self, target_file: str = "migrated_scaling_config.json") -> Dict[str, Any]:
        """
        Migrate legacy scaling data to research-based system
        
        Args:
            target_file: Output file for migrated configuration
        
        Returns:
            Migration report
        """
        migration_report = {
            "total_legacy_items": len(self.legacy_data),
            "successfully_migrated": 0,
            "research_enhanced": 0,
            "legacy_preserved": 0,
            "migration_details": []
        }
        
        migrated_config = {
            "system_info": {
                "migrated_from": "Legacy fragmented systems",
                "migration_date": "2025-01-27",
                "research_compliance": "DeepResearch.txt Sections 1.3 and 4.1"
            },
            "items": {}
        }
        
        for item_name, legacy_data in self.legacy_data.items():
            normalized_name = item_name.replace('_', ' ')
            item = self.research_system.get_item_by_name(item_name) or self.research_system.get_item_by_name(normalized_name)
            
            if item:
                # Research-enhanced migration
                research_scale = self.research_system.scaling_system.calculate_research_based_scale(item)
                pixel_dims = self.research_system.scaling_system.get_item_pixel_dimensions(item)
                
                migrated_config["items"][item_name] = {
                    "scale": research_scale,
                    "pixel_dimensions": pixel_dims,
                    "grid_size": item.get_grid_size(),
                    "research_based": True,
                    "legacy_scale": legacy_data.scale,
                    "improvement": abs(research_scale - legacy_data.scale)
                }
                
                migration_report["research_enhanced"] += 1
                migration_report["migration_details"].append({
                    "item": item_name,
                    "status": "research_enhanced",
                    "legacy_scale": legacy_data.scale,
                    "new_scale": research_scale
                })
                
            else:
                # Preserve legacy data
                migrated_config["items"][item_name] = {
                    "scale": legacy_data.scale,
                    "offset_x": legacy_data.offset_x,
                    "offset_y": legacy_data.offset_y,
                    "research_based": False,
                    "legacy_method": legacy_data.legacy_method
                }
                
                migration_report["legacy_preserved"] += 1
                migration_report["migration_details"].append({
                    "item": item_name,
                    "status": "legacy_preserved",
                    "reason": "No research data available"
                })
            
            migration_report["successfully_migrated"] += 1
        
        # Save migrated configuration
        with open(target_file, 'w') as f:
            json.dump(migrated_config, f, indent=2)
        
        print(f"Migration completed: {migration_report['successfully_migrated']} items processed")
        print(f"Research-enhanced: {migration_report['research_enhanced']}")
        print(f"Legacy preserved: {migration_report['legacy_preserved']}")
        
        return migration_report
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive status of the integrated system"""
        return {
            "research_system": {
                "items_loaded": len(self.research_system.items),
                "backpacks_created": len(self.research_system.backpacks),
                "scaling_system_active": True
            },
            "legacy_compatibility": {
                "legacy_items": len(self.legacy_data),
                "migration_opportunities": len([item for item in self.legacy_data.keys() 
                                              if item.replace('_', ' ') in self.research_system.items]),
                "migration_log_entries": len(self.migration_log)
            },
            "features": {
                "research_based_scaling": True,
                "rotation_support": True,
                "adjacency_validation": True,
                "grid_coordinate_conversion": True,
                "backward_compatibility": True
            }
        }


# Backward compatibility functions for existing code
def get_item_scale_legacy_compatible(item_name: str) -> float:
    """Legacy-compatible function for getting item scale"""
    warnings.warn("Using legacy scaling function. Consider migrating to IntegratedScalingSystem.", 
                  DeprecationWarning, stacklevel=2)
    
    system = IntegratedScalingSystem()
    return system.get_item_scale(item_name)


def get_corrected_item_dimensions_legacy_compatible(item_name: str, grid_width: int, grid_height: int, 
                                                   original_image_size: Tuple[int, int]) -> Tuple[int, int, float]:
    """Legacy-compatible function for getting corrected dimensions"""
    warnings.warn("Using legacy dimension function. Consider migrating to IntegratedScalingSystem.", 
                  DeprecationWarning, stacklevel=2)
    
    system = IntegratedScalingSystem()
    scale = system.get_item_scale(item_name)
    width, height = system.get_item_dimensions(item_name)
    
    return (width, height, scale)


def demonstrate_integrated_system():
    """Demonstrate the integrated scaling system"""
    print("=== Integrated Scaling System Demonstration ===")
    
    # Initialize system
    system = IntegratedScalingSystem()
    
    # Test various scaling operations
    test_items = ["Wooden Sword", "Axe", "Health Potion", "Dagger"]
    
    print("\nItem Scaling Comparison:")
    for item_name in test_items:
        research_scale = system.get_item_scale(item_name, use_research_based=True)
        legacy_scale = system.get_item_scale(item_name, use_research_based=False)
        dimensions = system.get_item_dimensions(item_name)
        
        print(f"{item_name}:")
        print(f"  Research scale: {research_scale:.3f}")
        print(f"  Legacy scale: {legacy_scale:.3f}")
        print(f"  Dimensions: {dimensions}")
        print(f"  Improvement: {abs(research_scale - legacy_scale):.3f}")
    
    # Test placement validation
    print("\nPlacement Validation:")
    for item_name in test_items[:2]:
        valid = system.validate_item_placement(item_name, 0, 0)
        print(f"{item_name} at (0,0): {valid}")
    
    # Test adjacency
    print("\nAdjacency Testing:")
    adjacent = system.check_item_adjacency("Wooden Sword", (0, 0), "Axe", (2, 0))
    print(f"Wooden Sword at (0,0) adjacent to Axe at (2,0): {adjacent}")
    
    # Migration report
    print("\nMigration Analysis:")
    migration_report = system.migrate_legacy_system()
    
    # System status
    print("\nSystem Status:")
    status = system.get_system_status()
    print(f"Research items: {status['research_system']['items_loaded']}")
    print(f"Legacy items: {status['legacy_compatibility']['legacy_items']}")
    print(f"Migration opportunities: {status['legacy_compatibility']['migration_opportunities']}")


if __name__ == "__main__":
    demonstrate_integrated_system()
