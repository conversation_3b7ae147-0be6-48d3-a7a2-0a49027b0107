"""
Test the Enhanced Recipe Engine
"""

from simulator.enhanced_recipe_engine import EnhancedRecipeEngine, GridPosition, PlacedItem
from simulator.enhanced_item_database import EnhancedItem, ItemRarity, ItemClass, ItemType

def create_test_item(name: str, grid_shape: list = None) -> EnhancedItem:
    """Create a test item for recipe testing"""
    if grid_shape is None:
        grid_shape = [[True]]
    
    return EnhancedItem(
        item_id=f"test_{name}",
        item_name=name,
        rarity=ItemRarity.COMMON,
        item_class=ItemClass.NEUTRAL,
        item_type=ItemType.WEAPON,
        subtype=[],
        cost=10,
        grid_shape=grid_shape,
        sockets=0,
        in_shop=True,
        stats={},
        effects=[],
        description=f"Test item: {name}"
    )

def test_enhanced_recipe_engine():
    print("🔧 TESTING ENHANCED RECIPE ENGINE")
    print("=" * 60)
    
    engine = EnhancedRecipeEngine()
    
    # Test recipe loading
    print(f"\n📚 RECIPE DATABASE:")
    print("-" * 30)
    print(f"  Loaded {len(engine.recipes)} recipes")
    
    # Show some example recipes
    for i, recipe in enumerate(engine.recipes[:5]):
        print(f"  {i+1}. {recipe.result_item}")
        print(f"     Ingredients: {', '.join(recipe.ingredients)}")
        if recipe.catalyst:
            print(f"     Catalyst: {recipe.catalyst}")
        if recipe.required_class:
            print(f"     Class: {recipe.required_class}")
        print()
    
    # Test adjacency detection
    print("\n🔗 TESTING ADJACENCY DETECTION:")
    print("-" * 30)
    
    # Create test items
    wooden_sword = create_test_item("Wooden Sword")
    whetstone1 = create_test_item("Whetstone")
    whetstone2 = create_test_item("Whetstone")
    
    # Place items adjacently
    engine.add_item(wooden_sword, GridPosition(1, 1))
    engine.add_item(whetstone1, GridPosition(2, 1))  # Adjacent to sword
    engine.add_item(whetstone2, GridPosition(3, 1))  # Adjacent to whetstone1
    
    print(f"  Placed {len(engine.placed_items)} items on grid")
    
    # Test adjacency groups
    groups = engine.find_adjacent_groups()
    print(f"  Found {len(groups)} adjacent groups:")
    for i, group in enumerate(groups):
        item_names = [item.item.item_name for item in group]
        print(f"    Group {i+1}: {', '.join(item_names)}")
    
    # Test recipe detection
    print("\n🍳 TESTING RECIPE DETECTION:")
    print("-" * 30)
    
    valid_recipes = engine.find_valid_recipes()
    print(f"  Found {len(valid_recipes)} valid recipes:")
    
    for recipe, ingredients, catalyst in valid_recipes:
        ingredient_names = [item.item.item_name for item in ingredients]
        print(f"    {recipe.result_item}")
        print(f"      Ingredients: {', '.join(ingredient_names)}")
        if catalyst:
            print(f"      Catalyst: {catalyst.item.item_name}")
    
    # Test recipe execution
    print("\n⚡ TESTING RECIPE EXECUTION:")
    print("-" * 30)
    
    items_before = len(engine.placed_items)
    results = engine.process_combinations()
    items_after = len(engine.placed_items)
    
    print(f"  Items before: {items_before}")
    print(f"  Items after: {items_after}")
    print(f"  Results: {results}")
    
    # Test combination locks
    print("\n🔒 TESTING COMBINATION LOCKS:")
    print("-" * 30)
    
    # Add new items for lock testing
    dagger = create_test_item("Dagger")
    pestilence = create_test_item("Pestilence Flask")
    
    engine.add_item(dagger, GridPosition(5, 1))
    engine.add_item(pestilence, GridPosition(6, 1))
    
    # Lock the dagger
    engine.toggle_combination_lock("Dagger")
    locked_items = engine.get_locked_items()
    print(f"  Locked items: {[item.item.item_name for item in locked_items]}")
    
    # Try to find recipes (should be blocked by lock)
    valid_recipes_locked = engine.find_valid_recipes()
    print(f"  Valid recipes with lock: {len(valid_recipes_locked)}")
    
    # Unlock and try again
    engine.toggle_combination_lock("Dagger")
    valid_recipes_unlocked = engine.find_valid_recipes()
    print(f"  Valid recipes after unlock: {len(valid_recipes_unlocked)}")
    
    # Test catalyst recipes
    print("\n🧪 TESTING CATALYST RECIPES:")
    print("-" * 30)
    
    # Find a catalyst recipe
    catalyst_recipes = [r for r in engine.recipes if r.catalyst]
    if catalyst_recipes:
        recipe = catalyst_recipes[0]
        print(f"  Testing recipe: {recipe.result_item}")
        print(f"    Ingredients: {', '.join(recipe.ingredients)}")
        print(f"    Catalyst: {recipe.catalyst}")
        
        # Create the required items
        if recipe.ingredients and recipe.catalyst:
            # Clear the grid first
            engine.placed_items.clear()
            
            # Add ingredient
            ingredient_item = create_test_item(recipe.ingredients[0])
            catalyst_item = create_test_item(recipe.catalyst)
            
            engine.add_item(ingredient_item, GridPosition(1, 1))
            engine.add_item(catalyst_item, GridPosition(2, 1))
            
            valid_catalyst_recipes = engine.find_valid_recipes()
            print(f"    Found {len(valid_catalyst_recipes)} valid catalyst recipes")
    
    # Test grid position utilities
    print("\n📍 TESTING GRID POSITION UTILITIES:")
    print("-" * 30)
    
    pos1 = GridPosition(1, 1)
    pos2 = GridPosition(2, 1)  # Adjacent
    pos3 = GridPosition(3, 3)  # Not adjacent
    
    print(f"  Position (1,1) adjacent to (2,1): {pos1.is_adjacent_to(pos2)}")
    print(f"  Position (1,1) adjacent to (3,3): {pos1.is_adjacent_to(pos3)}")
    
    adjacent_to_pos1 = pos1.get_adjacent_positions()
    print(f"  Adjacent positions to (1,1): {[(p.x, p.y) for p in adjacent_to_pos1]}")
    
    # Test recipe suggestions
    print("\n💡 TESTING RECIPE SUGGESTIONS:")
    print("-" * 30)
    
    available_items = ["Wooden Sword", "Whetstone", "Dagger", "Health Potion"]
    suggestions = engine.get_recipe_suggestions(available_items)
    
    print(f"  With items: {', '.join(available_items)}")
    print(f"  Recipe suggestions: {len(suggestions)}")
    for suggestion in suggestions[:3]:  # Show first 3
        print(f"    {suggestion.result_item}: {', '.join(suggestion.ingredients)}")
    
    # Test complex item shapes
    print("\n🔲 TESTING COMPLEX ITEM SHAPES:")
    print("-" * 30)
    
    # Create a 2x2 item
    large_item = create_test_item("Large Shield", [[True, True], [True, True]])
    engine.placed_items.clear()
    engine.add_item(large_item, GridPosition(0, 0))
    
    occupied_positions = engine.placed_items[0].get_occupied_positions()
    print(f"  Large Shield occupies: {[(p.x, p.y) for p in occupied_positions]}")
    
    # Test position lookup
    item_at_00 = engine.get_item_at_position(GridPosition(0, 0))
    item_at_11 = engine.get_item_at_position(GridPosition(1, 1))
    item_at_22 = engine.get_item_at_position(GridPosition(2, 2))
    
    print(f"  Item at (0,0): {item_at_00.item.item_name if item_at_00 else 'None'}")
    print(f"  Item at (1,1): {item_at_11.item.item_name if item_at_11 else 'None'}")
    print(f"  Item at (2,2): {item_at_22.item.item_name if item_at_22 else 'None'}")
    
    # Test placement order
    print("\n📋 TESTING PLACEMENT ORDER:")
    print("-" * 30)
    
    engine.placed_items.clear()
    items_to_place = ["First Item", "Second Item", "Third Item"]
    
    for i, item_name in enumerate(items_to_place):
        item = create_test_item(item_name)
        engine.add_item(item, GridPosition(i, 0))
    
    print("  Placement order:")
    for item in engine.placed_items:
        print(f"    {item.placement_order}: {item.item.item_name}")
    
    print("\n" + "=" * 60)
    print("✅ ENHANCED RECIPE ENGINE TEST COMPLETE")

if __name__ == "__main__":
    test_enhanced_recipe_engine()
