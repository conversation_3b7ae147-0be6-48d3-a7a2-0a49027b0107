"""
Test the Enhanced Item Database System
"""

from simulator.enhanced_item_database import EnhancedItemDatabase, ItemRarity, ItemClass, ItemType

def test_enhanced_database():
    print("🗄️ TESTING ENHANCED ITEM DATABASE")
    print("=" * 60)
    
    # Load the database
    db = EnhancedItemDatabase()
    
    # Validate completeness
    validation = db.validate_completeness()
    
    print(f"📊 DATABASE STATISTICS:")
    print(f"  Total items: {validation['total_items']}")
    print()
    
    print("📈 BY RARITY:")
    for rarity, count in validation['by_rarity'].items():
        print(f"  {rarity}: {count}")
    print()
    
    print("🏛️ BY CLASS:")
    for item_class, count in validation['by_class'].items():
        print(f"  {item_class}: {count}")
    print()
    
    print("🔧 BY TYPE:")
    for item_type, count in validation['by_type'].items():
        if count > 0:
            print(f"  {item_type}: {count}")
    print()
    
    # Show some sample items
    print("🔍 SAMPLE ITEMS:")
    sample_count = 0
    for item in db.items.values():
        if sample_count >= 5:
            break
        
        print(f"\n  {item.item_name}:")
        print(f"    Rarity: {item.rarity.value}")
        print(f"    Class: {item.item_class.value}")
        print(f"    Type: {item.item_type.value}")
        print(f"    Cost: {item.cost}")
        print(f"    Grid Size: {item.get_grid_size()}")
        print(f"    Sockets: {item.sockets}")
        print(f"    In Shop: {item.in_shop}")
        print(f"    Effects: {len(item.effects)}")
        if item.effects:
            for i, effect in enumerate(item.effects[:2]):  # Show first 2 effects
                print(f"      Effect {i+1}: {effect.trigger.value} -> {effect.effect_type}")
        sample_count += 1
    
    # Test specific queries
    print(f"\n🔍 QUERY TESTS:")
    weapons = db.get_items_by_type(ItemType.WEAPON)
    print(f"  Weapons: {len(weapons)}")
    
    berserker_items = db.get_items_by_class(ItemClass.BERSERKER)
    print(f"  Berserker items: {len(berserker_items)}")
    
    legendary_items = db.get_items_by_rarity(ItemRarity.LEGENDARY)
    print(f"  Legendary items: {len(legendary_items)}")
    
    shop_items = db.get_shop_items()
    print(f"  Shop items: {len(shop_items)}")
    
    # Check for issues
    print(f"\n⚠️ VALIDATION ISSUES:")
    print(f"  Items missing effects: {len(validation['missing_effects'])}")
    if validation['missing_effects'][:5]:  # Show first 5
        print(f"    Examples: {', '.join(validation['missing_effects'][:5])}")
    
    print(f"  Items missing stats: {len(validation['missing_stats'])}")
    if validation['missing_stats'][:5]:  # Show first 5
        print(f"    Examples: {', '.join(validation['missing_stats'][:5])}")
    
    print("\n" + "=" * 60)
    print("✅ ENHANCED DATABASE TEST COMPLETE")

if __name__ == "__main__":
    test_enhanced_database()
