import os
import json
from PIL import Image
from research_based_scaling import ResearchBasedScalingSystem, migrate_existing_items_to_research_schema

def analyze_item_images_research_based(item_dir, scaling_config_path):
    """
    Analyzes all item images using research-based scaling system from DeepResearch.txt.
    Implements Section 4.1 item schema and Section 1.3 grid system specifications.

    Args:
        item_dir (str): The directory containing item images.
        scaling_config_path (str): The path to the item scaling JSON file.
    """
    if not os.path.exists(item_dir):
        print(f"Error: Item directory not found at '{item_dir}'")
        return

    # Initialize research-based scaling system
    scaling_system = ResearchBasedScalingSystem(item_dir)

    # Load research-compliant items
    items = migrate_existing_items_to_research_schema()

    # Load existing scaling configuration for compatibility
    if os.path.exists(scaling_config_path):
        with open(scaling_config_path, 'r') as f:
            scaling_data = json.load(f)
    else:
        scaling_data = {
            "default": {
                "scale": 0.85,  # Research-based default (85% cell fill)
                "offset_x": 0,
                "offset_y": 0
            },
            "research_metadata": {
                "grid_system": "Section 1.3 - 9x7 maximum grid",
                "item_schema": "Section 4.1 - GridShape as 2D boolean arrays",
                "cell_dimensions": "102x101 pixels (calibrated)"
            }
        }

    print(f"Analyzing images in '{item_dir}' using research-based scaling...")

    processed_count = 0
    research_based_count = 0

    # Iterate through all PNG files in the directory
    for filename in os.listdir(item_dir):
        if filename.endswith(".png"):
            item_name = os.path.splitext(filename)[0]

            # Skip if this item already has a specific entry (to preserve manual tweaks)
            if item_name in scaling_data and item_name != "default" and "research_based" not in scaling_data[item_name]:
                continue

            image_path = os.path.join(item_dir, filename)

            try:
                # Try research-based scaling first
                if item_name in items:
                    item = items[item_name]
                    research_scale = scaling_system.calculate_research_based_scale(item)
                    grid_size = item.get_grid_size()
                    pixel_dims = scaling_system.get_item_pixel_dimensions(item)

                    scaling_data[item_name] = {
                        "scale": round(research_scale, 3),
                        "research_based": True,
                        "grid_size": grid_size,
                        "pixel_dimensions": pixel_dims,
                        "rotation_support": True
                    }
                    research_based_count += 1

                else:
                    # Fallback to legacy bounding box analysis
                    with Image.open(image_path) as img:
                        # Ensure image has an alpha channel
                        if img.mode != 'RGBA':
                            img = img.convert('RGBA')

                        # Get the bounding box of the non-transparent parts
                        bbox = img.getbbox()

                        if bbox:
                            # The actual content dimensions
                            content_width = bbox[2] - bbox[0]
                            content_height = bbox[3] - bbox[1]

                            # The total image dimensions
                            total_width = img.width
                            total_height = img.height

                            # Calculate how much of the image is taken up by the actual content
                            scale_x = content_width / total_width
                            scale_y = content_height / total_height

                            # Use research-based target fill (85%) instead of max ratio
                            scale = min(scale_x, scale_y) * 0.85

                            scaling_data[item_name] = {
                                "scale": round(scale, 3),
                                "research_based": False,
                                "legacy_method": "bounding_box"
                            }
                        else:
                            # Handle fully transparent images
                            scaling_data[item_name] = {
                                "scale": 0.85,
                                "research_based": False,
                                "note": "fully_transparent"
                            }

                processed_count += 1

            except Exception as e:
                print(f"Could not process {filename}: {e}")

    # Write the updated configuration back to the file
    with open(scaling_config_path, 'w') as f:
        json.dump(scaling_data, f, indent=2, sort_keys=True)

    print(f"Successfully updated '{scaling_config_path}' with scales for {processed_count} items.")
    print(f"Research-based scaling: {research_based_count} items")
    print(f"Legacy scaling: {processed_count - research_based_count} items")

    # Save research-based configuration separately
    scaling_system.save_scaling_configuration("research_scaling_config.json")


def analyze_item_images(item_dir, scaling_config_path):
    """
    Legacy function for backward compatibility.
    Calls the research-based version.
    """
    return analyze_item_images_research_based(item_dir, scaling_config_path)


def demonstrate_research_improvements():
    """
    Demonstrate the improvements from research-based scaling
    """
    print("=== Research-Based Item Scaling Demonstration ===")

    # Initialize systems
    scaling_system = ResearchBasedScalingSystem()
    items = migrate_existing_items_to_research_schema()

    print(f"Loaded {len(items)} research-compliant items")

    # Test a few items
    test_items = ["Wooden Sword", "Axe", "Dagger", "Shield", "Health Potion"]

    for item_name in test_items:
        if item_name in items:
            item = items[item_name]

            print(f"\n{item_name}:")
            print(f"  Grid size: {item.get_grid_size()}")
            print(f"  Research scale: {scaling_system.calculate_research_based_scale(item):.3f}")

            # Test rotation
            for rotation in [0, 90, 180, 270]:
                dims = scaling_system.get_item_pixel_dimensions(item, rotation)
                print(f"  {rotation}° rotation: {dims} pixels")

            # Test placement validation
            valid_pos = scaling_system.validate_placement(item, 0, 0, 0)
            invalid_pos = scaling_system.validate_placement(item, 8, 6, 0)  # Near edge
            print(f"  Placement (0,0): {valid_pos}, Placement (8,6): {invalid_pos}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--demo":
        demonstrate_research_improvements()
    else:
        ITEM_IMAGE_DIRECTORY = "data/item_images"
        SCALING_CONFIG_FILE = "item_scaling.json"
        analyze_item_images(ITEM_IMAGE_DIRECTORY, SCALING_CONFIG_FILE)
