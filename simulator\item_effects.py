"""
Item Effect Parsing and Execution System

Converts item effect text from the database into executable functions
that integrate with the enhanced simulation systems.
"""

import re
import json
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass
from enum import Enum

from simulator.status_effects import StatusEffectManager
from simulator.resources import ResourceManager
from simulator.combat_events import CombatEventManager, EventType


class EffectTrigger(Enum):
    """When an effect triggers"""
    START_OF_BATTLE = "start_of_battle"
    ON_HIT = "on_hit"
    ON_ATTACK = "on_attack"
    ITEM_ACTIVATION = "item_activation"
    DAMAGE_DEALT = "damage_dealt"
    DAMAGE_TAKEN = "damage_taken"
    BUFF_USED = "buff_used"
    POTION_CONSUMED = "potion_consumed"
    GAME_STARTED = "game_started"
    PERIODIC = "periodic"


@dataclass
class ParsedEffect:
    """Represents a parsed item effect"""
    trigger: EffectTrigger
    condition: Optional[str]  # e.g., "6 item activations", "10 damage dealt"
    effect_type: str  # e.g., "heal", "damage", "buff", "debuff"
    magnitude: Optional[int]  # Numeric value
    target: str  # "self", "opponent", "all"
    duration: Optional[float]  # For timed effects
    probability: Optional[float]  # For chance-based effects
    raw_text: str  # Original effect text


class ItemEffectParser:
    """Parses item effect text into structured, executable effects"""
    
    def __init__(self):
        # Common patterns for parsing effects
        self.patterns = {
            'start_of_battle': r'start of battle:?\s*(.+?)(?:\.|$)',
            'on_hit': r'on hit:?\s*(.+?)(?:\.|$)',
            'on_attack': r'on attack:?\s*(.+?)(?:\.|$)',
            'item_activation': r'(\d+)\s+item activations?:?\s*(.+?)(?:\.|$)',
            'damage_dealt': r'(\d+)\s*-?damage dealt:?\s*(.+?)(?:\.|$)',
            'damage_taken': r'(\d+)\s*damage taken:?\s*(.+?)(?:\.|$)',
            'buff_used': r'buff used:?\s*(.+?)(?:\.|$)',
            'potion_consumed': r'potion consumed:?\s*(.+?)(?:\.|$)',
            'game_started': r'game started:?\s*(.+?)(?:\.|$)',
            'periodic': r'every\s+(\d+(?:\.\d+)?)\s*s:?\s*(.+?)(?:\.|$)',
            
            # Effect patterns
            'heal': r'heal\s+(?:for\s+)?(\d+)',
            'damage': r'deal\s+(\d+)\s+.*?damage',
            'inflict_status': r'inflict\s+(\d+)\s+(.+?)(?:\s+for\s+(\d+(?:\.\d+)?)s)?',
            'gain_status': r'gain\s+(\d+)\s+(.+?)(?:\s+for\s+(\d+(?:\.\d+)?)s)?',
            'probability': r'(\d+)%\s+chance',
            'duration': r'for\s+(\d+(?:\.\d+)?)s',
            'random_buffs': r'(\d+)\s+.*?random\s+buffs?',
            'random_debuffs': r'(\d+)\s+.*?random\s+debuffs?',
        }
        
        # Status effect mappings from emojis/symbols to names
        self.status_mappings = {
            '💪': 'Empower',
            '🔥': 'Heat',
            '🍀': 'Luck',
            '💚': 'Regeneration',
            '🛡️': 'Block',
            '🧛': 'Vampirism',
            '🌟': 'Spikes',
            '☠️': 'Poison',
            '🥶': 'Cold',
            '👁️': 'Blind',
            '⚡': 'Stun',
            '🛡️': 'Invulnerability',
            '❄️': 'Cold',
        }
    
    def parse_effect_text(self, effect_text: str) -> List[ParsedEffect]:
        """Parse effect text into structured effects"""
        if not effect_text:
            return []
        
        effects = []
        
        # Split by periods and parse each part
        parts = [part.strip() for part in effect_text.split('.') if part.strip()]
        
        for part in parts:
            parsed = self._parse_single_effect(part)
            if parsed:
                effects.append(parsed)
        
        return effects
    
    def _parse_single_effect(self, text: str) -> Optional[ParsedEffect]:
        """Parse a single effect statement"""
        text = text.lower().strip()
        
        # Determine trigger
        trigger = None
        condition = None
        effect_part = text
        
        for trigger_name, pattern in self.patterns.items():
            if trigger_name in ['heal', 'damage', 'inflict_status', 'gain_status', 
                              'probability', 'duration', 'random_buffs', 'random_debuffs']:
                continue  # These are effect patterns, not triggers
                
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                trigger = EffectTrigger(trigger_name)
                if trigger_name in ['item_activation', 'damage_dealt', 'damage_taken', 'periodic']:
                    condition = match.group(1)
                    effect_part = match.group(2)
                else:
                    effect_part = match.group(1)
                break
        
        if not trigger:
            # Default to item activation if no specific trigger found
            trigger = EffectTrigger.ITEM_ACTIVATION
        
        # Parse the effect part
        effect_type, magnitude, target, duration, probability = self._parse_effect_details(effect_part)
        
        return ParsedEffect(
            trigger=trigger,
            condition=condition,
            effect_type=effect_type,
            magnitude=magnitude,
            target=target,
            duration=duration,
            probability=probability,
            raw_text=text
        )
    
    def _parse_effect_details(self, effect_text: str) -> Tuple[str, Optional[int], str, Optional[float], Optional[float]]:
        """Parse the details of an effect"""
        effect_type = "unknown"
        magnitude = None
        target = "self"
        duration = None
        probability = None
        
        # Check for probability
        prob_match = re.search(self.patterns['probability'], effect_text)
        if prob_match:
            probability = float(prob_match.group(1)) / 100.0
        
        # Check for duration
        dur_match = re.search(self.patterns['duration'], effect_text)
        if dur_match:
            duration = float(dur_match.group(1))
        
        # Check for heal
        heal_match = re.search(self.patterns['heal'], effect_text)
        if heal_match:
            effect_type = "heal"
            magnitude = int(heal_match.group(1))
            return effect_type, magnitude, target, duration, probability
        
        # Check for damage
        damage_match = re.search(self.patterns['damage'], effect_text)
        if damage_match:
            effect_type = "damage"
            magnitude = int(damage_match.group(1))
            target = "opponent"
            return effect_type, magnitude, target, duration, probability
        
        # Check for status effects
        inflict_match = re.search(self.patterns['inflict_status'], effect_text)
        if inflict_match:
            effect_type = "inflict_status"
            magnitude = int(inflict_match.group(1))
            target = "opponent"
            # Parse status type from emojis/text
            return effect_type, magnitude, target, duration, probability
        
        gain_match = re.search(self.patterns['gain_status'], effect_text)
        if gain_match:
            effect_type = "gain_status"
            magnitude = int(gain_match.group(1))
            target = "self"
            return effect_type, magnitude, target, duration, probability
        
        # Check for random buffs/debuffs
        random_buff_match = re.search(self.patterns['random_buffs'], effect_text)
        if random_buff_match:
            effect_type = "random_buffs"
            magnitude = int(random_buff_match.group(1))
            return effect_type, magnitude, target, duration, probability
        
        random_debuff_match = re.search(self.patterns['random_debuffs'], effect_text)
        if random_debuff_match:
            effect_type = "random_debuffs"
            magnitude = int(random_debuff_match.group(1))
            target = "opponent"
            return effect_type, magnitude, target, duration, probability
        
        return effect_type, magnitude, target, duration, probability


class ItemEffectExecutor:
    """Executes parsed item effects in the simulation"""
    
    def __init__(self, status_manager: StatusEffectManager, 
                 resource_manager: ResourceManager,
                 event_manager: CombatEventManager):
        self.status_manager = status_manager
        self.resource_manager = resource_manager
        self.event_manager = event_manager
        
        # Available status effects for random selection
        self.random_buffs = ['Empower', 'Heat', 'Luck', 'Regeneration', 'Vampirism', 'Spikes']
        self.random_debuffs = ['Poison', 'Cold', 'Blind', 'Stun']
    
    def execute_effect(self, effect: ParsedEffect, caster_id: int, target_id: int, 
                      current_time: float) -> bool:
        """Execute a parsed effect"""
        import random
        
        # Check probability
        if effect.probability and random.random() > effect.probability:
            return False
        
        if effect.effect_type == "heal":
            return self._execute_heal(effect, caster_id, current_time)
        elif effect.effect_type == "damage":
            return self._execute_damage(effect, target_id, current_time)
        elif effect.effect_type == "gain_status":
            return self._execute_gain_status(effect, caster_id, current_time)
        elif effect.effect_type == "inflict_status":
            return self._execute_inflict_status(effect, target_id, current_time)
        elif effect.effect_type == "random_buffs":
            return self._execute_random_buffs(effect, caster_id, current_time)
        elif effect.effect_type == "random_debuffs":
            return self._execute_random_debuffs(effect, target_id, current_time)
        
        return False
    
    def _execute_heal(self, effect: ParsedEffect, target_id: int, current_time: float) -> bool:
        """Execute a healing effect"""
        # This would integrate with the player's health system
        # For now, just log the effect
        print(f"Player {target_id} healed for {effect.magnitude}")
        return True
    
    def _execute_damage(self, effect: ParsedEffect, target_id: int, current_time: float) -> bool:
        """Execute a damage effect"""
        # This would integrate with the damage system
        print(f"Player {target_id} takes {effect.magnitude} damage")
        return True
    
    def _execute_gain_status(self, effect: ParsedEffect, target_id: int, current_time: float) -> bool:
        """Execute gaining a status effect"""
        # This would need to determine which status effect based on the text
        print(f"Player {target_id} gains status effect (magnitude: {effect.magnitude})")
        return True
    
    def _execute_inflict_status(self, effect: ParsedEffect, target_id: int, current_time: float) -> bool:
        """Execute inflicting a status effect"""
        print(f"Player {target_id} receives debuff (magnitude: {effect.magnitude})")
        return True
    
    def _execute_random_buffs(self, effect: ParsedEffect, target_id: int, current_time: float) -> bool:
        """Execute random buff effect"""
        import random
        selected_buffs = random.sample(self.random_buffs, min(effect.magnitude, len(self.random_buffs)))
        for buff in selected_buffs:
            self.status_manager.add_effect(buff, stacks=1, duration=effect.duration)
        return True
    
    def _execute_random_debuffs(self, effect: ParsedEffect, target_id: int, current_time: float) -> bool:
        """Execute random debuff effect"""
        import random
        selected_debuffs = random.sample(self.random_debuffs, min(effect.magnitude, len(self.random_debuffs)))
        for debuff in selected_debuffs:
            # This would apply to the target's status manager
            print(f"Applied {debuff} to player {target_id}")
        return True
