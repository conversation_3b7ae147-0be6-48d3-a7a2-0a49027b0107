"""
Test the Reward Function Design
"""

from rl.reward_function_design import (
    BasicRewardFunction, AdvancedRewardFunction, CurriculumRewardFunction,
    RewardFunctionManager, RewardType
)

def create_test_states():
    """Create test states for reward function testing"""
    prev_state = {
        "phase": "shop",
        "round": 3,
        "health": 80,
        "gold": 25,
        "lives": 3,
        "backpack_items": [
            {
                "name": "Wooden Sword",
                "type": "weapon",
                "tags": ["weapon", "melee"],
                "position": (1, 1),
                "power_level": 1.5
            },
            {
                "name": "Health Potion",
                "type": "potion", 
                "tags": ["potion", "healing"],
                "position": (0, 0),
                "power_level": 1.0
            }
        ],
        "inventory_items": [],
        "shop_items": [
            {"name": "Hero Sword", "cost": 20, "power_level": 3.0, "type": "weapon"},
            {"name": "Leather Armor", "cost": 15, "power_level": 2.0, "type": "armor"},
            None, None, None
        ],
        "occupied_cells": {(0, 0), (1, 1)},
        "stamina": 100,
        "mana": 10
    }
    
    # State after buying Hero Sword
    new_state = prev_state.copy()
    new_state["gold"] = 5  # Spent 20 gold
    new_state["backpack_items"] = prev_state["backpack_items"] + [
        {
            "name": "Hero Sword",
            "type": "weapon",
            "tags": ["weapon", "melee", "sword"],
            "position": (2, 1),
            "power_level": 3.0
        }
    ]
    new_state["occupied_cells"] = {(0, 0), (1, 1), (2, 1), (3, 1)}
    
    return prev_state, new_state

def test_reward_function_design():
    print("🎯 TESTING REWARD FUNCTION DESIGN")
    print("=" * 60)
    
    # Test basic reward function
    print("\n🔰 TESTING BASIC REWARD FUNCTION:")
    print("-" * 30)
    
    basic_rf = BasicRewardFunction()
    
    print(f"  Components: {len(basic_rf.components)}")
    for name, component in basic_rf.components.items():
        print(f"    {name}: weight={component.weight}, type={component.reward_type.value}")
    
    print(f"  Total weight: {basic_rf.total_weight}")
    
    # Test reward calculation
    prev_state, new_state = create_test_states()
    
    action = {
        "action_type": "buy_item",
        "item_name": "Hero Sword",
        "item_cost": 20,
        "item_value": 3.0
    }
    
    info = {
        "action_successful": True,
        "item_value": 3.0,
        "battle_completed": False
    }
    
    basic_rewards = basic_rf.calculate_reward(prev_state, action, new_state, info)
    basic_total = basic_rf.get_total_reward(prev_state, action, new_state, info)
    
    print(f"\n  Basic reward breakdown:")
    for name, value in basic_rewards.items():
        print(f"    {name}: {value:.3f}")
    print(f"  Total reward: {basic_total:.3f}")
    
    # Test advanced reward function
    print("\n🚀 TESTING ADVANCED REWARD FUNCTION:")
    print("-" * 30)
    
    advanced_rf = AdvancedRewardFunction()
    
    print(f"  Components: {len(advanced_rf.components)}")
    
    # Group by reward type
    by_type = {}
    for name, component in advanced_rf.components.items():
        reward_type = component.reward_type.value
        if reward_type not in by_type:
            by_type[reward_type] = []
        by_type[reward_type].append((name, component.weight))
    
    for reward_type, components in by_type.items():
        print(f"    {reward_type.upper()}:")
        for name, weight in components:
            print(f"      {name}: {weight}")
    
    print(f"  Total weight: {advanced_rf.total_weight}")
    
    # Test advanced reward calculation
    advanced_rewards = advanced_rf.calculate_reward(prev_state, action, new_state, info)
    advanced_total = advanced_rf.get_total_reward(prev_state, action, new_state, info)
    
    print(f"\n  Advanced reward breakdown:")
    for name, value in advanced_rewards.items():
        if abs(value) > 0.001:  # Only show non-zero rewards
            print(f"    {name}: {value:.3f}")
    print(f"  Total reward: {advanced_total:.3f}")
    
    # Test different action types
    print("\n🎬 TESTING DIFFERENT ACTION TYPES:")
    print("-" * 30)
    
    # Test placement action
    placement_action = {
        "action_type": "place_item",
        "item_name": "Dagger",
        "x": 3,
        "y": 0
    }
    
    placement_info = {"action_successful": True}
    
    placement_rewards = advanced_rf.calculate_reward(prev_state, placement_action, new_state, placement_info)
    placement_total = advanced_rf.get_total_reward(prev_state, placement_action, new_state, placement_info)
    
    print(f"  Placement action reward: {placement_total:.3f}")
    print(f"    Key components:")
    for name, value in placement_rewards.items():
        if abs(value) > 0.001:
            print(f"      {name}: {value:.3f}")
    
    # Test shop reroll action
    reroll_action = {
        "action_type": "reroll_shop",
        "cost": 2
    }
    
    reroll_state = prev_state.copy()
    reroll_state["gold"] = 23  # Spent 2 gold on reroll
    
    reroll_rewards = advanced_rf.calculate_reward(prev_state, reroll_action, reroll_state, placement_info)
    reroll_total = advanced_rf.get_total_reward(prev_state, reroll_action, reroll_state, placement_info)
    
    print(f"\n  Reroll action reward: {reroll_total:.3f}")
    
    # Test battle outcome
    print("\n⚔️ TESTING BATTLE OUTCOMES:")
    print("-" * 30)
    
    battle_action = {"action_type": "end_turn"}
    
    # Test win
    win_info = {
        "action_successful": True,
        "battle_completed": True,
        "battle_won": True,
        "damage_dealt": 50,
        "damage_taken": 10
    }
    
    win_rewards = advanced_rf.calculate_reward(prev_state, battle_action, new_state, win_info)
    win_total = advanced_rf.get_total_reward(prev_state, battle_action, new_state, win_info)
    
    print(f"  Battle win reward: {win_total:.3f}")
    print(f"    Battle performance: {win_rewards.get('battle_performance', 0):.3f}")
    
    # Test loss
    loss_info = {
        "action_successful": True,
        "battle_completed": True,
        "battle_won": False,
        "damage_dealt": 20,
        "damage_taken": 60
    }
    
    loss_rewards = advanced_rf.calculate_reward(prev_state, battle_action, new_state, loss_info)
    loss_total = advanced_rf.get_total_reward(prev_state, battle_action, new_state, loss_info)
    
    print(f"  Battle loss reward: {loss_total:.3f}")
    print(f"    Battle performance: {loss_rewards.get('battle_performance', 0):.3f}")
    
    # Test curriculum reward function
    print("\n📚 TESTING CURRICULUM REWARD FUNCTION:")
    print("-" * 30)
    
    # Test at different difficulty levels
    difficulties = [0.0, 0.5, 1.0]
    
    for difficulty in difficulties:
        curriculum_rf = CurriculumRewardFunction(difficulty)
        curriculum_total = curriculum_rf.get_total_reward(prev_state, action, new_state, info)
        
        print(f"  Difficulty {difficulty}: reward = {curriculum_total:.3f}")
    
    # Test curriculum adaptation
    adaptive_curriculum = CurriculumRewardFunction(0.0)
    
    print(f"\n  Adaptive curriculum:")
    for step in [0.0, 0.3, 0.7, 1.0]:
        adaptive_curriculum.update_difficulty(step)
        adaptive_total = adaptive_curriculum.get_total_reward(prev_state, action, new_state, info)
        print(f"    Step {step}: reward = {adaptive_total:.3f}")
    
    # Test reward function manager
    print("\n🎛️ TESTING REWARD FUNCTION MANAGER:")
    print("-" * 30)
    
    manager = RewardFunctionManager()
    
    print(f"  Available functions: {list(manager.functions.keys())}")
    print(f"  Current function: {manager.current_function}")
    
    # Test switching functions
    for func_name in ["basic", "advanced", "curriculum"]:
        manager.set_function(func_name)
        total_reward = manager.calculate_reward(prev_state, action, new_state, info)
        breakdown = manager.get_reward_breakdown(prev_state, action, new_state, info)
        
        print(f"\n  {func_name.upper()} function:")
        print(f"    Total reward: {total_reward:.3f}")
        print(f"    Non-zero components: {sum(1 for v in breakdown.values() if abs(v) > 0.001)}")
    
    # Test invalid actions
    print("\n❌ TESTING INVALID ACTIONS:")
    print("-" * 30)
    
    invalid_action = {
        "action_type": "buy_item",
        "item_name": "Expensive Item",
        "item_cost": 100  # More than available gold
    }
    
    invalid_info = {
        "action_successful": False,
        "error": "insufficient_gold"
    }
    
    manager.set_function("advanced")
    invalid_total = manager.calculate_reward(prev_state, invalid_action, prev_state, invalid_info)
    invalid_breakdown = manager.get_reward_breakdown(prev_state, invalid_action, prev_state, invalid_info)
    
    print(f"  Invalid action reward: {invalid_total:.3f}")
    print(f"  Action validity component: {invalid_breakdown.get('action_validity', 0):.3f}")
    
    # Test edge cases
    print("\n🔍 TESTING EDGE CASES:")
    print("-" * 30)
    
    # Empty state
    empty_state = {
        "phase": "shop",
        "round": 1,
        "health": 100,
        "gold": 10,
        "lives": 3,
        "backpack_items": [],
        "inventory_items": [],
        "shop_items": [None] * 5,
        "occupied_cells": set()
    }
    
    empty_action = {"action_type": "pass"}
    empty_info = {"action_successful": True}
    
    empty_total = manager.calculate_reward(empty_state, empty_action, empty_state, empty_info)
    print(f"  Empty state pass action: {empty_total:.3f}")
    
    # Game completion
    completion_info = {
        "action_successful": True,
        "game_completed": True,
        "final_rank": 2,
        "total_players": 8,
        "won_game": False
    }
    
    completion_total = manager.calculate_reward(prev_state, action, new_state, completion_info)
    completion_breakdown = manager.get_reward_breakdown(prev_state, action, new_state, completion_info)
    
    print(f"  Game completion (2nd place): {completion_total:.3f}")
    print(f"  Final ranking component: {completion_breakdown.get('final_ranking', 0):.3f}")
    
    # Test curriculum difficulty update
    print("\n📈 TESTING CURRICULUM DIFFICULTY UPDATE:")
    print("-" * 30)
    
    manager.set_function("curriculum")
    
    print("  Difficulty progression:")
    for i, difficulty in enumerate([0.0, 0.25, 0.5, 0.75, 1.0]):
        manager.update_curriculum_difficulty(difficulty)
        reward = manager.calculate_reward(prev_state, action, new_state, info)
        print(f"    Level {i+1} (diff={difficulty}): {reward:.3f}")
    
    print("\n" + "=" * 60)
    print("✅ REWARD FUNCTION DESIGN TEST COMPLETE")

if __name__ == "__main__":
    test_reward_function_design()
