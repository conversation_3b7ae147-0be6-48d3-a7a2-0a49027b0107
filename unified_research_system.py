"""
Unified Research-Based System

Combines all research findings from DeepResearch.txt into a single, cohesive system:
- Section 1.3: Grid system with proper adjacency and rotation
- Section 4.1: Item schema with GridShape as 2D boolean arrays
- Research-based scaling and placement
- Unified coordinate system and validation
"""

import json
import os
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from research_based_scaling import (
    ResearchBasedScalingSystem, 
    ResearchCompliantItem, 
    migrate_existing_items_to_research_schema,
    GridDimensions
)
from enhanced_grid_system import EnhancedBackpack, PlacementResult, AdjacencyType


@dataclass
class UnifiedSystemConfig:
    """Configuration for the unified research-based system"""
    item_images_dir: str = "data/item_images"
    item_data_path: str = "data/Backpack Battle Items.json"
    scaling_config_path: str = "item_scaling.json"
    research_config_path: str = "research_scaling_config.json"
    
    # Grid settings
    default_cell_fill: float = 0.85
    enable_bag_expansion: bool = True
    enable_rotation: bool = True
    
    # Validation settings
    strict_adjacency: bool = True  # Use edge adjacency only (Section 1.3)
    validate_placement: bool = True


class UnifiedResearchSystem:
    """
    Complete research-based system integrating all DeepResearch.txt findings
    """
    
    def __init__(self, config: UnifiedSystemConfig = None):
        self.config = config or UnifiedSystemConfig()
        
        # Initialize subsystems
        self.scaling_system = ResearchBasedScalingSystem(self.config.item_images_dir)
        self.grid_dims = GridDimensions()
        
        # Load research-compliant items
        self.items = migrate_existing_items_to_research_schema(self.config.item_data_path)
        
        # Create backpack instances
        self.backpacks: Dict[str, EnhancedBackpack] = {}
        
        print(f"Unified Research System initialized with {len(self.items)} items")
    
    def create_backpack(self, backpack_id: str, starting_width: int = 3, 
                       starting_height: int = 4) -> EnhancedBackpack:
        """
        Create a new research-compliant backpack
        
        Args:
            backpack_id: Unique identifier for the backpack
            starting_width, starting_height: Initial grid size
        
        Returns:
            EnhancedBackpack instance
        """
        backpack = EnhancedBackpack(starting_width, starting_height)
        self.backpacks[backpack_id] = backpack
        return backpack
    
    def get_item_by_name(self, item_name: str) -> Optional[ResearchCompliantItem]:
        """Get an item by name"""
        return self.items.get(item_name)
    
    def calculate_item_placement_data(self, item_name: str, rotation: int = 0) -> Dict:
        """
        Calculate complete placement data for an item
        
        Args:
            item_name: Name of the item
            rotation: Rotation angle (0, 90, 180, 270)
        
        Returns:
            Dictionary with placement data
        """
        item = self.get_item_by_name(item_name)
        if not item:
            return {"error": f"Item '{item_name}' not found"}
        
        # Get scaling data
        scale = self.scaling_system.calculate_research_based_scale(item)
        pixel_dims = self.scaling_system.get_item_pixel_dimensions(item, rotation)
        
        # Get grid data
        grid_size = item.get_grid_size()
        rotated_shape = item.get_rotated_shape(rotation)
        
        return {
            "item_name": item_name,
            "grid_size": grid_size,
            "rotated_grid_size": (len(rotated_shape[0]), len(rotated_shape)) if rotated_shape else (0, 0),
            "scale_factor": scale,
            "pixel_dimensions": pixel_dims,
            "grid_shape": rotated_shape,
            "rotation": rotation,
            "research_compliant": True
        }
    
    def validate_item_placement(self, backpack_id: str, item_name: str, 
                              x: int, y: int, rotation: int = 0) -> PlacementResult:
        """
        Validate if an item can be placed in a backpack
        
        Args:
            backpack_id: ID of the backpack
            item_name: Name of the item to place
            x, y: Grid coordinates
            rotation: Rotation angle
        
        Returns:
            PlacementResult with validation details
        """
        if backpack_id not in self.backpacks:
            return PlacementResult(False, f"Backpack '{backpack_id}' not found")
        
        item = self.get_item_by_name(item_name)
        if not item:
            return PlacementResult(False, f"Item '{item_name}' not found")
        
        backpack = self.backpacks[backpack_id]
        return backpack.can_place_item(item, x, y, rotation)
    
    def place_item_in_backpack(self, backpack_id: str, item_name: str, item_id: int,
                              x: int, y: int, rotation: int = 0) -> PlacementResult:
        """
        Place an item in a backpack
        
        Args:
            backpack_id: ID of the backpack
            item_name: Name of the item to place
            item_id: Unique ID for this item instance
            x, y: Grid coordinates
            rotation: Rotation angle
        
        Returns:
            PlacementResult with placement status
        """
        if backpack_id not in self.backpacks:
            return PlacementResult(False, f"Backpack '{backpack_id}' not found")
        
        item = self.get_item_by_name(item_name)
        if not item:
            return PlacementResult(False, f"Item '{item_name}' not found")
        
        backpack = self.backpacks[backpack_id]
        return backpack.place_item(item, item_id, x, y, rotation)
    
    def find_optimal_placement(self, backpack_id: str, item_name: str, 
                             prefer_rotation: bool = True) -> Optional[Tuple[int, int, int]]:
        """
        Find optimal placement for an item in a backpack
        
        Args:
            backpack_id: ID of the backpack
            item_name: Name of the item to place
            prefer_rotation: Whether to try rotations for better fit
        
        Returns:
            (x, y, rotation) tuple if placement found, None otherwise
        """
        if backpack_id not in self.backpacks:
            return None
        
        item = self.get_item_by_name(item_name)
        if not item:
            return None
        
        backpack = self.backpacks[backpack_id]
        available_cells = backpack.get_available_cells()
        
        # Try different rotations
        rotations = [0, 90, 180, 270] if prefer_rotation else [0]
        
        for rotation in rotations:
            rotated_shape = item.get_rotated_shape(rotation)
            if not rotated_shape:
                continue
            
            shape_height = len(rotated_shape)
            shape_width = len(rotated_shape[0]) if shape_height > 0 else 0
            
            # Try each available position
            for x, y in available_cells:
                result = backpack.can_place_item(item, x, y, rotation)
                if result.success:
                    return (x, y, rotation)
        
        return None
    
    def analyze_backpack_synergies(self, backpack_id: str) -> Dict:
        """
        Analyze synergies in a backpack based on adjacency rules
        
        Args:
            backpack_id: ID of the backpack to analyze
        
        Returns:
            Dictionary with synergy analysis
        """
        if backpack_id not in self.backpacks:
            return {"error": f"Backpack '{backpack_id}' not found"}
        
        backpack = self.backpacks[backpack_id]
        synergies = []
        
        # Check all item pairs for adjacency
        item_ids = list(backpack.items.keys())
        for i, item1_id in enumerate(item_ids):
            for item2_id in item_ids[i+1:]:
                # Check edge adjacency (Section 1.3 standard)
                if backpack.check_adjacency(item1_id, item2_id, AdjacencyType.EDGE):
                    item1 = backpack.items[item1_id]
                    item2 = backpack.items[item2_id]
                    
                    synergies.append({
                        "item1": item1.item_name,
                        "item2": item2.item_name,
                        "adjacency_type": "edge",
                        "item1_id": item1_id,
                        "item2_id": item2_id
                    })
        
        return {
            "total_synergies": len(synergies),
            "synergies": synergies,
            "research_compliant": True
        }
    
    def export_backpack_layout(self, backpack_id: str, include_pixel_coords: bool = True) -> Dict:
        """
        Export complete backpack layout with research-based data
        
        Args:
            backpack_id: ID of the backpack to export
            include_pixel_coords: Whether to include pixel coordinates
        
        Returns:
            Complete layout data
        """
        if backpack_id not in self.backpacks:
            return {"error": f"Backpack '{backpack_id}' not found"}
        
        backpack = self.backpacks[backpack_id]
        layout = {
            "backpack_id": backpack_id,
            "grid_bounds": backpack.get_grid_bounds(),
            "total_cells": len(backpack.cells),
            "occupied_cells": len(backpack.get_occupied_cells()),
            "available_cells": len(backpack.get_available_cells()),
            "items": [],
            "research_metadata": {
                "grid_system": "Section 1.3 compliant",
                "item_schema": "Section 4.1 compliant",
                "adjacency_rules": "Edge-sharing only",
                "rotation_support": True
            }
        }
        
        # Add item details
        for item_id, item in backpack.items.items():
            x, y = backpack.item_positions[item_id]
            rotation = backpack.item_rotations[item_id]
            
            item_data = {
                "item_id": item_id,
                "item_name": item.item_name,
                "position": (x, y),
                "rotation": rotation,
                "grid_size": item.get_grid_size(),
                "rotated_shape": item.get_rotated_shape(rotation)
            }
            
            if include_pixel_coords:
                pixel_pos = self.scaling_system.get_grid_position_pixels(x, y)
                pixel_dims = self.scaling_system.get_item_pixel_dimensions(item, rotation)
                scale = self.scaling_system.calculate_research_based_scale(item)
                
                item_data.update({
                    "pixel_position": pixel_pos,
                    "pixel_dimensions": pixel_dims,
                    "scale_factor": scale
                })
            
            layout["items"].append(item_data)
        
        return layout
    
    def save_configuration(self, output_path: str = "unified_research_config.json"):
        """Save the unified system configuration"""
        config = {
            "system_info": {
                "name": "Unified Research-Based System",
                "version": "1.0",
                "research_compliance": "DeepResearch.txt Sections 1.3 and 4.1"
            },
            "grid_dimensions": {
                "max_width": self.grid_dims.MAX_WIDTH,
                "max_height": self.grid_dims.MAX_HEIGHT,
                "cell_width": self.grid_dims.CELL_WIDTH,
                "cell_height": self.grid_dims.CELL_HEIGHT,
                "grid_offset_x": self.grid_dims.GRID_OFFSET_X,
                "grid_offset_y": self.grid_dims.GRID_OFFSET_Y
            },
            "items_loaded": len(self.items),
            "backpacks_created": len(self.backpacks),
            "features": {
                "research_based_scaling": True,
                "enhanced_grid_system": True,
                "adjacency_validation": True,
                "rotation_support": True,
                "bag_expansion": True
            }
        }
        
        with open(output_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Saved unified system configuration to {output_path}")


def demonstrate_unified_system():
    """Demonstrate the complete unified research system"""
    print("=== Unified Research-Based System Demonstration ===")
    
    # Initialize system
    system = UnifiedResearchSystem()
    
    # Create a backpack
    backpack = system.create_backpack("test_backpack", 4, 3)
    
    # Test item placement with research-based data
    test_items = ["Wooden Sword", "Axe", "Health Potion"]
    
    for i, item_name in enumerate(test_items):
        if item_name in system.items:
            # Get placement data
            placement_data = system.calculate_item_placement_data(item_name)
            print(f"\n{item_name} placement data:")
            print(f"  Grid size: {placement_data.get('grid_size')}")
            print(f"  Scale factor: {placement_data.get('scale_factor', 0):.3f}")
            print(f"  Pixel dimensions: {placement_data.get('pixel_dimensions')}")
            
            # Find optimal placement
            optimal = system.find_optimal_placement("test_backpack", item_name)
            if optimal:
                x, y, rotation = optimal
                result = system.place_item_in_backpack("test_backpack", item_name, i+1, x, y, rotation)
                print(f"  Placed at ({x}, {y}) with {rotation}° rotation: {result.success}")
    
    # Analyze synergies
    synergies = system.analyze_backpack_synergies("test_backpack")
    print(f"\nSynergy analysis: {synergies.get('total_synergies', 0)} synergies found")
    
    # Export layout
    layout = system.export_backpack_layout("test_backpack")
    print(f"\nBackpack layout: {layout['occupied_cells']}/{layout['total_cells']} cells occupied")
    
    # Save configuration
    system.save_configuration()


if __name__ == "__main__":
    demonstrate_unified_system()
