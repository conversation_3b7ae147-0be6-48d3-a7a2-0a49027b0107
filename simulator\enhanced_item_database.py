"""
Research-Compliant Item Database System

Implements the exact item schema from Section 4.1 of DeepResearch.txt:
- ItemID: A unique integer or string identifier
- ItemName: The in-game name of the item
- Rarity: An enum {Common, Rare, Epic, Legendary, Godly, Unique}
- Class: An enum {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Pyromancer, <PERSON>, Ranger}
- Type: An enum categorizing the item's primary function
- Subtype: A list of tags for more specific mechanics
- Cost: The base Gold cost in the shop
- GridShape: A 2D boolean array defining the item's physical shape
- Sockets: An integer representing the number of gem sockets
- InShop: A boolean indicating if the item can appear naturally in the shop
- Stats: A dictionary of numerical properties
- Effects: A list of dictionaries defining mechanical effects
"""

import json
import sqlite3
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum

from simulator.item_effects import ItemEffectParser, ParsedEffect


class ItemRarity(Enum):
    COMMON = "Common"
    RARE = "Rare"
    EPIC = "Epic"
    LEGENDARY = "Legendary"
    GODLY = "Godly"
    UNIQUE = "Unique"


class ItemClass(Enum):
    NEUTRAL = "Neutral"
    BERSERKER = "Berserker"
    PYROMANCER = "Pyromancer"
    REAPER = "Reaper"
    RANGER = "Ranger"


class ItemType(Enum):
    WEAPON = "Weapon"
    ARMOR = "Armor"
    HELMET = "Helmet"
    SHOES = "Shoes"
    GLOVES = "Gloves"
    SHIELD = "Shield"
    BAG = "Bag"
    PET = "Pet"
    FOOD = "Food"
    POTION = "Potion"
    GEMSTONE = "Gemstone"
    ACCESSORY = "Accessory"
    PLAYING_CARD = "Playing Card"


@dataclass
class EnhancedItem:
    """Research-compliant item representation"""
    item_id: str
    item_name: str
    rarity: ItemRarity
    item_class: ItemClass
    item_type: ItemType
    subtype: List[str]
    cost: int
    grid_shape: List[List[bool]]  # 2D boolean array
    sockets: int
    in_shop: bool
    stats: Dict[str, Any]
    effects: List[ParsedEffect]
    
    # Additional fields for compatibility
    description: str = ""
    required_subclass: Optional[str] = None
    
    def get_grid_size(self) -> tuple:
        """Get the (width, height) of the item"""
        if not self.grid_shape:
            return (1, 1)
        return (len(self.grid_shape[0]), len(self.grid_shape))
    
    def get_occupied_cells(self) -> List[tuple]:
        """Get list of (x, y) coordinates that this item occupies"""
        cells = []
        for y, row in enumerate(self.grid_shape):
            for x, occupied in enumerate(row):
                if occupied:
                    cells.append((x, y))
        return cells
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        # Convert enums to strings
        data['rarity'] = self.rarity.value
        data['item_class'] = self.item_class.value
        data['item_type'] = self.item_type.value
        # Convert effects to dictionaries
        data['effects'] = [asdict(effect) for effect in self.effects]
        return data


class EnhancedItemDatabase:
    """Research-compliant item database manager"""
    
    def __init__(self, db_path: str = "GameData.db"):
        self.db_path = db_path
        self.effect_parser = ItemEffectParser()
        self.items: Dict[str, EnhancedItem] = {}
        self._load_items()
    
    def _load_items(self):
        """Load items from database and convert to research-compliant format"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM Items")
            rows = cursor.fetchall()
            
            for row in rows:
                item = self._convert_db_item(row)
                if item:
                    self.items[item.item_name] = item
            
            conn.close()
            print(f"Loaded {len(self.items)} items from database")
            
        except sqlite3.Error as e:
            print(f"Database error loading items: {e}")
    
    def _convert_db_item(self, row: sqlite3.Row) -> Optional[EnhancedItem]:
        """Convert database row to EnhancedItem"""
        try:
            # Parse raw stats
            raw_stats = json.loads(row['RawStats']) if row['RawStats'] else {}
            
            # Extract basic info
            item_name = row['Name']
            rarity = self._parse_rarity(raw_stats.get('Rarity', row['Rarity']))
            item_class = self._parse_class(raw_stats.get('Class', row['Class']))
            item_type = self._parse_type(raw_stats.get('Type', row['Type']))
            cost = int(raw_stats.get('Cost', row['Cost'] or 0))
            
            # Parse grid shape
            grid_shape = self._parse_grid_shape(row['Shape'])
            
            # Parse stats
            stats = self._extract_stats(raw_stats)
            
            # Parse effects
            effect_text = raw_stats.get('Effect', '')
            effects = self.effect_parser.parse_effect_text(effect_text)
            
            # Determine other properties
            sockets = int(raw_stats.get('Sockets', 0))
            in_shop = raw_stats.get('In shop', 'Yes').lower() == 'yes'
            subtype = self._determine_subtype(item_type, raw_stats)
            
            return EnhancedItem(
                item_id=str(row['ItemID']),
                item_name=item_name,
                rarity=rarity,
                item_class=item_class,
                item_type=item_type,
                subtype=subtype,
                cost=cost,
                grid_shape=grid_shape,
                sockets=sockets,
                in_shop=in_shop,
                stats=stats,
                effects=effects,
                description=row['Description'] or ''
            )
            
        except Exception as e:
            print(f"Error converting item {row['Name']}: {e}")
            return None
    
    def _parse_rarity(self, rarity_str: str) -> ItemRarity:
        """Parse rarity string to enum"""
        if not rarity_str:
            return ItemRarity.COMMON
        
        rarity_map = {
            'common': ItemRarity.COMMON,
            'rare': ItemRarity.RARE,
            'epic': ItemRarity.EPIC,
            'legendary': ItemRarity.LEGENDARY,
            'godly': ItemRarity.GODLY,
            'unique': ItemRarity.UNIQUE
        }
        return rarity_map.get(rarity_str.lower(), ItemRarity.COMMON)
    
    def _parse_class(self, class_str: str) -> ItemClass:
        """Parse class string to enum"""
        if not class_str:
            return ItemClass.NEUTRAL
        
        class_map = {
            'neutral': ItemClass.NEUTRAL,
            'berserker': ItemClass.BERSERKER,
            'pyromancer': ItemClass.PYROMANCER,
            'reaper': ItemClass.REAPER,
            'ranger': ItemClass.RANGER
        }
        return class_map.get(class_str.lower(), ItemClass.NEUTRAL)
    
    def _parse_type(self, type_str: str) -> ItemType:
        """Parse type string to enum"""
        if not type_str:
            return ItemType.ACCESSORY
        
        type_map = {
            'weapon': ItemType.WEAPON,
            'armor': ItemType.ARMOR,
            'helmet': ItemType.HELMET,
            'shoes': ItemType.SHOES,
            'gloves': ItemType.GLOVES,
            'shield': ItemType.SHIELD,
            'bag': ItemType.BAG,
            'pet': ItemType.PET,
            'food': ItemType.FOOD,
            'potion': ItemType.POTION,
            'gemstone': ItemType.GEMSTONE,
            'accessory': ItemType.ACCESSORY,
            'playing card': ItemType.PLAYING_CARD
        }
        return type_map.get(type_str.lower(), ItemType.ACCESSORY)
    
    def _parse_grid_shape(self, shape_str: str) -> List[List[bool]]:
        """Parse grid shape from database"""
        if not shape_str:
            return [[True]]  # Default 1x1 shape
        
        try:
            # Try to parse as JSON
            shape_data = json.loads(shape_str)
            if isinstance(shape_data, list) and len(shape_data) > 0:
                # Convert to boolean grid
                grid = []
                for row in shape_data:
                    if isinstance(row, list):
                        bool_row = [cell == 1 or cell == True for cell in row]
                        grid.append(bool_row)
                return grid if grid else [[True]]
        except:
            pass
        
        return [[True]]  # Default fallback
    
    def _extract_stats(self, raw_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Extract numerical stats from raw stats"""
        stats = {}
        
        # Common stat fields
        stat_fields = ['Damage', 'Stamina', 'Accuracy', 'Cooldown', 'Health', 'Block']
        
        for field in stat_fields:
            if field in raw_stats:
                stats[field.lower()] = raw_stats[field]
        
        return stats
    
    def _determine_subtype(self, item_type: ItemType, raw_stats: Dict[str, Any]) -> List[str]:
        """Determine item subtypes based on type and stats"""
        subtypes = []
        
        if item_type == ItemType.WEAPON:
            # Determine weapon subtypes
            if 'ranged' in str(raw_stats).lower():
                subtypes.append('Ranged')
            else:
                subtypes.append('Melee')
        
        # Add elemental subtypes based on effects
        effect_text = raw_stats.get('Effect', '').lower()
        if 'fire' in effect_text or '🔥' in effect_text:
            subtypes.append('Fire')
        if 'ice' in effect_text or '❄️' in effect_text:
            subtypes.append('Ice')
        if 'poison' in effect_text or '☠️' in effect_text:
            subtypes.append('Dark')
        if 'heal' in effect_text or '💚' in effect_text:
            subtypes.append('Holy')
        
        return subtypes
    
    def get_item(self, name: str) -> Optional[EnhancedItem]:
        """Get item by name"""
        return self.items.get(name)
    
    def get_items_by_class(self, item_class: ItemClass) -> List[EnhancedItem]:
        """Get all items for a specific class"""
        return [item for item in self.items.values() 
                if item.item_class == item_class or item.item_class == ItemClass.NEUTRAL]
    
    def get_items_by_rarity(self, rarity: ItemRarity) -> List[EnhancedItem]:
        """Get all items of a specific rarity"""
        return [item for item in self.items.values() if item.rarity == rarity]
    
    def get_items_by_type(self, item_type: ItemType) -> List[EnhancedItem]:
        """Get all items of a specific type"""
        return [item for item in self.items.values() if item.item_type == item_type]
    
    def get_shop_items(self) -> List[EnhancedItem]:
        """Get all items that can appear in shop"""
        return [item for item in self.items.values() if item.in_shop]
    
    def validate_completeness(self) -> Dict[str, Any]:
        """Validate database completeness against research requirements"""
        validation = {
            'total_items': len(self.items),
            'by_rarity': {},
            'by_class': {},
            'by_type': {},
            'missing_effects': [],
            'missing_stats': []
        }
        
        # Count by categories
        for rarity in ItemRarity:
            validation['by_rarity'][rarity.value] = len(self.get_items_by_rarity(rarity))
        
        for item_class in ItemClass:
            validation['by_class'][item_class.value] = len(self.get_items_by_class(item_class))
        
        for item_type in ItemType:
            validation['by_type'][item_type.value] = len(self.get_items_by_type(item_type))
        
        # Check for items missing effects or stats
        for item in self.items.values():
            if not item.effects:
                validation['missing_effects'].append(item.item_name)
            if not item.stats and item.item_type in [ItemType.WEAPON, ItemType.ARMOR]:
                validation['missing_stats'].append(item.item_name)
        
        return validation
