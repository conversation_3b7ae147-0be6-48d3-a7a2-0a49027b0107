"""
Test the Build Recommendation Engine
"""

from assist_mode.build_recommendation_engine import (
    RecommendationEngine, BuildOptimizer, SynergyAnalyzer,
    Recommendation, BuildAnalysis, RecommendationType, Priority
)

def create_test_game_state():
    """Create a comprehensive test game state"""
    return {
        "phase": "shop",
        "round": 4,
        "health": 75,
        "gold": 35,
        "lives": 2,
        "character_class": "berserker",
        
        # Backpack items
        "backpack_items": [
            {
                "name": "Hero Sword",
                "type": "weapon",
                "class": "neutral",
                "position": (1, 1),
                "power_level": 3.2,
                "tags": ["weapon", "melee", "sword"]
            },
            {
                "name": "Berserker Axe",
                "type": "weapon", 
                "class": "berserker",
                "position": (3, 1),
                "power_level": 2.8,
                "tags": ["weapon", "melee", "axe"]
            },
            {
                "name": "Leather Armor",
                "type": "armor",
                "class": "neutral",
                "position": (0, 0),
                "power_level": 2.1,
                "tags": ["armor", "defense"]
            }
        ],
        
        # Inventory items
        "inventory_items": [
            {
                "name": "Health Potion",
                "type": "potion",
                "class": "neutral",
                "power_level": 1.0,
                "tags": ["potion", "healing"],
                "shape": [[True]]
            }
        ],
        
        # Shop items
        "shop_items": [
            {
                "name": "Double Axe",
                "type": "weapon",
                "class": "berserker",
                "cost": 25,
                "power_level": 4.0,
                "tags": ["weapon", "melee", "axe"]
            },
            {
                "name": "Chain Mail",
                "type": "armor",
                "class": "neutral",
                "cost": 20,
                "power_level": 3.5,
                "tags": ["armor", "defense", "metal"]
            },
            None,  # Empty slot
            {
                "name": "Weak Dagger",
                "type": "weapon",
                "class": "neutral",
                "cost": 8,
                "power_level": 1.2,
                "tags": ["weapon", "melee"]
            },
            {
                "name": "Magic Ring",
                "type": "accessory",
                "class": "neutral",
                "cost": 30,
                "power_level": 2.5,
                "tags": ["accessory", "magic"]
            }
        ],
        
        "occupied_cells": {(0, 0), (1, 1), (2, 1), (3, 1), (4, 1)}
    }

def test_build_recommendation_engine():
    print("🧠 TESTING BUILD RECOMMENDATION ENGINE")
    print("=" * 60)
    
    # Test synergy analyzer
    print("\n🔗 TESTING SYNERGY ANALYZER:")
    print("-" * 30)
    
    from simulator.enhanced_item_database import EnhancedItemDatabase
    item_database = EnhancedItemDatabase()
    synergy_analyzer = SynergyAnalyzer(item_database)
    
    game_state = create_test_game_state()
    backpack_items = game_state["backpack_items"]
    
    synergy_analysis = synergy_analyzer.analyze_synergies(backpack_items)
    
    print(f"  Synergy analysis results:")
    print(f"    Total synergies: {synergy_analysis['total_synergies']}")
    print(f"    Synergy score: {synergy_analysis['synergy_score']:.3f}")
    print(f"    Average strength: {synergy_analysis['average_strength']:.3f}")
    
    print(f"  Synergy pairs:")
    for pair in synergy_analysis['synergy_pairs']:
        print(f"    {pair['item1']} + {pair['item2']}: {pair['strength']:.3f} ({pair['type']})")
    
    # Test individual synergy calculation
    item1 = backpack_items[0]  # Hero Sword
    item2 = backpack_items[1]  # Berserker Axe
    
    synergy_strength = synergy_analyzer._calculate_item_synergy(item1, item2)
    synergy_type = synergy_analyzer._get_synergy_type(item1, item2)
    
    print(f"\n  Individual synergy test:")
    print(f"    {item1['name']} + {item2['name']}: {synergy_strength:.3f}")
    print(f"    Synergy type: {synergy_type}")
    
    # Test build optimizer
    print("\n🏗️ TESTING BUILD OPTIMIZER:")
    print("-" * 30)
    
    build_optimizer = BuildOptimizer(item_database)
    build_analysis = build_optimizer.analyze_build(game_state)
    
    print(f"  Build analysis results:")
    print(f"    Synergy score: {build_analysis.synergy_score:.3f}")
    print(f"    Power level: {build_analysis.power_level:.3f}")
    print(f"    Defensive rating: {build_analysis.defensive_rating:.3f}")
    print(f"    Offensive rating: {build_analysis.offensive_rating:.3f}")
    print(f"    Flexibility score: {build_analysis.flexibility_score:.3f}")
    print(f"    Completion: {build_analysis.completion_percentage:.1%}")
    
    print(f"\n  Build strengths:")
    for strength in build_analysis.strengths:
        print(f"    + {strength}")
    
    print(f"\n  Build weaknesses:")
    for weakness in build_analysis.weaknesses:
        print(f"    - {weakness}")
    
    print(f"\n  Recommended direction: {build_analysis.recommended_direction}")
    
    # Test recommendation engine
    print("\n🎯 TESTING RECOMMENDATION ENGINE:")
    print("-" * 30)
    
    recommendation_engine = RecommendationEngine()
    
    print(f"  Engine components initialized:")
    print(f"    Item database: {recommendation_engine.item_database is not None}")
    print(f"    Build optimizer: {recommendation_engine.build_optimizer is not None}")
    
    # Generate recommendations
    recommendations = recommendation_engine.generate_recommendations(game_state, max_recommendations=8)
    
    print(f"\n  Generated {len(recommendations)} recommendations:")
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n    Recommendation {i}:")
        print(f"      Type: {rec.recommendation_type.value}")
        print(f"      Priority: {rec.priority.value}")
        print(f"      Confidence: {rec.confidence:.3f}")
        print(f"      Reasoning: {rec.reasoning}")
        print(f"      Expected benefit: {rec.expected_benefit:.3f}")
        print(f"      Risk: {rec.risk_assessment}")
        print(f"      Parameters: {rec.action_parameters}")
    
    # Test recommendation conversion
    print("\n📋 TESTING RECOMMENDATION CONVERSION:")
    print("-" * 30)
    
    if recommendations:
        first_rec = recommendations[0]
        rec_dict = first_rec.to_dict()
        
        print(f"  Recommendation dictionary:")
        for key, value in rec_dict.items():
            print(f"    {key}: {value}")
    
    # Test different game phases
    print("\n🔄 TESTING DIFFERENT GAME PHASES:")
    print("-" * 30)
    
    # Battle phase
    battle_state = game_state.copy()
    battle_state["phase"] = "battle"
    
    battle_recommendations = recommendation_engine.generate_recommendations(battle_state)
    print(f"  Battle phase recommendations: {len(battle_recommendations)}")
    
    # Early game state
    early_state = {
        "phase": "shop",
        "round": 1,
        "health": 100,
        "gold": 10,
        "character_class": "ranger",
        "backpack_items": [],
        "inventory_items": [],
        "shop_items": [None] * 5,
        "occupied_cells": set()
    }
    
    early_recommendations = recommendation_engine.generate_recommendations(early_state)
    print(f"  Early game recommendations: {len(early_recommendations)}")
    
    # Test item fit evaluation
    print("\n📊 TESTING ITEM FIT EVALUATION:")
    print("-" * 30)
    
    shop_items = game_state["shop_items"]
    
    for i, item in enumerate(shop_items):
        if item:
            fit_score = recommendation_engine._evaluate_item_fit(item, game_state, build_analysis)
            print(f"    {item['name']}: fit score {fit_score:.3f}")
    
    # Test shop quality evaluation
    shop_quality = recommendation_engine._evaluate_shop_quality(shop_items, game_state, build_analysis)
    print(f"\n  Overall shop quality: {shop_quality:.3f}")
    
    # Test placement optimization
    print("\n📍 TESTING PLACEMENT OPTIMIZATION:")
    print("-" * 30)
    
    inventory_items = game_state["inventory_items"]
    
    for item in inventory_items:
        optimal_placement = recommendation_engine._find_optimal_placement(item, game_state)
        if optimal_placement:
            x, y, rotation = optimal_placement
            print(f"    {item['name']}: optimal placement at ({x}, {y}) with rotation {rotation}")
        else:
            print(f"    {item['name']}: no valid placement found")
    
    # Test turn readiness evaluation
    turn_readiness = recommendation_engine._evaluate_turn_readiness(game_state, build_analysis)
    print(f"\n  Turn readiness score: {turn_readiness:.3f}")
    
    # Test edge cases
    print("\n🔍 TESTING EDGE CASES:")
    print("-" * 30)
    
    # Empty build
    empty_state = {
        "phase": "shop",
        "round": 1,
        "health": 100,
        "gold": 50,
        "character_class": "neutral",
        "backpack_items": [],
        "inventory_items": [],
        "shop_items": [None] * 5,
        "occupied_cells": set()
    }
    
    empty_analysis = build_optimizer.analyze_build(empty_state)
    empty_recommendations = recommendation_engine.generate_recommendations(empty_state)
    
    print(f"  Empty build analysis:")
    print(f"    Power level: {empty_analysis.power_level:.3f}")
    print(f"    Synergy score: {empty_analysis.synergy_score:.3f}")
    print(f"    Recommendations: {len(empty_recommendations)}")
    
    # Full inventory
    full_state = game_state.copy()
    full_state["backpack_items"] = [
        {"name": f"Item_{i}", "type": "weapon", "power_level": 1.0}
        for i in range(20)
    ]
    
    full_recommendations = recommendation_engine.generate_recommendations(full_state)
    sell_recommendations = [r for r in full_recommendations if r.recommendation_type == RecommendationType.SELL_ITEM]
    
    print(f"\n  Full inventory:")
    print(f"    Total recommendations: {len(full_recommendations)}")
    print(f"    Sell recommendations: {len(sell_recommendations)}")
    
    # Test recommendation priorities
    print("\n📈 TESTING RECOMMENDATION PRIORITIES:")
    print("-" * 30)
    
    priority_counts = {}
    for rec in recommendations:
        priority = rec.priority.value
        priority_counts[priority] = priority_counts.get(priority, 0) + 1
    
    print(f"  Priority distribution:")
    for priority, count in priority_counts.items():
        print(f"    {priority}: {count}")
    
    # Test recommendation types
    type_counts = {}
    for rec in recommendations:
        rec_type = rec.recommendation_type.value
        type_counts[rec_type] = type_counts.get(rec_type, 0) + 1
    
    print(f"\n  Type distribution:")
    for rec_type, count in type_counts.items():
        print(f"    {rec_type}: {count}")
    
    # Performance test
    print("\n⚡ TESTING PERFORMANCE:")
    print("-" * 30)
    
    import time
    
    # Time build analysis
    start_time = time.time()
    for _ in range(10):
        build_optimizer.analyze_build(game_state)
    analysis_time = (time.time() - start_time) / 10
    
    # Time recommendation generation
    start_time = time.time()
    for _ in range(10):
        recommendation_engine.generate_recommendations(game_state)
    recommendation_time = (time.time() - start_time) / 10
    
    print(f"  Performance metrics:")
    print(f"    Build analysis: {analysis_time*1000:.2f} ms")
    print(f"    Recommendation generation: {recommendation_time*1000:.2f} ms")
    print(f"    Total processing: {(analysis_time + recommendation_time)*1000:.2f} ms")
    
    print("\n" + "=" * 60)
    print("✅ BUILD RECOMMENDATION ENGINE TEST COMPLETE")

if __name__ == "__main__":
    test_build_recommendation_engine()
