"""
Sophisticated Reward Function Design for RL Training

Implements multiple reward functions for different aspects of strategic play:
- Immediate action rewards (placement, purchasing, selling)
- Strategic rewards (synergy building, resource management)
- Long-term rewards (build completion, win/loss outcomes)
- Curriculum learning rewards (progressive difficulty)
- Multi-objective rewards (exploration vs exploitation)
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np

from rl.enhanced_state_representation import EnhancedStateEncoder


class RewardType(Enum):
    IMMEDIATE = "immediate"
    STRATEGIC = "strategic"
    OUTCOME = "outcome"
    CURRICULUM = "curriculum"
    EXPLORATION = "exploration"


@dataclass
class RewardComponent:
    """Individual component of the reward function"""
    name: str
    reward_type: RewardType
    weight: float
    description: str
    min_value: float = -1.0
    max_value: float = 1.0


class RewardFunction:
    """Base class for reward functions"""
    
    def __init__(self, name: str, components: List[RewardComponent]):
        self.name = name
        self.components = {comp.name: comp for comp in components}
        self.total_weight = sum(comp.weight for comp in components)
    
    def calculate_reward(self, prev_state: Dict[str, Any], action: Dict[str, Any], 
                        new_state: Dict[str, Any], info: Dict[str, Any]) -> Dict[str, float]:
        """Calculate reward components"""
        raise NotImplementedError
    
    def get_total_reward(self, prev_state: Dict[str, Any], action: Dict[str, Any],
                        new_state: Dict[str, Any], info: Dict[str, Any]) -> float:
        """Get weighted total reward"""
        rewards = self.calculate_reward(prev_state, action, new_state, info)
        
        total = 0.0
        for name, value in rewards.items():
            if name in self.components:
                component = self.components[name]
                # Clamp to component bounds
                clamped_value = np.clip(value, component.min_value, component.max_value)
                total += clamped_value * component.weight
        
        return total / self.total_weight  # Normalize by total weight


class BasicRewardFunction(RewardFunction):
    """Basic reward function for initial training"""
    
    def __init__(self):
        components = [
            RewardComponent("action_success", RewardType.IMMEDIATE, 1.0, 
                          "Reward for successful actions", 0.0, 1.0),
            RewardComponent("gold_efficiency", RewardType.STRATEGIC, 0.5,
                          "Reward for efficient gold usage", -0.5, 0.5),
            RewardComponent("item_synergy", RewardType.STRATEGIC, 1.5,
                          "Reward for building synergies", 0.0, 1.0),
            RewardComponent("build_progress", RewardType.STRATEGIC, 1.0,
                          "Reward for build progression", 0.0, 1.0),
            RewardComponent("battle_outcome", RewardType.OUTCOME, 3.0,
                          "Reward for battle results", -1.0, 1.0)
        ]
        super().__init__("basic", components)
    
    def calculate_reward(self, prev_state: Dict[str, Any], action: Dict[str, Any],
                        new_state: Dict[str, Any], info: Dict[str, Any]) -> Dict[str, float]:
        rewards = {}
        
        # Action success reward
        rewards["action_success"] = 0.1 if info.get("action_successful", True) else -0.2
        
        # Gold efficiency
        gold_change = new_state.get("gold", 0) - prev_state.get("gold", 0)
        if gold_change < 0:  # Spent gold
            item_value = info.get("item_value", 0)
            efficiency = item_value / abs(gold_change) if gold_change != 0 else 0
            rewards["gold_efficiency"] = min(0.5, efficiency * 0.1)
        else:
            rewards["gold_efficiency"] = 0.0
        
        # Item synergy
        prev_synergies = self._count_synergies(prev_state.get("backpack_items", []))
        new_synergies = self._count_synergies(new_state.get("backpack_items", []))
        synergy_change = new_synergies - prev_synergies
        rewards["item_synergy"] = synergy_change * 0.2
        
        # Build progress
        prev_items = len(prev_state.get("backpack_items", []))
        new_items = len(new_state.get("backpack_items", []))
        if new_items > prev_items:
            rewards["build_progress"] = 0.1
        elif new_items < prev_items and action.get("action_type") == "sell_item":
            rewards["build_progress"] = -0.05  # Small penalty for selling
        else:
            rewards["build_progress"] = 0.0
        
        # Battle outcome
        if info.get("battle_completed", False):
            if info.get("battle_won", False):
                rewards["battle_outcome"] = 1.0
            else:
                rewards["battle_outcome"] = -0.5
        else:
            rewards["battle_outcome"] = 0.0
        
        return rewards
    
    def _count_synergies(self, items: List[Dict[str, Any]]) -> int:
        """Count synergies between items"""
        synergy_count = 0
        for i, item1 in enumerate(items):
            for item2 in items[i+1:]:
                tags1 = set(item1.get("tags", []))
                tags2 = set(item2.get("tags", []))
                if len(tags1.intersection(tags2)) > 0:
                    synergy_count += 1
        return synergy_count


class AdvancedRewardFunction(RewardFunction):
    """Advanced reward function with sophisticated strategic evaluation"""
    
    def __init__(self):
        components = [
            # Immediate rewards
            RewardComponent("action_validity", RewardType.IMMEDIATE, 0.5,
                          "Penalty for invalid actions", -1.0, 0.0),
            RewardComponent("placement_quality", RewardType.IMMEDIATE, 1.0,
                          "Reward for good item placement", 0.0, 1.0),
            RewardComponent("shop_decisions", RewardType.IMMEDIATE, 0.8,
                          "Reward for smart shop choices", -0.5, 1.0),
            
            # Strategic rewards
            RewardComponent("synergy_building", RewardType.STRATEGIC, 2.0,
                          "Reward for building synergies", 0.0, 1.0),
            RewardComponent("resource_management", RewardType.STRATEGIC, 1.5,
                          "Reward for efficient resource use", -0.5, 1.0),
            RewardComponent("build_coherence", RewardType.STRATEGIC, 1.8,
                          "Reward for coherent build strategy", 0.0, 1.0),
            RewardComponent("timing_optimization", RewardType.STRATEGIC, 1.2,
                          "Reward for good timing decisions", -0.3, 1.0),
            RewardComponent("risk_management", RewardType.STRATEGIC, 1.0,
                          "Reward for appropriate risk taking", -0.5, 1.0),
            
            # Outcome rewards
            RewardComponent("battle_performance", RewardType.OUTCOME, 3.0,
                          "Reward based on battle performance", -1.0, 1.0),
            RewardComponent("progression_success", RewardType.OUTCOME, 2.5,
                          "Reward for advancing through rounds", 0.0, 1.0),
            RewardComponent("final_ranking", RewardType.OUTCOME, 4.0,
                          "Reward based on final game ranking", -1.0, 1.0)
        ]
        super().__init__("advanced", components)
        self.state_encoder = EnhancedStateEncoder()
    
    def calculate_reward(self, prev_state: Dict[str, Any], action: Dict[str, Any],
                        new_state: Dict[str, Any], info: Dict[str, Any]) -> Dict[str, float]:
        rewards = {}
        
        # Action validity
        rewards["action_validity"] = 0.0 if info.get("action_successful", True) else -0.5
        
        # Placement quality
        if action.get("action_type") == "place_item":
            placement_score = self._evaluate_placement_quality(action, new_state)
            rewards["placement_quality"] = placement_score
        else:
            rewards["placement_quality"] = 0.0
        
        # Shop decisions
        if action.get("action_type") in ["buy_item", "reroll_shop", "lock_item"]:
            shop_score = self._evaluate_shop_decision(action, prev_state, new_state)
            rewards["shop_decisions"] = shop_score
        else:
            rewards["shop_decisions"] = 0.0
        
        # Synergy building
        synergy_score = self._evaluate_synergy_improvement(prev_state, new_state)
        rewards["synergy_building"] = synergy_score
        
        # Resource management
        resource_score = self._evaluate_resource_management(prev_state, new_state, action)
        rewards["resource_management"] = resource_score
        
        # Build coherence
        coherence_score = self._evaluate_build_coherence(new_state)
        rewards["build_coherence"] = coherence_score
        
        # Timing optimization
        timing_score = self._evaluate_timing(prev_state, new_state, action)
        rewards["timing_optimization"] = timing_score
        
        # Risk management
        risk_score = self._evaluate_risk_management(prev_state, new_state, action)
        rewards["risk_management"] = risk_score
        
        # Battle performance
        if info.get("battle_completed", False):
            battle_score = self._evaluate_battle_performance(info)
            rewards["battle_performance"] = battle_score
        else:
            rewards["battle_performance"] = 0.0
        
        # Progression success
        if new_state.get("round", 1) > prev_state.get("round", 1):
            rewards["progression_success"] = 0.5
        elif info.get("game_over", False) and not info.get("won_game", False):
            rewards["progression_success"] = -0.3
        else:
            rewards["progression_success"] = 0.0
        
        # Final ranking
        if info.get("game_completed", False):
            ranking_score = self._evaluate_final_ranking(info)
            rewards["final_ranking"] = ranking_score
        else:
            rewards["final_ranking"] = 0.0
        
        return rewards
    
    def _evaluate_placement_quality(self, action: Dict[str, Any], state: Dict[str, Any]) -> float:
        """Evaluate quality of item placement"""
        x, y = action.get("x", 0), action.get("y", 0)
        item_name = action.get("item_name", "")
        
        # Check adjacency to synergistic items
        adjacency_score = 0.0
        backpack_items = state.get("backpack_items", [])
        
        for item in backpack_items:
            if item.get("name") == item_name:
                continue
            
            item_pos = item.get("position", (0, 0))
            distance = abs(item_pos[0] - x) + abs(item_pos[1] - y)
            
            if distance == 1:  # Adjacent
                # Check for synergy
                item_tags = set(item.get("tags", []))
                # Would need to get placed item tags from action
                adjacency_score += 0.2
        
        # Check for efficient space usage
        space_efficiency = self._calculate_space_efficiency(state)
        
        return min(1.0, adjacency_score + space_efficiency * 0.3)
    
    def _evaluate_shop_decision(self, action: Dict[str, Any], prev_state: Dict[str, Any], 
                               new_state: Dict[str, Any]) -> float:
        """Evaluate shop decision quality"""
        action_type = action.get("action_type")
        
        if action_type == "buy_item":
            # Evaluate if the item fits the build
            item_name = action.get("item_name", "")
            build_fit = self._evaluate_item_build_fit(item_name, prev_state)
            
            # Consider gold efficiency
            gold_spent = prev_state.get("gold", 0) - new_state.get("gold", 0)
            item_value = action.get("item_value", gold_spent)
            efficiency = item_value / max(gold_spent, 1)
            
            return build_fit * 0.7 + min(1.0, efficiency) * 0.3
        
        elif action_type == "reroll_shop":
            # Evaluate if reroll was necessary
            shop_quality = self._evaluate_shop_quality(prev_state)
            if shop_quality < 0.3:  # Poor shop, reroll justified
                return 0.3
            else:
                return -0.2  # Good shop, reroll wasteful
        
        elif action_type == "lock_item":
            # Evaluate if locked item is valuable
            return 0.1  # Small positive for strategic locking
        
        return 0.0
    
    def _evaluate_synergy_improvement(self, prev_state: Dict[str, Any], 
                                    new_state: Dict[str, Any]) -> float:
        """Evaluate improvement in synergies"""
        prev_encoded = self.state_encoder.encode_state(prev_state)
        new_encoded = self.state_encoder.encode_state(new_state)
        
        prev_synergy = prev_encoded["build_metrics"][3]  # Synergy score
        new_synergy = new_encoded["build_metrics"][3]
        
        synergy_improvement = new_synergy - prev_synergy
        return np.clip(synergy_improvement * 2.0, 0.0, 1.0)
    
    def _evaluate_resource_management(self, prev_state: Dict[str, Any], 
                                    new_state: Dict[str, Any], action: Dict[str, Any]) -> float:
        """Evaluate resource management efficiency"""
        # Gold management
        gold_change = new_state.get("gold", 0) - prev_state.get("gold", 0)
        
        if gold_change < 0:  # Spent gold
            # Check if spending was efficient
            round_num = new_state.get("round", 1)
            expected_spending = round_num * 10  # Rough heuristic
            
            if abs(gold_change) <= expected_spending:
                return 0.2
            else:
                return -0.1  # Overspending
        
        # Inventory management
        inventory_size = len(new_state.get("inventory_items", []))
        if inventory_size > 5:  # Too many items in inventory
            return -0.2
        
        return 0.0
    
    def _evaluate_build_coherence(self, state: Dict[str, Any]) -> float:
        """Evaluate how coherent the build strategy is"""
        encoded = self.state_encoder.encode_state(state)
        
        # Use multiple build metrics
        type_diversity = encoded["build_metrics"][1]
        class_synergy = encoded["build_metrics"][6]
        power_level = encoded["build_metrics"][4]
        
        # Coherent builds have good synergy and power, moderate diversity
        coherence = (class_synergy * 0.4 + power_level * 0.4 + 
                    (1.0 - abs(type_diversity - 0.5)) * 0.2)
        
        return coherence
    
    def _evaluate_timing(self, prev_state: Dict[str, Any], new_state: Dict[str, Any],
                        action: Dict[str, Any]) -> float:
        """Evaluate timing of decisions"""
        round_num = new_state.get("round", 1)
        action_type = action.get("action_type")
        
        # Early game: focus on economy and basic items
        if round_num <= 3:
            if action_type == "buy_item" and action.get("item_cost", 0) > 15:
                return -0.2  # Too expensive for early game
            return 0.1
        
        # Mid game: build synergies
        elif round_num <= 6:
            if action_type == "place_item":
                return 0.2  # Good time to place items
            return 0.0
        
        # Late game: optimize and prepare for battles
        else:
            if action_type in ["sell_item", "rotate_item"]:
                return 0.1  # Optimization actions
            return 0.0
    
    def _evaluate_risk_management(self, prev_state: Dict[str, Any], new_state: Dict[str, Any],
                                 action: Dict[str, Any]) -> float:
        """Evaluate risk management decisions"""
        health = new_state.get("health", 100)
        lives = new_state.get("lives", 3)
        
        # High risk situation
        if health < 30 or lives <= 1:
            if action.get("action_type") == "buy_item":
                # Should focus on defensive items
                item_type = action.get("item_type", "")
                if item_type in ["armor", "shield", "potion"]:
                    return 0.3
                else:
                    return -0.2
        
        # Low risk situation
        else:
            if action.get("action_type") == "reroll_shop":
                return 0.1  # Can afford to reroll
        
        return 0.0
    
    def _evaluate_battle_performance(self, info: Dict[str, Any]) -> float:
        """Evaluate battle performance"""
        if info.get("battle_won", False):
            damage_dealt = info.get("damage_dealt", 0)
            damage_taken = info.get("damage_taken", 0)
            
            # Bonus for efficient wins
            if damage_taken == 0:
                return 1.0  # Perfect win
            elif damage_dealt > damage_taken * 2:
                return 0.8  # Dominant win
            else:
                return 0.5  # Standard win
        else:
            # Loss, but consider how close it was
            damage_dealt = info.get("damage_dealt", 0)
            damage_taken = info.get("damage_taken", 100)
            
            if damage_dealt > damage_taken * 0.8:
                return -0.2  # Close loss
            else:
                return -0.8  # Decisive loss
    
    def _evaluate_final_ranking(self, info: Dict[str, Any]) -> float:
        """Evaluate final game ranking"""
        final_rank = info.get("final_rank", 8)
        total_players = info.get("total_players", 8)
        
        # Normalize rank to 0-1 scale (1 = first place, 0 = last place)
        normalized_rank = (total_players - final_rank) / (total_players - 1)
        
        # Convert to reward scale
        if normalized_rank >= 0.8:  # Top 20%
            return 1.0
        elif normalized_rank >= 0.5:  # Top 50%
            return 0.5
        elif normalized_rank >= 0.2:  # Bottom 50%
            return -0.2
        else:  # Bottom 20%
            return -1.0
    
    # Helper methods
    def _calculate_space_efficiency(self, state: Dict[str, Any]) -> float:
        """Calculate how efficiently space is used"""
        occupied_cells = len(state.get("occupied_cells", set()))
        total_cells = 9 * 7  # Grid size
        return occupied_cells / total_cells
    
    def _evaluate_item_build_fit(self, item_name: str, state: Dict[str, Any]) -> float:
        """Evaluate how well an item fits the current build"""
        # Simplified: would need item database integration
        return 0.5  # Neutral fit
    
    def _evaluate_shop_quality(self, state: Dict[str, Any]) -> float:
        """Evaluate quality of current shop"""
        shop_items = state.get("shop_items", [])
        quality_score = 0.0
        
        for item in shop_items:
            if item:
                power_level = item.get("power_level", 1.0)
                cost = item.get("cost", 10)
                efficiency = power_level / max(cost, 1)
                quality_score += efficiency
        
        return min(1.0, quality_score / 5.0)  # Normalize by shop size


class CurriculumRewardFunction(RewardFunction):
    """Curriculum learning reward function that adapts based on training progress"""
    
    def __init__(self, difficulty_level: float = 0.0):
        self.difficulty_level = difficulty_level  # 0.0 = easy, 1.0 = hard
        self.basic_function = BasicRewardFunction()
        self.advanced_function = AdvancedRewardFunction()
        
        # Interpolate between basic and advanced based on difficulty
        components = []
        for comp in self.basic_function.components.values():
            new_comp = RewardComponent(
                comp.name, comp.reward_type, 
                comp.weight * (1.0 - difficulty_level),
                comp.description, comp.min_value, comp.max_value
            )
            components.append(new_comp)
        
        for comp in self.advanced_function.components.values():
            new_comp = RewardComponent(
                comp.name, comp.reward_type,
                comp.weight * difficulty_level,
                comp.description, comp.min_value, comp.max_value
            )
            components.append(new_comp)
        
        super().__init__("curriculum", components)
    
    def calculate_reward(self, prev_state: Dict[str, Any], action: Dict[str, Any],
                        new_state: Dict[str, Any], info: Dict[str, Any]) -> Dict[str, float]:
        # Combine rewards from both functions
        basic_rewards = self.basic_function.calculate_reward(prev_state, action, new_state, info)
        advanced_rewards = self.advanced_function.calculate_reward(prev_state, action, new_state, info)
        
        combined_rewards = {}
        
        # Weight basic rewards
        for name, value in basic_rewards.items():
            combined_rewards[name] = value * (1.0 - self.difficulty_level)
        
        # Weight advanced rewards
        for name, value in advanced_rewards.items():
            if name in combined_rewards:
                combined_rewards[name] += value * self.difficulty_level
            else:
                combined_rewards[name] = value * self.difficulty_level
        
        return combined_rewards
    
    def update_difficulty(self, new_difficulty: float):
        """Update curriculum difficulty level"""
        self.difficulty_level = np.clip(new_difficulty, 0.0, 1.0)


class RewardFunctionManager:
    """Manages different reward functions and provides unified interface"""
    
    def __init__(self):
        self.functions = {
            "basic": BasicRewardFunction(),
            "advanced": AdvancedRewardFunction(),
            "curriculum": CurriculumRewardFunction()
        }
        self.current_function = "basic"
    
    def set_function(self, function_name: str):
        """Set the active reward function"""
        if function_name in self.functions:
            self.current_function = function_name
        else:
            raise ValueError(f"Unknown reward function: {function_name}")
    
    def calculate_reward(self, prev_state: Dict[str, Any], action: Dict[str, Any],
                        new_state: Dict[str, Any], info: Dict[str, Any]) -> float:
        """Calculate reward using current function"""
        function = self.functions[self.current_function]
        return function.get_total_reward(prev_state, action, new_state, info)
    
    def get_reward_breakdown(self, prev_state: Dict[str, Any], action: Dict[str, Any],
                           new_state: Dict[str, Any], info: Dict[str, Any]) -> Dict[str, float]:
        """Get detailed reward breakdown"""
        function = self.functions[self.current_function]
        return function.calculate_reward(prev_state, action, new_state, info)
    
    def update_curriculum_difficulty(self, difficulty: float):
        """Update curriculum learning difficulty"""
        if "curriculum" in self.functions:
            self.functions["curriculum"].update_difficulty(difficulty)
