"""
Test the Real-time Analysis Pipeline
"""

import time
import threading
from assist_mode.real_time_analysis_pipeline import (
    AnalysisPipeline, RealtimeAssistant, StateChangeDetector,
    AnalysisConfig, AnalysisResult, PerformanceMetrics,
    create_default_config, create_high_performance_config, create_debug_config
)
from assist_mode.vision_system_integration import GameStateCapture, DetectedItem

def create_mock_game_state_capture():
    """Create a mock game state capture for testing"""
    return GameStateCapture(
        timestamp=time.time(),
        backpack_items=[
            DetectedItem("Hero Sword", 0.95, (100, 100, 50, 50), (1, 1)),
            DetectedItem("Leather Armor", 0.87, (200, 150, 50, 50), (3, 2))
        ],
        shop_items=[
            DetectedItem("Double Axe", 0.92, (50, 300, 60, 40)),
            None,
            DetectedItem("Health Potion", 0.88, (150, 300, 30, 30)),
            None,
            DetectedItem("Magic Ring", 0.85, (250, 300, 40, 40))
        ],
        player_stats={"health": 85, "gold": 42, "round": 3, "lives": 3},
        game_phase="shop",
        confidence_score=0.89
    )

def test_real_time_analysis_pipeline():
    print("⚡ TESTING REAL-TIME ANALYSIS PIPELINE")
    print("=" * 60)
    
    # Test configuration creation
    print("\n⚙️ TESTING CONFIGURATION CREATION:")
    print("-" * 30)
    
    default_config = create_default_config()
    high_perf_config = create_high_performance_config()
    debug_config = create_debug_config()
    
    print(f"  Default config:")
    print(f"    Analysis FPS: {default_config.analysis_fps}")
    print(f"    Vision FPS: {default_config.vision_fps}")
    print(f"    Max recommendations: {default_config.max_recommendations}")
    print(f"    Debug logging: {default_config.enable_debug_logging}")
    
    print(f"\n  High performance config:")
    print(f"    Analysis FPS: {high_perf_config.analysis_fps}")
    print(f"    Vision FPS: {high_perf_config.vision_fps}")
    print(f"    Max recommendations: {high_perf_config.max_recommendations}")
    
    print(f"\n  Debug config:")
    print(f"    Analysis FPS: {debug_config.analysis_fps}")
    print(f"    Debug logging: {debug_config.enable_debug_logging}")
    print(f"    Log file: {debug_config.log_file_path}")
    
    # Test performance metrics
    print("\n📊 TESTING PERFORMANCE METRICS:")
    print("-" * 30)
    
    metrics = PerformanceMetrics()
    
    # Simulate some analyses
    test_times = [0.05, 0.08, 0.12, 0.06, 0.15, 0.09]
    test_successes = [True, True, False, True, True, True]
    
    for processing_time, success in zip(test_times, test_successes):
        metrics.update(processing_time, success)
    
    metrics_dict = metrics.to_dict()
    print(f"  Performance metrics after {len(test_times)} analyses:")
    print(f"    Total analyses: {metrics_dict['total_analyses']}")
    print(f"    Success rate: {metrics_dict['success_rate']:.1%}")
    print(f"    Average time: {metrics_dict['average_processing_time']*1000:.1f} ms")
    print(f"    Max time: {metrics_dict['max_processing_time']*1000:.1f} ms")
    print(f"    Min time: {metrics_dict['min_processing_time']*1000:.1f} ms")
    
    # Test state change detector
    print("\n🔍 TESTING STATE CHANGE DETECTOR:")
    print("-" * 30)
    
    config = create_default_config()
    change_detector = StateChangeDetector(config)
    
    # Create initial state
    initial_state = create_mock_game_state_capture()
    change1 = change_detector.detect_change(initial_state)
    print(f"  Initial state change detected: {change1}")
    
    # Same state (no change)
    same_state = create_mock_game_state_capture()
    change2 = change_detector.detect_change(same_state)
    print(f"  Same state change detected: {change2}")
    
    # Modified state (change)
    modified_state = create_mock_game_state_capture()
    modified_state.player_stats["gold"] = 50  # Gold changed
    modified_state.backpack_items.append(
        DetectedItem("New Item", 0.9, (300, 200, 50, 50), (5, 3))
    )
    change3 = change_detector.detect_change(modified_state)
    print(f"  Modified state change detected: {change3}")
    
    # Phase change
    phase_change_state = create_mock_game_state_capture()
    phase_change_state.game_phase = "battle"
    change4 = change_detector.detect_change(phase_change_state)
    print(f"  Phase change detected: {change4}")
    
    # Test analysis result
    print("\n📋 TESTING ANALYSIS RESULT:")
    print("-" * 30)
    
    # Create mock analysis result
    mock_result = AnalysisResult(
        timestamp=time.time(),
        game_state_capture=initial_state,
        build_analysis=None,  # Would be BuildAnalysis object
        recommendations=[],   # Would be list of Recommendation objects
        processing_time=0.085,
        confidence_score=0.89,
        state_changed=True
    )
    
    result_dict = mock_result.to_dict()
    print(f"  Analysis result:")
    print(f"    Timestamp: {result_dict['timestamp']}")
    print(f"    Has game state: {result_dict['has_game_state']}")
    print(f"    Processing time: {result_dict['processing_time']*1000:.1f} ms")
    print(f"    Confidence: {result_dict['confidence_score']:.3f}")
    print(f"    State changed: {result_dict['state_changed']}")
    
    # Test analysis pipeline (without actually starting it)
    print("\n🔧 TESTING ANALYSIS PIPELINE SETUP:")
    print("-" * 30)
    
    pipeline = AnalysisPipeline(config)
    
    print(f"  Pipeline components:")
    print(f"    Vision system: {pipeline.vision_system is not None}")
    print(f"    Recommendation engine: {pipeline.recommendation_engine is not None}")
    print(f"    Change detector: {pipeline.change_detector is not None}")
    print(f"    Running: {pipeline.running}")
    
    # Test status
    status = pipeline.get_status()
    print(f"\n  Pipeline status:")
    print(f"    Running: {status['running']}")
    print(f"    Queue size: {status['queue_size']}")
    print(f"    Max queue size: {status['max_queue_size']}")
    print(f"    Analysis FPS: {status['config']['analysis_fps']}")
    
    # Test callback system
    print("\n📞 TESTING CALLBACK SYSTEM:")
    print("-" * 30)
    
    callback_results = []
    
    def test_callback(result: AnalysisResult):
        callback_results.append(result)
        print(f"    Callback received result with confidence {result.confidence_score:.3f}")
    
    pipeline.add_result_callback(test_callback)
    print(f"  Added callback to pipeline")
    print(f"  Callbacks registered: {len(pipeline.result_callbacks)}")
    
    # Test realtime assistant
    print("\n🤖 TESTING REALTIME ASSISTANT:")
    print("-" * 30)
    
    assistant = RealtimeAssistant(config)
    
    print(f"  Assistant created:")
    print(f"    Config: {assistant.config is not None}")
    print(f"    Pipeline: {assistant.pipeline is not None}")
    
    # Test status
    assistant_status = assistant.get_status()
    print(f"\n  Assistant status:")
    print(f"    Running: {assistant_status['running']}")
    print(f"    Current recommendations: {assistant_status['current_recommendations']}")
    print(f"    Has build analysis: {assistant_status['has_build_analysis']}")
    print(f"    Last update time: {assistant_status['last_update_time']}")
    
    # Test current state getters
    current_recs = assistant.get_current_recommendations()
    current_analysis = assistant.get_current_build_analysis()
    
    print(f"\n  Current state:")
    print(f"    Recommendations: {len(current_recs)}")
    print(f"    Build analysis: {current_analysis is not None}")
    
    # Test queue operations
    print("\n📥 TESTING QUEUE OPERATIONS:")
    print("-" * 30)
    
    # Test getting results from empty queue
    latest_result = pipeline.get_latest_result()
    all_results = pipeline.get_all_results()
    
    print(f"  Empty queue operations:")
    print(f"    Latest result: {latest_result is not None}")
    print(f"    All results: {len(all_results)}")
    
    # Test performance monitoring
    print("\n📈 TESTING PERFORMANCE MONITORING:")
    print("-" * 30)
    
    perf_metrics = pipeline.get_performance_metrics()
    perf_dict = perf_metrics.to_dict()
    
    print(f"  Performance metrics:")
    print(f"    Total analyses: {perf_dict['total_analyses']}")
    print(f"    Success rate: {perf_dict['success_rate']:.1%}")
    print(f"    Average time: {perf_dict['average_processing_time']*1000:.1f} ms")
    
    # Test configuration variations
    print("\n🎛️ TESTING CONFIGURATION VARIATIONS:")
    print("-" * 30)
    
    configs = {
        "Low Performance": AnalysisConfig(analysis_fps=1, vision_fps=2),
        "Medium Performance": AnalysisConfig(analysis_fps=3, vision_fps=5),
        "High Performance": AnalysisConfig(analysis_fps=10, vision_fps=15)
    }
    
    for name, test_config in configs.items():
        test_pipeline = AnalysisPipeline(test_config)
        test_status = test_pipeline.get_status()
        
        print(f"  {name}:")
        print(f"    Analysis FPS: {test_status['config']['analysis_fps']}")
        print(f"    Vision FPS: {test_config.vision_fps}")
        print(f"    Max recommendations: {test_status['config']['max_recommendations']}")
    
    # Test error handling
    print("\n❌ TESTING ERROR HANDLING:")
    print("-" * 30)
    
    # Test with invalid callback
    def error_callback(result: AnalysisResult):
        raise Exception("Test error in callback")
    
    pipeline.add_result_callback(error_callback)
    print(f"  Added error-prone callback")
    print(f"  Total callbacks: {len(pipeline.result_callbacks)}")
    
    # Test state change detection edge cases
    print("\n🔍 TESTING EDGE CASES:")
    print("-" * 30)
    
    edge_detector = StateChangeDetector(config)
    
    # Test with None state
    none_change = edge_detector.detect_change(None)
    print(f"  None state change: {none_change}")
    
    # Test with empty state
    empty_state = GameStateCapture(
        timestamp=time.time(),
        backpack_items=[],
        shop_items=[None] * 5,
        player_stats={},
        game_phase="shop",
        confidence_score=0.5
    )
    empty_change = edge_detector.detect_change(empty_state)
    print(f"  Empty state change: {empty_change}")
    
    # Test rapid state changes
    print("\n⚡ TESTING RAPID STATE CHANGES:")
    print("-" * 30)
    
    rapid_detector = StateChangeDetector(config)
    changes_detected = 0
    
    for i in range(5):
        test_state = create_mock_game_state_capture()
        test_state.player_stats["gold"] = 40 + i  # Gradual change
        
        if rapid_detector.detect_change(test_state):
            changes_detected += 1
    
    print(f"  Changes detected in 5 rapid updates: {changes_detected}")
    
    # Test memory usage with large history
    print("\n💾 TESTING MEMORY MANAGEMENT:")
    print("-" * 30)
    
    large_history_config = AnalysisConfig(state_history_size=100)
    memory_detector = StateChangeDetector(large_history_config)
    
    # Add many states
    for i in range(150):  # More than history size
        test_state = create_mock_game_state_capture()
        test_state.timestamp = time.time() + i
        memory_detector.detect_change(test_state)
    
    history_size = len(memory_detector.previous_states)
    print(f"  History size after 150 additions: {history_size}")
    print(f"  Max history size: {large_history_config.state_history_size}")
    print(f"  Memory management working: {history_size <= large_history_config.state_history_size}")
    
    # Test timing accuracy
    print("\n⏱️ TESTING TIMING ACCURACY:")
    print("-" * 30)
    
    timing_metrics = PerformanceMetrics()
    
    # Simulate precise timing measurements
    precise_times = [0.001, 0.002, 0.0015, 0.0025, 0.003]
    
    for t in precise_times:
        timing_metrics.update(t, True)
    
    timing_dict = timing_metrics.to_dict()
    print(f"  Precise timing test:")
    print(f"    Average: {timing_dict['average_processing_time']*1000:.3f} ms")
    print(f"    Min: {timing_dict['min_processing_time']*1000:.3f} ms")
    print(f"    Max: {timing_dict['max_processing_time']*1000:.3f} ms")
    
    print("\n" + "=" * 60)
    print("✅ REAL-TIME ANALYSIS PIPELINE TEST COMPLETE")

if __name__ == "__main__":
    test_real_time_analysis_pipeline()
