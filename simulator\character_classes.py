"""
Character Classes and Starting Bags System

Implements the class-specific starting bags and their effects as specified
in Section 5.1 of DeepResearch.txt:

- Ranger: Ranger Bag (10% crit + 3% per Luck) or Vineweave Basket
- Reaper: Storage Coffin (25% poison on activation) or Relic Case  
- Berserker: Duffle Bag (Battle Rage at <50% HP) or Utility Pouch
- Pyromancer: Fire Pit (generates Flame, +4 HP per Fire item) or Offering Bowl
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from simulator.status_effects import StatusEffectManager
from simulator.resources import ResourceManager


class CharacterClass(Enum):
    RANGER = "Ranger"
    REAPER = "Reaper"
    BERSERKER = "Berserker"
    PYROMANCER = "Pyromancer"


@dataclass
class StartingBagChoice:
    """Represents a starting bag option for a class"""
    name: str
    description: str
    grid_size: Tuple[int, int]  # (width, height)
    base_slots: int
    passive_effect: str
    effect_description: str


class StartingBagEffect:
    """Base class for starting bag effects"""
    
    def __init__(self, bag_name: str):
        self.bag_name = bag_name
        self.is_active = True
    
    def apply_effect(self, player_state: Dict[str, Any], 
                    status_manager: StatusEffectManager,
                    items_in_bag: List[Any]) -> Dict[str, Any]:
        """Apply the bag's passive effect"""
        return {}
    
    def on_item_activation(self, item: Any, player_state: Dict[str, Any]) -> Dict[str, Any]:
        """Called when an item in the bag activates"""
        return {}
    
    def on_battle_start(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        """Called at the start of battle"""
        return {}


class RangerBagEffect(StartingBagEffect):
    """Ranger Bag: 10% crit chance + 3% per Luck stack"""
    
    def apply_effect(self, player_state: Dict[str, Any], 
                    status_manager: StatusEffectManager,
                    items_in_bag: List[Any]) -> Dict[str, Any]:
        """Calculate crit chance bonus for weapons in bag"""
        base_crit = 0.10  # 10% base
        luck_stacks = status_manager.get_effect_stacks("Luck")
        luck_bonus = luck_stacks * 0.03  # 3% per Luck stack
        
        total_crit_bonus = base_crit + luck_bonus
        
        return {
            "crit_chance_bonus": total_crit_bonus,
            "affected_items": len(items_in_bag)
        }


class VineweaveBasketEffect(StartingBagEffect):
    """Vineweave Basket: Alternative Ranger starting bag"""
    
    def apply_effect(self, player_state: Dict[str, Any], 
                    status_manager: StatusEffectManager,
                    items_in_bag: List[Any]) -> Dict[str, Any]:
        # Implementation would depend on specific mechanics
        return {"nature_synergy_bonus": True}


class StorageCoffinEffect(StartingBagEffect):
    """Storage Coffin: 25% chance to inflict 1 Poison on item activation"""
    
    def on_item_activation(self, item: Any, player_state: Dict[str, Any]) -> Dict[str, Any]:
        """25% chance to inflict poison when any item in bag activates"""
        import random
        
        if random.random() < 0.25:  # 25% chance
            return {
                "inflict_poison": 1,
                "target": "opponent"
            }
        return {}


class RelicCaseEffect(StartingBagEffect):
    """Relic Case: Alternative Reaper starting bag"""
    
    def apply_effect(self, player_state: Dict[str, Any], 
                    status_manager: StatusEffectManager,
                    items_in_bag: List[Any]) -> Dict[str, Any]:
        # Implementation would depend on specific mechanics
        return {"relic_synergy_bonus": True}


class DuffleBagEffect(StartingBagEffect):
    """Duffle Bag: Battle Rage when HP < 50% (30% faster, 20% damage reduction)"""
    
    def apply_effect(self, player_state: Dict[str, Any], 
                    status_manager: StatusEffectManager,
                    items_in_bag: List[Any]) -> Dict[str, Any]:
        """Check if Battle Rage should activate"""
        current_hp = player_state.get("health", 100)
        max_hp = player_state.get("max_health", 100)
        
        if current_hp < (max_hp * 0.5):  # Below 50% HP
            # Activate Battle Rage for 5 seconds
            return {
                "battle_rage_active": True,
                "cooldown_multiplier": 0.7,  # 30% faster (multiply by 0.7)
                "damage_reduction": 0.2,     # 20% damage reduction
                "duration": 5.0,
                "affected_items": len(items_in_bag)
            }
        
        return {"battle_rage_active": False}


class UtilityPouchEffect(StartingBagEffect):
    """Utility Pouch: Alternative Berserker starting bag"""
    
    def apply_effect(self, player_state: Dict[str, Any], 
                    status_manager: StatusEffectManager,
                    items_in_bag: List[Any]) -> Dict[str, Any]:
        # Implementation would depend on specific mechanics
        return {"utility_bonus": True}


class FirePitEffect(StartingBagEffect):
    """Fire Pit: Generates Flame for 1 Gold, +4 max HP per Fire item"""
    
    def on_battle_start(self, player_state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate Flame item offer and calculate HP bonus"""
        fire_items_in_bag = 0  # Would count actual Fire items
        hp_bonus = fire_items_in_bag * 4
        
        return {
            "generate_flame_offer": True,
            "flame_cost": 1,
            "max_health_bonus": hp_bonus
        }
    
    def apply_effect(self, player_state: Dict[str, Any], 
                    status_manager: StatusEffectManager,
                    items_in_bag: List[Any]) -> Dict[str, Any]:
        """Calculate ongoing HP bonus from Fire items"""
        fire_items = sum(1 for item in items_in_bag 
                        if hasattr(item, 'subtype') and 'Fire' in item.subtype)
        
        return {
            "max_health_bonus": fire_items * 4,
            "fire_items_count": fire_items
        }


class OfferingBowlEffect(StartingBagEffect):
    """Offering Bowl: Alternative Pyromancer starting bag"""
    
    def apply_effect(self, player_state: Dict[str, Any], 
                    status_manager: StatusEffectManager,
                    items_in_bag: List[Any]) -> Dict[str, Any]:
        # Implementation would depend on specific mechanics
        return {"offering_bonus": True}


class CharacterClassManager:
    """Manages character classes and their starting bag choices"""
    
    def __init__(self):
        self.starting_bags = self._initialize_starting_bags()
        self.bag_effects = self._initialize_bag_effects()
    
    def _initialize_starting_bags(self) -> Dict[CharacterClass, List[StartingBagChoice]]:
        """Initialize starting bag choices for each class"""
        return {
            CharacterClass.RANGER: [
                StartingBagChoice(
                    name="Ranger Bag",
                    description="Grants 10% critical hit chance to all weapons, +3% per Luck",
                    grid_size=(3, 4),
                    base_slots=12,
                    passive_effect="crit_chance_bonus",
                    effect_description="10% crit + 3% per Luck stack"
                ),
                StartingBagChoice(
                    name="Vineweave Basket",
                    description="Alternative Ranger starting bag with nature synergies",
                    grid_size=(3, 4),
                    base_slots=12,
                    passive_effect="nature_synergy",
                    effect_description="Nature item synergies"
                )
            ],
            
            CharacterClass.REAPER: [
                StartingBagChoice(
                    name="Storage Coffin",
                    description="25% chance to inflict 1 Poison when any item activates",
                    grid_size=(3, 4),
                    base_slots=12,
                    passive_effect="poison_chance",
                    effect_description="25% poison on item activation"
                ),
                StartingBagChoice(
                    name="Relic Case",
                    description="Alternative Reaper starting bag with relic synergies",
                    grid_size=(3, 4),
                    base_slots=12,
                    passive_effect="relic_synergy",
                    effect_description="Relic item synergies"
                )
            ],
            
            CharacterClass.BERSERKER: [
                StartingBagChoice(
                    name="Duffle Bag",
                    description="Battle Rage when HP < 50%: 30% faster, 20% damage reduction",
                    grid_size=(3, 4),
                    base_slots=12,
                    passive_effect="battle_rage",
                    effect_description="Battle Rage at low HP"
                ),
                StartingBagChoice(
                    name="Utility Pouch",
                    description="Alternative Berserker starting bag with utility bonuses",
                    grid_size=(3, 4),
                    base_slots=12,
                    passive_effect="utility_bonus",
                    effect_description="Utility item bonuses"
                )
            ],
            
            CharacterClass.PYROMANCER: [
                StartingBagChoice(
                    name="Fire Pit",
                    description="Generates Flame for 1 Gold, +4 max HP per Fire item",
                    grid_size=(3, 4),
                    base_slots=12,
                    passive_effect="fire_synergy",
                    effect_description="Flame generation + Fire HP bonus"
                ),
                StartingBagChoice(
                    name="Offering Bowl",
                    description="Alternative Pyromancer starting bag with offering mechanics",
                    grid_size=(3, 4),
                    base_slots=12,
                    passive_effect="offering_bonus",
                    effect_description="Offering mechanics"
                )
            ]
        }
    
    def _initialize_bag_effects(self) -> Dict[str, StartingBagEffect]:
        """Initialize bag effect implementations"""
        return {
            "Ranger Bag": RangerBagEffect("Ranger Bag"),
            "Vineweave Basket": VineweaveBasketEffect("Vineweave Basket"),
            "Storage Coffin": StorageCoffinEffect("Storage Coffin"),
            "Relic Case": RelicCaseEffect("Relic Case"),
            "Duffle Bag": DuffleBagEffect("Duffle Bag"),
            "Utility Pouch": UtilityPouchEffect("Utility Pouch"),
            "Fire Pit": FirePitEffect("Fire Pit"),
            "Offering Bowl": OfferingBowlEffect("Offering Bowl")
        }
    
    def get_starting_bag_choices(self, character_class: CharacterClass) -> List[StartingBagChoice]:
        """Get starting bag choices for a character class"""
        return self.starting_bags.get(character_class, [])
    
    def get_bag_effect(self, bag_name: str) -> Optional[StartingBagEffect]:
        """Get the effect implementation for a bag"""
        return self.bag_effects.get(bag_name)
    
    def apply_bag_effects(self, bag_name: str, player_state: Dict[str, Any],
                         status_manager: StatusEffectManager,
                         items_in_bag: List[Any]) -> Dict[str, Any]:
        """Apply the effects of a starting bag"""
        effect = self.get_bag_effect(bag_name)
        if effect:
            return effect.apply_effect(player_state, status_manager, items_in_bag)
        return {}
