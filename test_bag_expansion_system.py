"""
Test the Bag Expansion Mechanics System
"""

from simulator.bag_expansion_system import BagExpansionManager, BagExpansion
from simulator.enhanced_item_database import EnhancedItem, ItemRarity, ItemClass, ItemType

def create_test_bag(name: str, grid_shape: list = None) -> EnhancedItem:
    """Create a test bag item"""
    if grid_shape is None:
        grid_shape = [[True, True], [True, True]]  # Default 2x2 bag
    
    return EnhancedItem(
        item_id=f"bag_{name}",
        item_name=name,
        rarity=ItemRarity.RARE,
        item_class=ItemClass.NEUTRAL,
        item_type=ItemType.BAG,
        subtype=["Storage"],
        cost=20,
        grid_shape=grid_shape,
        sockets=0,
        in_shop=True,
        stats={},
        effects=[],
        description=f"Test bag: {name}"
    )

def test_bag_expansion_system():
    print("🎒 TESTING BAG EXPANSION MECHANICS SYSTEM")
    print("=" * 60)
    
    manager = BagExpansionManager()
    
    # Test initial state
    print("\n📏 TESTING INITIAL STATE:")
    print("-" * 30)
    
    initial_size = manager.get_total_grid_size()
    available_cells = manager.get_available_cells()
    
    print(f"  Base grid size: {initial_size[0]}x{initial_size[1]}")
    print(f"  Available cells: {len(available_cells)}")
    print(f"  Expected: {manager.base_width * manager.base_height}")
    
    # Test bag definitions
    print("\n📚 TESTING BAG DEFINITIONS:")
    print("-" * 30)
    
    print(f"  Loaded {len(manager.bag_definitions)} bag types:")
    for i, (bag_name, expansion) in enumerate(list(manager.bag_definitions.items())[:5]):
        print(f"    {i+1}. {bag_name}")
        print(f"       Expansion: {expansion.width_bonus}x{expansion.height_bonus} (+{expansion.total_slots_bonus} slots)")
        print(f"       Effects: {', '.join(expansion.special_effects) if expansion.special_effects else 'None'}")
        print(f"       Description: {expansion.description}")
        print()
    
    # Test bag placement
    print("\n🎯 TESTING BAG PLACEMENT:")
    print("-" * 30)
    
    # Create and place a small bag
    small_bag = create_test_bag("Small Bag")
    placement_success = manager.place_bag(small_bag, (2, 2))
    
    print(f"  Placed Small Bag at (2,2): {placement_success}")
    
    if placement_success:
        new_size = manager.get_total_grid_size()
        new_available = len(manager.get_available_cells())
        
        print(f"  Grid size after placement: {new_size[0]}x{new_size[1]}")
        print(f"  Available cells after placement: {new_available}")
        print(f"  Net change: {new_available - len(available_cells)} cells")
    
    # Test expansion effects
    print("\n⚡ TESTING EXPANSION EFFECTS:")
    print("-" * 30)
    
    effects = manager.get_expansion_effects()
    print(f"  Active expansion effects: {len(effects)}")
    for bag_name, bag_effects in effects.items():
        print(f"    {bag_name}: {', '.join(bag_effects)}")
    
    # Test specialty bags
    print("\n🔧 TESTING SPECIALTY BAGS:")
    print("-" * 30)
    
    # Place a quiver
    quiver = create_test_bag("Quiver", [[True], [True], [True], [True]])  # 1x4 shape
    quiver_success = manager.place_bag(quiver, (5, 1))
    
    print(f"  Placed Quiver at (5,1): {quiver_success}")
    
    # Place a potion belt
    potion_belt = create_test_bag("Potion Belt", [[True, True, True, True]])  # 4x1 shape
    belt_success = manager.place_bag(potion_belt, (1, 5))
    
    print(f"  Placed Potion Belt at (1,5): {belt_success}")
    
    # Check total effects
    all_effects = manager.get_expansion_effects()
    print(f"  Total active effects: {len(all_effects)}")
    for bag_name, bag_effects in all_effects.items():
        print(f"    {bag_name}: {', '.join(bag_effects)}")
    
    # Test bag contents
    print("\n📦 TESTING BAG CONTENTS:")
    print("-" * 30)
    
    # Add items to bags
    manager.place_item_in_bag("Health Potion", "Small Bag")
    manager.place_item_in_bag("Mana Potion", "Small Bag")
    manager.place_item_in_bag("Arrow", "Quiver")
    manager.place_item_in_bag("Poison Arrow", "Quiver")
    
    small_bag_contents = manager.get_bag_contents("Small Bag")
    quiver_contents = manager.get_bag_contents("Quiver")
    
    print(f"  Small Bag contents: {small_bag_contents}")
    print(f"  Quiver contents: {quiver_contents}")
    
    # Test bag movement
    print("\n🚚 TESTING BAG MOVEMENT:")
    print("-" * 30)
    
    print("  Before movement:")
    print(f"    Small Bag contents: {manager.get_bag_contents('Small Bag')}")
    
    # Move the small bag
    move_success = manager.move_bag("Small Bag", (6, 3))
    print(f"  Moved Small Bag to (6,3): {move_success}")
    
    print("  After movement:")
    print(f"    Small Bag contents: {manager.get_bag_contents('Small Bag')}")
    
    # Test invalid placements
    print("\n❌ TESTING INVALID PLACEMENTS:")
    print("-" * 30)
    
    # Try to place a bag where another exists
    conflicting_bag = create_test_bag("Conflicting Bag")
    conflict_result = manager.place_bag(conflicting_bag, (6, 3))  # Same position as moved bag
    print(f"  Placing bag at occupied position: {conflict_result}")
    
    # Try to place a bag outside grid
    outside_bag = create_test_bag("Outside Bag")
    outside_result = manager.place_bag(outside_bag, (50, 50))
    print(f"  Placing bag outside grid: {outside_result}")
    
    # Test bag removal
    print("\n🗑️ TESTING BAG REMOVAL:")
    print("-" * 30)
    
    size_before_removal = manager.get_total_grid_size()
    available_before_removal = len(manager.get_available_cells())
    
    print(f"  Before removal - Grid: {size_before_removal}, Available: {available_before_removal}")
    
    # Remove the quiver
    removal_success = manager.remove_bag("Quiver")
    print(f"  Removed Quiver: {removal_success}")
    
    size_after_removal = manager.get_total_grid_size()
    available_after_removal = len(manager.get_available_cells())
    
    print(f"  After removal - Grid: {size_after_removal}, Available: {available_after_removal}")
    
    # Test grid visualization
    print("\n🗺️ TESTING GRID VISUALIZATION:")
    print("-" * 30)
    
    grid_viz = manager.get_grid_visualization()
    print("  Current grid state:")
    print("    Legend: O=base grid, +=expansion, X=occupied, .=empty")
    
    for i, row in enumerate(grid_viz[:10]):  # Show first 10 rows
        row_str = ''.join(row[:15])  # Show first 15 columns
        print(f"    Row {i:2d}: {row_str}")
    
    # Test large bag
    print("\n🏢 TESTING LARGE BAG:")
    print("-" * 30)
    
    large_bag = create_test_bag("Dimensional Pouch", [[True, True], [True, True]])
    large_success = manager.place_bag(large_bag, (0, 0))
    
    print(f"  Placed Dimensional Pouch: {large_success}")
    
    if large_success:
        final_size = manager.get_total_grid_size()
        final_available = len(manager.get_available_cells())
        
        print(f"  Final grid size: {final_size[0]}x{final_size[1]}")
        print(f"  Final available cells: {final_available}")
        
        # Check dimensional pouch effects
        dimensional_effects = manager.get_expansion_effects()
        if "Dimensional Pouch" in dimensional_effects:
            print(f"  Dimensional effects: {dimensional_effects['Dimensional Pouch']}")
    
    # Test class-specific bags
    print("\n🏛️ TESTING CLASS-SPECIFIC BAGS:")
    print("-" * 30)
    
    class_bags = ["Berserker Pack", "Ranger Satchel", "Reaper Coffin", "Pyro Furnace"]
    
    for bag_name in class_bags:
        if bag_name in manager.bag_definitions:
            expansion = manager.bag_definitions[bag_name]
            print(f"  {bag_name}:")
            print(f"    Expansion: {expansion.width_bonus}x{expansion.height_bonus}")
            print(f"    Effects: {', '.join(expansion.special_effects)}")
    
    # Summary statistics
    print("\n📊 FINAL STATISTICS:")
    print("-" * 30)
    
    total_bags = len(manager.placed_bags)
    total_effects = sum(len(effects) for effects in manager.get_expansion_effects().values())
    final_grid_size = manager.get_total_grid_size()
    
    print(f"  Total bags placed: {total_bags}")
    print(f"  Total active effects: {total_effects}")
    print(f"  Final grid size: {final_grid_size[0]}x{final_grid_size[1]}")
    print(f"  Grid expansion: {final_grid_size[0] * final_grid_size[1] - manager.base_width * manager.base_height} cells")
    
    print("\n" + "=" * 60)
    print("✅ BAG EXPANSION SYSTEM TEST COMPLETE")

if __name__ == "__main__":
    test_bag_expansion_system()
