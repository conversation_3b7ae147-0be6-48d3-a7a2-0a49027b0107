"""
Test the Gemstone Context System
"""

from simulator.gemstone_system import (
    GemstoneManager, Gemstone, GemType, GemRarity, GemContext,
    GemstoneEffectCalculator
)

def test_gemstone_system():
    print("💎 TESTING GEMSTONE CONTEXT SYSTEM")
    print("=" * 60)
    
    manager = GemstoneManager()
    calculator = GemstoneEffectCalculator()
    
    # Test gem creation and rarity progression
    print("\n🔧 TESTING GEM CREATION AND PROGRESSION:")
    print("-" * 40)
    
    # Create gems of different rarities
    chipped_ruby = Gemstone(GemType.RUBY, GemRarity.CHIPPED)
    flawed_ruby = Gemstone(GemType.RUBY, GemRarity.FLAWED)
    regular_sapphire = Gemstone(GemType.SAPPHIRE, GemRarity.REGULAR)
    perfect_emerald = Gemstone(GemType.EMERALD, GemRarity.PERFECT)
    
    print(f"  Created gems:")
    print(f"    {chipped_ruby.gem_rarity.value} {chipped_ruby.gem_type.value} (Tier {chipped_ruby.get_tier_value()})")
    print(f"    {flawed_ruby.gem_rarity.value} {flawed_ruby.gem_type.value} (Tier {flawed_ruby.get_tier_value()})")
    print(f"    {regular_sapphire.gem_rarity.value} {regular_sapphire.gem_type.value} (Tier {regular_sapphire.get_tier_value()})")
    print(f"    {perfect_emerald.gem_rarity.value} {perfect_emerald.gem_type.value} (Tier {perfect_emerald.get_tier_value()})")
    
    # Test context-dependent effects
    print("\n💫 TESTING CONTEXT-DEPENDENT EFFECTS:")
    print("-" * 40)
    
    test_gem = Gemstone(GemType.RUBY, GemRarity.REGULAR)
    
    for context in GemContext:
        effect = calculator.get_gem_effect(test_gem.gem_type, test_gem.gem_rarity, context)
        if effect:
            print(f"  {context.value}: {effect.description}")
            print(f"    Effect: {effect.effect_type} = {effect.magnitude}")
    
    # Test rarity scaling
    print("\n📈 TESTING RARITY SCALING:")
    print("-" * 40)
    
    print("  Ruby in Weapon (Lifesteal):")
    for rarity in GemRarity:
        test_gem = Gemstone(GemType.RUBY, rarity)
        effect = calculator.get_gem_effect(test_gem.gem_type, rarity, GemContext.WEAPON)
        if effect:
            print(f"    {rarity.value}: {effect.magnitude} lifesteal")
    
    # Test gem combination
    print("\n🔄 TESTING GEM COMBINATION:")
    print("-" * 40)
    
    # Add gems to manager
    gem1 = Gemstone(GemType.TOPAZ, GemRarity.CHIPPED)
    gem2 = Gemstone(GemType.TOPAZ, GemRarity.CHIPPED)
    gem3 = Gemstone(GemType.SAPPHIRE, GemRarity.FLAWED)
    
    manager.add_gem(gem1)
    manager.add_gem(gem2)
    manager.add_gem(gem3)
    
    print(f"  Before combination: {len(manager.gems)} gems")
    print(f"    {gem1.gem_rarity.value} {gem1.gem_type.value}")
    print(f"    {gem2.gem_rarity.value} {gem2.gem_type.value}")
    print(f"    {gem3.gem_rarity.value} {gem3.gem_type.value}")
    
    # Test combination
    can_combine = gem1.can_combine_with(gem2)
    print(f"  Can combine Topaz gems: {can_combine}")
    
    if can_combine:
        result = manager.combine_gems(gem1, gem2)
        if result:
            print(f"  Combination result: {result.gem_rarity.value} {result.gem_type.value}")
            print(f"  After combination: {len(manager.gems)} gems")
    
    # Test socketing
    print("\n🔌 TESTING GEM SOCKETING:")
    print("-" * 40)
    
    # Create fresh gems for socketing test
    manager = GemstoneManager()
    weapon_ruby = Gemstone(GemType.RUBY, GemRarity.FLAWLESS)
    armor_topaz = Gemstone(GemType.TOPAZ, GemRarity.REGULAR)
    backpack_emerald = Gemstone(GemType.EMERALD, GemRarity.REGULAR)
    
    manager.add_gem(weapon_ruby)
    manager.add_gem(armor_topaz)
    manager.add_gem(backpack_emerald)
    
    # Set item contexts
    manager.set_item_context("Hero Sword", GemContext.WEAPON)
    manager.set_item_context("Leather Armor", GemContext.ARMOR)
    
    # Socket gems
    manager.socket_gem(weapon_ruby, "Hero Sword")
    manager.socket_gem(armor_topaz, "Leather Armor")
    # backpack_emerald stays in backpack
    
    print(f"  Socketed gems:")
    print(f"    Hero Sword: {len(manager.get_socketed_gems('Hero Sword'))} gems")
    print(f"    Leather Armor: {len(manager.get_socketed_gems('Leather Armor'))} gems")
    print(f"    Backpack: {len(manager.get_backpack_gems())} gems")
    
    # Test total effects
    print("\n⚡ TESTING TOTAL EFFECTS:")
    print("-" * 40)
    
    total_effects = manager.get_total_effects()
    print(f"  Total effects from all gems:")
    for effect_type, magnitude in total_effects.items():
        print(f"    {effect_type}: {magnitude}")
    
    # Test item-specific effects
    print("\n🗡️ TESTING ITEM-SPECIFIC EFFECTS:")
    print("-" * 40)
    
    sword_effects = manager.get_gem_effects_for_item("Hero Sword")
    armor_effects = manager.get_gem_effects_for_item("Leather Armor")
    
    print(f"  Hero Sword effects:")
    for effect in sword_effects:
        print(f"    {effect.description}: {effect.magnitude}")
    
    print(f"  Leather Armor effects:")
    for effect in armor_effects:
        print(f"    {effect.description}: {effect.magnitude}")
    
    # Test all gem types
    print("\n🌈 TESTING ALL GEM TYPES:")
    print("-" * 40)
    
    for gem_type in GemType:
        print(f"  {gem_type.value}:")
        for context in GemContext:
            effect = calculator.get_gem_effect(gem_type, GemRarity.REGULAR, context)
            if effect:
                print(f"    {context.value}: {effect.effect_type}")
    
    # Test combinable gem detection
    print("\n🔍 TESTING COMBINABLE GEM DETECTION:")
    print("-" * 40)
    
    # Add more gems for combination testing
    manager.add_gem(Gemstone(GemType.RUBY, GemRarity.CHIPPED))
    manager.add_gem(Gemstone(GemType.RUBY, GemRarity.CHIPPED))
    manager.add_gem(Gemstone(GemType.SAPPHIRE, GemRarity.FLAWED))
    manager.add_gem(Gemstone(GemType.SAPPHIRE, GemRarity.FLAWED))
    
    combinable_pairs = manager.find_combinable_gems()
    print(f"  Found {len(combinable_pairs)} combinable pairs:")
    for gem1, gem2 in combinable_pairs:
        print(f"    {gem1.gem_rarity.value} {gem1.gem_type.value} + {gem2.gem_rarity.value} {gem2.gem_type.value}")
    
    print("\n" + "=" * 60)
    print("✅ GEMSTONE SYSTEM TEST COMPLETE")

if __name__ == "__main__":
    test_gemstone_system()
