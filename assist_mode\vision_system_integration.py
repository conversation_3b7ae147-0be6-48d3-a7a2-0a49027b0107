"""
Vision System Integration for Real-time Assist Mode

Integrates existing vision components for real-time game state capture and item recognition.
Provides unified interface for capturing and analyzing game screens to extract:
- Current backpack state and item positions
- Shop items and their properties
- Player stats (health, gold, round, etc.)
- Battle state and opponent information
"""

from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
import numpy as np
import cv2
from pathlib import Path
import time
import threading
import queue

from simulator.enhanced_item_database import EnhancedItemDatabase, EnhancedItem


@dataclass
class VisionConfig:
    """Configuration for vision system"""
    # Screen capture settings
    capture_region: Optional[Tuple[int, int, int, int]] = None  # (x, y, width, height)
    capture_fps: int = 10
    
    # Item recognition settings
    item_confidence_threshold: float = 0.8
    grid_detection_threshold: float = 0.7
    
    # Template matching settings
    template_match_threshold: float = 0.8
    scale_range: Tuple[float, float] = (0.8, 1.2)
    
    # Reference image paths
    reference_screenshots_path: str = "data/screenshots/bbss"
    item_scale_reference_path: str = "data/item_scale_reference"
    
    # Processing settings
    enable_preprocessing: bool = True
    debug_mode: bool = False


@dataclass
class DetectedItem:
    """Represents an item detected in the game screen"""
    item_name: str
    confidence: float
    bounding_box: Tuple[int, int, int, int]  # (x, y, width, height)
    grid_position: Optional[Tuple[int, int]] = None
    rotation: int = 0
    properties: Optional[Dict[str, Any]] = None


@dataclass
class GameStateCapture:
    """Captured game state from vision system"""
    timestamp: float
    backpack_items: List[DetectedItem]
    shop_items: List[Optional[DetectedItem]]
    player_stats: Dict[str, Any]
    game_phase: str
    confidence_score: float
    
    def to_simulation_state(self) -> Dict[str, Any]:
        """Convert to simulation-compatible state format"""
        return {
            "timestamp": self.timestamp,
            "phase": self.game_phase,
            "backpack_items": [
                {
                    "name": item.item_name,
                    "position": item.grid_position or (0, 0),
                    "rotation": item.rotation,
                    "confidence": item.confidence
                }
                for item in self.backpack_items
            ],
            "shop_items": [
                {
                    "name": item.item_name,
                    "confidence": item.confidence,
                    "properties": item.properties or {}
                } if item else None
                for item in self.shop_items
            ],
            "player_stats": self.player_stats,
            "confidence_score": self.confidence_score
        }


class ItemRecognitionEngine:
    """Handles item recognition from game screenshots"""
    
    def __init__(self, config: VisionConfig):
        self.config = config
        self.item_database = EnhancedItemDatabase()
        self.item_templates = {}
        self.grid_template = None
        self._load_templates()
    
    def _load_templates(self):
        """Load item templates and grid references"""
        reference_path = Path(self.config.item_scale_reference_path)
        
        if reference_path.exists():
            # Load item templates from reference images
            for item_file in reference_path.glob("*.png"):
                item_name = item_file.stem
                template = cv2.imread(str(item_file), cv2.IMREAD_COLOR)
                if template is not None:
                    self.item_templates[item_name] = template
            
            print(f"Loaded {len(self.item_templates)} item templates")
        
        # Load grid template
        grid_path = Path(self.config.reference_screenshots_path) / "inventory_grid.png"
        if grid_path.exists():
            self.grid_template = cv2.imread(str(grid_path), cv2.IMREAD_GRAYSCALE)
    
    def detect_items_in_region(self, image: np.ndarray, region: Tuple[int, int, int, int]) -> List[DetectedItem]:
        """Detect items in a specific region of the image"""
        x, y, w, h = region
        roi = image[y:y+h, x:x+w]
        
        detected_items = []
        
        for item_name, template in self.item_templates.items():
            matches = self._template_match_multiscale(roi, template)
            
            for match in matches:
                if match["confidence"] >= self.config.item_confidence_threshold:
                    # Adjust coordinates to full image
                    bbox = (
                        match["x"] + x,
                        match["y"] + y,
                        match["width"],
                        match["height"]
                    )
                    
                    detected_item = DetectedItem(
                        item_name=item_name,
                        confidence=match["confidence"],
                        bounding_box=bbox
                    )
                    
                    detected_items.append(detected_item)
        
        return detected_items
    
    def _template_match_multiscale(self, image: np.ndarray, template: np.ndarray) -> List[Dict[str, Any]]:
        """Perform template matching at multiple scales"""
        matches = []
        
        # Convert to grayscale for matching
        if len(image.shape) == 3:
            image_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            image_gray = image
        
        if len(template.shape) == 3:
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        else:
            template_gray = template
        
        # Try different scales
        scale_min, scale_max = self.config.scale_range
        scales = np.linspace(scale_min, scale_max, 5)
        
        for scale in scales:
            # Resize template
            new_width = int(template_gray.shape[1] * scale)
            new_height = int(template_gray.shape[0] * scale)
            
            if new_width <= 0 or new_height <= 0:
                continue
            
            scaled_template = cv2.resize(template_gray, (new_width, new_height))
            
            # Skip if template is larger than image
            if scaled_template.shape[0] > image_gray.shape[0] or scaled_template.shape[1] > image_gray.shape[1]:
                continue
            
            # Perform template matching
            result = cv2.matchTemplate(image_gray, scaled_template, cv2.TM_CCOEFF_NORMED)
            
            # Find matches above threshold
            locations = np.where(result >= self.config.template_match_threshold)
            
            for pt in zip(*locations[::-1]):
                matches.append({
                    "x": pt[0],
                    "y": pt[1],
                    "width": new_width,
                    "height": new_height,
                    "confidence": result[pt[1], pt[0]],
                    "scale": scale
                })
        
        # Remove overlapping matches (non-maximum suppression)
        matches = self._non_maximum_suppression(matches)
        
        return matches
    
    def _non_maximum_suppression(self, matches: List[Dict[str, Any]], overlap_threshold: float = 0.3) -> List[Dict[str, Any]]:
        """Remove overlapping detections"""
        if not matches:
            return matches
        
        # Sort by confidence
        matches = sorted(matches, key=lambda x: x["confidence"], reverse=True)
        
        keep = []
        
        for i, match in enumerate(matches):
            overlap = False
            
            for kept_match in keep:
                # Calculate intersection over union
                iou = self._calculate_iou(match, kept_match)
                if iou > overlap_threshold:
                    overlap = True
                    break
            
            if not overlap:
                keep.append(match)
        
        return keep
    
    def _calculate_iou(self, box1: Dict[str, Any], box2: Dict[str, Any]) -> float:
        """Calculate Intersection over Union of two bounding boxes"""
        x1, y1, w1, h1 = box1["x"], box1["y"], box1["width"], box1["height"]
        x2, y2, w2, h2 = box2["x"], box2["y"], box2["width"], box2["height"]
        
        # Calculate intersection
        xi1 = max(x1, x2)
        yi1 = max(y1, y2)
        xi2 = min(x1 + w1, x2 + w2)
        yi2 = min(y1 + h1, y2 + h2)
        
        if xi2 <= xi1 or yi2 <= yi1:
            return 0.0
        
        intersection = (xi2 - xi1) * (yi2 - yi1)
        
        # Calculate union
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0


class GridDetector:
    """Detects and analyzes the inventory grid"""
    
    def __init__(self, config: VisionConfig):
        self.config = config
        self.grid_size = (9, 7)  # Standard grid size
    
    def detect_grid(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Detect the inventory grid region"""
        # Convert to grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # Use edge detection to find grid lines
        edges = cv2.Canny(gray, 50, 150)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Look for rectangular contours that could be the grid
        for contour in contours:
            # Approximate contour
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # Check if it's roughly rectangular
            if len(approx) == 4:
                x, y, w, h = cv2.boundingRect(approx)
                
                # Check aspect ratio (should be roughly 9:7)
                aspect_ratio = w / h
                expected_ratio = self.grid_size[0] / self.grid_size[1]
                
                if 0.8 * expected_ratio <= aspect_ratio <= 1.2 * expected_ratio:
                    # Check size (should be reasonable)
                    if w > 200 and h > 150:  # Minimum size
                        return (x, y, w, h)
        
        return None
    
    def map_items_to_grid(self, items: List[DetectedItem], grid_region: Tuple[int, int, int, int]) -> List[DetectedItem]:
        """Map detected items to grid positions"""
        x, y, w, h = grid_region
        cell_width = w / self.grid_size[0]
        cell_height = h / self.grid_size[1]
        
        for item in items:
            item_x, item_y, item_w, item_h = item.bounding_box
            
            # Calculate center of item
            center_x = item_x + item_w // 2
            center_y = item_y + item_h // 2
            
            # Map to grid coordinates
            if x <= center_x <= x + w and y <= center_y <= y + h:
                grid_x = int((center_x - x) / cell_width)
                grid_y = int((center_y - y) / cell_height)
                
                # Clamp to grid bounds
                grid_x = max(0, min(self.grid_size[0] - 1, grid_x))
                grid_y = max(0, min(self.grid_size[1] - 1, grid_y))
                
                item.grid_position = (grid_x, grid_y)
        
        return items


class ScreenCapture:
    """Handles screen capture and preprocessing"""
    
    def __init__(self, config: VisionConfig):
        self.config = config
        self.capture_active = False
        self.capture_thread = None
        self.frame_queue = queue.Queue(maxsize=10)
    
    def start_capture(self):
        """Start continuous screen capture"""
        if self.capture_active:
            return
        
        self.capture_active = True
        self.capture_thread = threading.Thread(target=self._capture_loop)
        self.capture_thread.daemon = True
        self.capture_thread.start()
    
    def stop_capture(self):
        """Stop screen capture"""
        self.capture_active = False
        if self.capture_thread:
            self.capture_thread.join()
    
    def get_latest_frame(self) -> Optional[np.ndarray]:
        """Get the latest captured frame"""
        try:
            return self.frame_queue.get_nowait()
        except queue.Empty:
            return None
    
    def _capture_loop(self):
        """Main capture loop"""
        import mss  # Screen capture library
        
        with mss.mss() as sct:
            while self.capture_active:
                try:
                    # Capture screen
                    if self.config.capture_region:
                        monitor = {
                            "top": self.config.capture_region[1],
                            "left": self.config.capture_region[0],
                            "width": self.config.capture_region[2],
                            "height": self.config.capture_region[3]
                        }
                    else:
                        monitor = sct.monitors[1]  # Primary monitor
                    
                    screenshot = sct.grab(monitor)
                    frame = np.array(screenshot)
                    
                    # Convert BGRA to BGR
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                    
                    # Preprocessing
                    if self.config.enable_preprocessing:
                        frame = self._preprocess_frame(frame)
                    
                    # Add to queue (replace oldest if full)
                    try:
                        self.frame_queue.put_nowait(frame)
                    except queue.Full:
                        try:
                            self.frame_queue.get_nowait()  # Remove oldest
                            self.frame_queue.put_nowait(frame)
                        except queue.Empty:
                            pass
                    
                    # Control capture rate
                    time.sleep(1.0 / self.config.capture_fps)
                    
                except Exception as e:
                    if self.config.debug_mode:
                        print(f"Capture error: {e}")
                    time.sleep(0.1)
    
    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess captured frame"""
        # Enhance contrast
        lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        l = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8)).apply(l)
        enhanced = cv2.merge([l, a, b])
        frame = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        # Reduce noise
        frame = cv2.bilateralFilter(frame, 9, 75, 75)
        
        return frame


class VisionSystemIntegrator:
    """Main vision system that integrates all components"""
    
    def __init__(self, config: VisionConfig):
        self.config = config
        self.item_recognizer = ItemRecognitionEngine(config)
        self.grid_detector = GridDetector(config)
        self.screen_capture = ScreenCapture(config)
        self.last_capture = None
    
    def start_real_time_capture(self):
        """Start real-time screen capture"""
        self.screen_capture.start_capture()
        print("Vision system started - real-time capture active")
    
    def stop_real_time_capture(self):
        """Stop real-time screen capture"""
        self.screen_capture.stop_capture()
        print("Vision system stopped")
    
    def capture_game_state(self, image: Optional[np.ndarray] = None) -> Optional[GameStateCapture]:
        """Capture and analyze current game state"""
        if image is None:
            image = self.screen_capture.get_latest_frame()
        
        if image is None:
            return None
        
        try:
            # Detect inventory grid
            grid_region = self.grid_detector.detect_grid(image)
            if not grid_region:
                if self.config.debug_mode:
                    print("Could not detect inventory grid")
                return None
            
            # Detect items in backpack
            backpack_items = self.item_recognizer.detect_items_in_region(image, grid_region)
            backpack_items = self.grid_detector.map_items_to_grid(backpack_items, grid_region)
            
            # Detect shop items (would need shop region detection)
            shop_items = self._detect_shop_items(image)
            
            # Extract player stats (would need UI element detection)
            player_stats = self._extract_player_stats(image)
            
            # Determine game phase
            game_phase = self._determine_game_phase(image)
            
            # Calculate overall confidence
            confidence_score = self._calculate_confidence(backpack_items, shop_items)
            
            capture = GameStateCapture(
                timestamp=time.time(),
                backpack_items=backpack_items,
                shop_items=shop_items,
                player_stats=player_stats,
                game_phase=game_phase,
                confidence_score=confidence_score
            )
            
            self.last_capture = capture
            return capture
            
        except Exception as e:
            if self.config.debug_mode:
                print(f"Error capturing game state: {e}")
            return None
    
    def _detect_shop_items(self, image: np.ndarray) -> List[Optional[DetectedItem]]:
        """Detect items in shop (placeholder implementation)"""
        # Would implement shop region detection and item recognition
        return [None] * 5  # 5 shop slots
    
    def _extract_player_stats(self, image: np.ndarray) -> Dict[str, Any]:
        """Extract player statistics from UI (placeholder implementation)"""
        # Would implement OCR for health, gold, round, etc.
        return {
            "health": 100,
            "gold": 50,
            "round": 3,
            "lives": 3
        }
    
    def _determine_game_phase(self, image: np.ndarray) -> str:
        """Determine current game phase (placeholder implementation)"""
        # Would analyze UI elements to determine phase
        return "shop"
    
    def _calculate_confidence(self, backpack_items: List[DetectedItem], 
                            shop_items: List[Optional[DetectedItem]]) -> float:
        """Calculate overall confidence score"""
        if not backpack_items:
            return 0.5  # Neutral confidence for empty backpack
        
        total_confidence = sum(item.confidence for item in backpack_items)
        return total_confidence / len(backpack_items)
    
    def get_debug_image(self, image: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        """Get debug image with detection overlays"""
        if image is None:
            image = self.screen_capture.get_latest_frame()
        
        if image is None or not self.last_capture:
            return image
        
        debug_image = image.copy()
        
        # Draw detected items
        for item in self.last_capture.backpack_items:
            x, y, w, h = item.bounding_box
            cv2.rectangle(debug_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            cv2.putText(debug_image, f"{item.item_name} ({item.confidence:.2f})", 
                       (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        return debug_image
