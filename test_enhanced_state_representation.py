"""
Test the Enhanced State Representation
"""

import numpy as np
from rl.enhanced_state_representation import EnhancedStateEncoder, StateFeatureType

def create_comprehensive_game_state():
    """Create a comprehensive test game state"""
    return {
        "phase": "shop",
        "round": 5,
        "health": 85,
        "gold": 42,
        "lives": 2,
        "trophies": 1250,
        "character_class": "berserker",
        "subclass": "Fighter",
        "starting_bag": "Duffle Bag",
        
        # Resources
        "stamina": 80,
        "max_stamina": 100,
        "mana": 15,
        "max_mana": 25,
        "status_effects": {
            "Empower": 3,
            "Luck": 2,
            "Poison": 1
        },
        
        # Backpack items
        "backpack_items": [
            {
                "name": "Hero Sword",
                "type": "weapon",
                "rarity": "epic",
                "class": "neutral",
                "position": (1, 1),
                "rotation": 0,
                "shape": [[True, True], [True, True]],
                "tags": ["weapon", "melee", "sword"],
                "power_level": 3.2,
                "cost": 25,
                "sockets": 1,
                "socketed_gems": 1,
                "combination_locked": False
            },
            {
                "name": "Leather Armor",
                "type": "armor",
                "rarity": "rare",
                "class": "neutral",
                "position": (4, 2),
                "rotation": 0,
                "shape": [[True, True], [True, True]],
                "tags": ["armor", "defense"],
                "power_level": 2.1,
                "cost": 15,
                "sockets": 0,
                "socketed_gems": 0,
                "combination_locked": True
            },
            {
                "name": "Health Potion",
                "type": "potion",
                "rarity": "common",
                "class": "neutral",
                "position": (0, 0),
                "rotation": 0,
                "shape": [[True]],
                "tags": ["potion", "healing"],
                "power_level": 1.0,
                "cost": 5,
                "sockets": 0,
                "socketed_gems": 0,
                "combination_locked": False
            },
            {
                "name": "Axe",
                "type": "weapon",
                "rarity": "rare",
                "class": "berserker",
                "position": (6, 0),
                "rotation": 1,
                "shape": [[True, True], [True, False]],
                "tags": ["weapon", "melee", "axe"],
                "power_level": 2.8,
                "cost": 20,
                "sockets": 0,
                "socketed_gems": 0,
                "combination_locked": False
            }
        ],
        
        # Inventory
        "inventory_items": [
            {
                "name": "Whetstone",
                "type": "accessory",
                "rarity": "common",
                "tags": ["upgrade", "weapon"],
                "power_level": 1.2,
                "cost": 8
            }
        ],
        
        # Shop
        "shop_items": [
            {
                "name": "Double Axe",
                "type": "weapon",
                "rarity": "epic",
                "cost": 30,
                "power_level": 4.0,
                "locked": False,
                "reserved": False,
                "tags": ["weapon", "melee", "axe"]
            },
            {
                "name": "Mana Potion",
                "type": "potion",
                "rarity": "common",
                "cost": 8,
                "power_level": 1.1,
                "locked": True,
                "reserved": False,
                "tags": ["potion", "mana"]
            },
            None,  # Empty slot
            {
                "name": "Vampiric Armor",
                "type": "armor",
                "rarity": "legendary",
                "cost": 45,
                "power_level": 4.5,
                "locked": False,
                "reserved": True,
                "tags": ["armor", "vampirism"]
            },
            {
                "name": "Lucky Clover",
                "type": "accessory",
                "rarity": "rare",
                "cost": 12,
                "power_level": 2.0,
                "locked": False,
                "reserved": False,
                "tags": ["luck", "nature"]
            }
        ],
        
        # Occupied cells
        "occupied_cells": {(0, 0), (1, 1), (2, 1), (1, 2), (2, 2), (4, 2), (5, 2), (4, 3), (5, 3), (6, 0), (6, 1)},
        
        # Shop state
        "rerolls_used": 3,
        "reserves_used": 1,
        "gold_spent": 108,
        
        # Battle state
        "in_battle": False,
        "battle_time": 0
    }

def test_enhanced_state_representation():
    print("🧠 TESTING ENHANCED STATE REPRESENTATION")
    print("=" * 60)
    
    encoder = EnhancedStateEncoder()
    
    # Test feature definitions
    print("\n📋 TESTING FEATURE DEFINITIONS:")
    print("-" * 30)
    
    print(f"  Defined {len(encoder.features)} features:")
    for name, feature in encoder.features.items():
        print(f"    {name}: {feature.feature_type.value} {feature.shape}")
        if feature.normalization:
            print(f"      Normalization: {feature.normalization}")
    
    # Test mappings
    print("\n🗺️ TESTING MAPPINGS:")
    print("-" * 30)
    
    print(f"  Item types: {len(encoder.item_type_mapping)} types")
    print(f"    Sample: {list(encoder.item_type_mapping.items())[:5]}")
    
    print(f"  Rarities: {len(encoder.rarity_mapping)} rarities")
    print(f"    Sample: {list(encoder.rarity_mapping.items())}")
    
    print(f"  Classes: {len(encoder.class_mapping)} classes")
    print(f"    Sample: {list(encoder.class_mapping.items())}")
    
    # Test state encoding
    print("\n🔢 TESTING STATE ENCODING:")
    print("-" * 30)
    
    game_state = create_comprehensive_game_state()
    encoded_state = encoder.encode_state(game_state)
    
    print(f"  Encoded {len(encoded_state)} feature arrays:")
    for name, array in encoded_state.items():
        print(f"    {name}: shape {array.shape}, dtype {array.dtype}")
        print(f"      Range: [{array.min():.3f}, {array.max():.3f}]")
        if array.size <= 20:  # Show small arrays
            print(f"      Values: {array.flatten()}")
        print()
    
    # Test grid encodings
    print("\n🔲 TESTING GRID ENCODINGS:")
    print("-" * 30)
    
    item_grid = encoded_state["item_grid"]
    rarity_grid = encoded_state["rarity_grid"]
    synergy_grid = encoded_state["synergy_grid"]
    adjacency_grid = encoded_state["adjacency_grid"]
    
    print("  Item Grid (first 5 rows):")
    for i in range(min(5, item_grid.shape[0])):
        row_str = " ".join(f"{int(x):2d}" for x in item_grid[i])
        print(f"    Row {i}: {row_str}")
    
    print("\n  Rarity Grid (first 5 rows):")
    for i in range(min(5, rarity_grid.shape[0])):
        row_str = " ".join(f"{int(x):2d}" for x in rarity_grid[i])
        print(f"    Row {i}: {row_str}")
    
    print(f"\n  Synergy Grid stats:")
    print(f"    Non-zero positions: {np.count_nonzero(synergy_grid)}")
    print(f"    Max synergy: {synergy_grid.max():.3f}")
    print(f"    Mean synergy: {synergy_grid.mean():.3f}")
    
    print(f"\n  Adjacency Grid stats:")
    print(f"    Max adjacency: {adjacency_grid.max()}")
    print(f"    Mean adjacency: {adjacency_grid.mean():.3f}")
    
    # Test numerical features
    print("\n📊 TESTING NUMERICAL FEATURES:")
    print("-" * 30)
    
    player_stats = encoded_state["player_stats"]
    build_metrics = encoded_state["build_metrics"]
    resource_state = encoded_state["resource_state"]
    
    print("  Player Stats:")
    stat_names = ["health", "gold", "lives", "round", "trophies", 
                  "inventory", "backpack", "rerolls", "reserves", "shop_phase"]
    for i, (name, value) in enumerate(zip(stat_names, player_stats)):
        print(f"    {name}: {value:.3f}")
    
    print("\n  Build Metrics (first 10):")
    metric_names = ["item_count", "type_diversity", "avg_rarity", "synergies", "power_level",
                   "adjacency", "class_synergy", "cost_efficiency", "combination_potential", "defensive_ratio"]
    for i, (name, value) in enumerate(zip(metric_names, build_metrics[:10])):
        print(f"    {name}: {value:.3f}")
    
    print("\n  Resource State:")
    resource_names = ["stamina", "max_stamina", "mana", "max_mana", 
                     "positive_effects", "negative_effects", "in_battle", "battle_time"]
    for i, (name, value) in enumerate(zip(resource_names, resource_state)):
        print(f"    {name}: {value:.3f}")
    
    # Test shop encoding
    print("\n🛒 TESTING SHOP ENCODING:")
    print("-" * 30)
    
    shop_items = encoded_state["shop_items"]
    
    print("  Shop Items (5 slots, 8 features each):")
    feature_names = ["type", "rarity", "cost", "power", "locked", "reserved", "synergy", "affordable"]
    
    for i in range(shop_items.shape[0]):
        print(f"    Slot {i}:")
        for j, (name, value) in enumerate(zip(feature_names, shop_items[i])):
            print(f"      {name}: {value:.3f}")
        print()
    
    # Test categorical features
    print("\n🏷️ TESTING CATEGORICAL FEATURES:")
    print("-" * 30)
    
    game_phase = encoded_state["game_phase"]
    character_info = encoded_state["character_info"]
    
    print(f"  Game Phase: {game_phase}")
    phase_names = ["shop", "battle", "combination", "end"]
    active_phase = phase_names[np.argmax(game_phase)]
    print(f"    Active phase: {active_phase}")
    
    print(f"\n  Character Info: {character_info}")
    print(f"    Class one-hot: {character_info[:4]}")
    print(f"    Has subclass: {character_info[4]}")
    print(f"    Has starting bag: {character_info[5]}")
    print(f"    Class synergy: {character_info[6]:.3f}")
    print(f"    Round 8+: {character_info[7]}")
    
    # Test flattened state
    print("\n📏 TESTING FLATTENED STATE:")
    print("-" * 30)
    
    flattened_state = encoder.get_flattened_state(game_state)
    state_size = encoder.get_state_size()
    
    print(f"  Flattened state shape: {flattened_state.shape}")
    print(f"  Calculated state size: {state_size}")
    print(f"  State range: [{flattened_state.min():.3f}, {flattened_state.max():.3f}]")
    print(f"  Non-zero elements: {np.count_nonzero(flattened_state)}")
    print(f"  Mean value: {flattened_state.mean():.3f}")
    print(f"  Std deviation: {flattened_state.std():.3f}")
    
    # Test with different game states
    print("\n🔄 TESTING DIFFERENT GAME STATES:")
    print("-" * 30)
    
    # Empty backpack state
    empty_state = {
        "phase": "shop",
        "round": 1,
        "health": 100,
        "gold": 10,
        "lives": 3,
        "character_class": "ranger",
        "backpack_items": [],
        "inventory_items": [],
        "shop_items": [None] * 5,
        "occupied_cells": set()
    }
    
    empty_encoded = encoder.encode_state(empty_state)
    empty_flattened = encoder.get_flattened_state(empty_state)
    
    print(f"  Empty state:")
    print(f"    Flattened size: {empty_flattened.shape}")
    print(f"    Non-zero elements: {np.count_nonzero(empty_flattened)}")
    print(f"    Build metrics sum: {empty_encoded['build_metrics'].sum():.3f}")
    
    # Battle phase state
    battle_state = game_state.copy()
    battle_state["phase"] = "battle"
    battle_state["in_battle"] = True
    battle_state["battle_time"] = 25.5
    
    battle_encoded = encoder.encode_state(battle_state)
    
    print(f"\n  Battle state:")
    print(f"    Game phase: {battle_encoded['game_phase']}")
    print(f"    Resource state: {battle_encoded['resource_state'][-2:]}")  # Last 2 elements
    
    # Test synergy calculations
    print("\n🔗 TESTING SYNERGY CALCULATIONS:")
    print("-" * 30)
    
    # Test position synergy
    synergy_score = encoder._calculate_position_synergy(2, 1, game_state["backpack_items"])
    print(f"  Position (2,1) synergy: {synergy_score:.3f}")
    
    # Test total synergies
    total_synergies = encoder._count_total_synergies(game_state["backpack_items"])
    print(f"  Total synergies in build: {total_synergies}")
    
    # Test class synergy
    class_synergy = encoder._calculate_class_synergy(game_state["backpack_items"], "berserker")
    print(f"  Berserker class synergy: {class_synergy:.3f}")
    
    # Test item-build synergy
    test_item = {"tags": ["weapon", "melee", "axe"]}
    item_synergy = encoder._calculate_item_build_synergy(test_item, game_state)
    print(f"  Axe item synergy with build: {item_synergy:.3f}")
    
    print("\n" + "=" * 60)
    print("✅ ENHANCED STATE REPRESENTATION TEST COMPLETE")

if __name__ == "__main__":
    test_enhanced_state_representation()
