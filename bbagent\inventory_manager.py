import numpy as np
from typing import Op<PERSON>, <PERSON><PERSON>, List

from .item import Item
from .config import BACKPACK_DIMENSIONS

class InventoryManager:
    """
    Manages the state of the backpack and provides methods for placing,
    removing, and validating item positions.
    """
    def __init__(self):
        self.width, self.height = BACKPACK_DIMENSIONS
        self.grid = np.zeros((self.height, self.width), dtype=int)
        # Use a dictionary to store item objects by their instance ID for quick lookup
        self.placed_items = {} 

    def _rotate_layout(self, layout: List[List[int]], angle: int) -> List[List[int]]:
        """
        Rotates the item's grid_layout matrix by a given angle (must be a multiple of 90).
        """
        if not layout:
            return []
            
        rotations = angle // 90
        rotated_layout = np.array(layout)
        
        for _ in range(rotations % 4):
            rotated_layout = np.rot90(rotated_layout)
            
        return rotated_layout.tolist()

    def _can_place_at(self, item_layout: List[List[int]], pos: tuple) -> bool:
        """
        Checks if a given item layout can be placed at pos without collision.
        """
        item_height = len(item_layout)
        if item_height == 0:
            return True
        item_width = len(item_layout[0])
        
        x, y = pos
        
        # Check for out-of-bounds placement
        if not (0 <= y < self.height and 0 <= x < self.width and y + item_height <= self.height and x + item_width <= self.width):
            return False
            
        # Check for collisions with existing items
        for i in range(item_height):
            for j in range(item_width):
                if item_layout[i][j] == 1 and self.grid[y + i][x + j] != 0:
                    return False
        return True

    def find_placement_with_backtracking(self, item: Item) -> Optional[tuple]:
        """
        Uses a backtracking algorithm to find a valid placement for the item,
        considering all four rotations.

        Returns the (x, y) coordinates and rotation if a spot is found, else None.
        """
        for angle in [0, 90, 180, 270]:
            rotated_layout = self._rotate_layout(item.grid_layout, angle)
            item_height = len(rotated_layout)
            if item_height == 0: continue
            item_width = len(rotated_layout[0])

            for y in range(self.height - item_height + 1):
                for x in range(self.width - item_width + 1):
                    if self._can_place_at(rotated_layout, (x, y)):
                        item.current_rotation = angle
                        return ((x, y), angle)
        return None
        
    def add_item_to_grid(self, item: Item, pos: tuple):
        """
        Places an item on the grid and stores it.
        Assumes placement is valid.
        """
        x, y = pos
        rotated_layout = self._rotate_layout(item.grid_layout, item.current_rotation)
        
        item_height = len(rotated_layout)
        if item_height == 0: return
        item_width = len(rotated_layout[0])

        for i in range(item_height):
            for j in range(item_width):
                if rotated_layout[i][j] == 1:
                    # Storing item's instance id, assuming it is unique
                    self.grid[y + i][x + j] = id(item)
                    
        item.position = pos
        self.placed_items[id(item)] = item

    def remove_item_from_grid(self, item: Item):
        """
        Removes an item from the grid.
        """
        if id(item) not in self.placed_items:
            return # Item not in inventory

        x, y = item.position
        rotated_layout = self._rotate_layout(item.grid_layout, item.current_rotation)
        
        item_height = len(rotated_layout)
        if item_height == 0: return
        item_width = len(rotated_layout[0])

        for i in range(item_height):
            for j in range(item_width):
                if rotated_layout[i][j] == 1:
                    if self.grid[y + i][x + j] == id(item):
                        self.grid[y + i][x + j] = 0

        del self.placed_items[id(item)]
        item.position = None

    def handle_stash(self, target_item_name: str) -> List:
        """
        Generates a sequence of moves to retrieve an item from the stash.

        This is a placeholder implementation. The final logic will identify
        occluding items and generate moves to a temporary free space.
        """
        print(f"Planning to retrieve {target_item_name} from stash. (Not implemented)")
        return []

    def get_all_items(self) -> List[Item]:
        """Returns a list of all items currently placed in the inventory."""
        return list(self.placed_items.values())

    def clear(self):
        """Clears the inventory grid and all placed items."""
        self.__init__()

    def __str__(self) -> str:
        return str(self.grid)