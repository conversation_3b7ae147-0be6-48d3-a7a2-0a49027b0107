"""
Test the Complete RL Action Space
"""

from rl.complete_action_space import ActionSpace, ActionExecutor, Action, ActionType

def create_test_game_state():
    """Create a test game state for action testing"""
    return {
        "phase": "shop",
        "gold": 50,
        "shop_items": [
            {"name": "Wooden Sword", "cost": 10, "power_level": 1.5, "locked": False},
            {"name": "Health Potion", "cost": 5, "power_level": 1.0, "locked": False},
            None,  # Empty slot
            {"name": "Leather Armor", "cost": 15, "power_level": 2.0, "locked": True},
            {"name": "Whetstone", "cost": 8, "power_level": 1.2, "locked": False}
        ],
        "inventory_items": [
            {"name": "Dagger", "shape": [[True]], "tags": ["weapon", "melee"]},
            {"name": "Flame", "shape": [[True]], "tags": ["fire", "magic"]}
        ],
        "backpack_items": [
            {
                "name": "Hero Sword", 
                "position": (1, 1), 
                "rotation": 0,
                "shape": [[True, True], [True, True]],
                "tags": ["weapon", "melee"],
                "combination_locked": False
            }
        ],
        "occupied_cells": {(1, 1), (2, 1), (1, 2), (2, 2)},
        "reserves_available": 2,
        "reroll_cost": 2,
        "rerolls_used": 0,
        "can_end_turn": True,
        "gems": [
            {"id": "gem1", "type": "Ruby", "rarity": "Chipped", "socketed": False},
            {"id": "gem2", "type": "Ruby", "rarity": "Chipped", "socketed": False},
            {"id": "gem3", "type": "Sapphire", "rarity": "Flawed", "socketed": False}
        ],
        "items_with_sockets": [
            {"name": "Hero Sword", "available_sockets": 1}
        ]
    }

def test_complete_action_space():
    print("🎮 TESTING COMPLETE RL ACTION SPACE")
    print("=" * 60)
    
    action_space = ActionSpace()
    
    # Test action space dimensions
    print("\n📏 TESTING ACTION SPACE DIMENSIONS:")
    print("-" * 30)
    
    print(f"  Grid size: {action_space.grid_width}x{action_space.grid_height}")
    print(f"  Placement actions: {action_space.placement_actions}")
    print(f"  Shop actions: {action_space.shop_actions}")
    print(f"  Management actions: {action_space.management_actions}")
    print(f"  Total action space size: {action_space.total_actions}")
    
    # Test valid action generation
    print("\n🎯 TESTING VALID ACTION GENERATION:")
    print("-" * 30)
    
    game_state = create_test_game_state()
    valid_actions = action_space.get_valid_actions(game_state)
    
    print(f"  Generated {len(valid_actions)} valid actions")
    
    # Count actions by type
    action_counts = {}
    for action in valid_actions:
        action_type = action.action_type.value
        action_counts[action_type] = action_counts.get(action_type, 0) + 1
    
    print("  Action distribution:")
    for action_type, count in action_counts.items():
        print(f"    {action_type}: {count}")
    
    # Test shop actions
    print("\n🛒 TESTING SHOP ACTIONS:")
    print("-" * 30)
    
    shop_actions = [a for a in valid_actions if a.action_type in [
        ActionType.BUY_ITEM, ActionType.LOCK_ITEM, ActionType.RESERVE_ITEM, ActionType.REROLL_SHOP
    ]]
    
    print(f"  Shop actions available: {len(shop_actions)}")
    
    for action in shop_actions[:5]:  # Show first 5
        print(f"    {action.action_type.value}: {action.parameters}")
    
    # Test placement actions
    print("\n📍 TESTING PLACEMENT ACTIONS:")
    print("-" * 30)
    
    placement_actions = [a for a in valid_actions if a.action_type == ActionType.PLACE_ITEM]
    
    print(f"  Placement actions available: {len(placement_actions)}")
    
    # Group by item
    placement_by_item = {}
    for action in placement_actions:
        item_name = action.parameters["item_name"]
        placement_by_item[item_name] = placement_by_item.get(item_name, 0) + 1
    
    print("  Placement options per item:")
    for item_name, count in placement_by_item.items():
        print(f"    {item_name}: {count} positions")
    
    # Test rotation functionality
    print("\n🔄 TESTING ROTATION FUNCTIONALITY:")
    print("-" * 30)
    
    # Test shape rotation
    test_shape = [[True, True, False], [True, False, False]]
    print(f"  Original shape: {test_shape}")
    
    for rotation in range(4):
        rotated = action_space._get_rotated_shape(test_shape, rotation)
        print(f"  Rotation {rotation * 90}°: {rotated}")
    
    # Test action execution
    print("\n⚡ TESTING ACTION EXECUTION:")
    print("-" * 30)
    
    # Mock game manager for executor
    class MockGameManager:
        pass
    
    executor = ActionExecutor(MockGameManager())
    
    # Test buy action
    buy_action = Action(
        action_type=ActionType.BUY_ITEM,
        parameters={"shop_index": 0, "item_name": "Wooden Sword"}
    )
    
    new_state, reward, done = executor.execute_action(buy_action, game_state)
    
    print(f"  Buy action executed:")
    print(f"    Gold before: {game_state['gold']}")
    print(f"    Gold after: {new_state['gold']}")
    print(f"    Reward: {reward}")
    print(f"    Done: {done}")
    print(f"    Inventory items: {len(new_state['inventory_items'])}")
    
    # Test place action
    place_action = Action(
        action_type=ActionType.PLACE_ITEM,
        parameters={"item_name": "Dagger", "x": 0, "y": 0, "rotation": 0}
    )
    
    new_state2, reward2, done2 = executor.execute_action(place_action, new_state)
    
    print(f"\n  Place action executed:")
    print(f"    Inventory items before: {len(new_state['inventory_items'])}")
    print(f"    Inventory items after: {len(new_state2['inventory_items'])}")
    print(f"    Backpack items after: {len(new_state2['backpack_items'])}")
    print(f"    Reward: {reward2}")
    print(f"    Occupied cells: {len(new_state2['occupied_cells'])}")
    
    # Test sell action
    sell_action = Action(
        action_type=ActionType.SELL_ITEM,
        parameters={"item_name": "Hero Sword", "sell_value": 5}
    )
    
    new_state3, reward3, done3 = executor.execute_action(sell_action, new_state2)
    
    print(f"\n  Sell action executed:")
    print(f"    Gold before: {new_state2['gold']}")
    print(f"    Gold after: {new_state3['gold']}")
    print(f"    Backpack items before: {len(new_state2['backpack_items'])}")
    print(f"    Backpack items after: {len(new_state3['backpack_items'])}")
    print(f"    Reward: {reward3}")
    
    # Test gemstone actions
    print("\n💎 TESTING GEMSTONE ACTIONS:")
    print("-" * 30)
    
    gem_actions = [a for a in valid_actions if a.action_type in [
        ActionType.SOCKET_GEM, ActionType.COMBINE_GEMS
    ]]
    
    print(f"  Gemstone actions available: {len(gem_actions)}")
    
    for action in gem_actions:
        print(f"    {action.action_type.value}: {action.parameters}")
    
    # Test strategic actions
    print("\n🧠 TESTING STRATEGIC ACTIONS:")
    print("-" * 30)
    
    strategic_actions = [a for a in valid_actions if a.action_type in [
        ActionType.END_TURN, ActionType.PASS, ActionType.TOGGLE_COMBINATION_LOCK
    ]]
    
    print(f"  Strategic actions available: {len(strategic_actions)}")
    
    for action in strategic_actions:
        print(f"    {action.action_type.value}: {action.parameters}")
    
    # Test end turn action
    end_turn_action = Action(
        action_type=ActionType.END_TURN,
        parameters={}
    )
    
    final_state, final_reward, final_done = executor.execute_action(end_turn_action, new_state3)
    
    print(f"\n  End turn action executed:")
    print(f"    Phase before: {new_state3.get('phase', 'unknown')}")
    print(f"    Phase after: {final_state.get('phase', 'unknown')}")
    print(f"    Final reward: {final_reward}")
    print(f"    Turn ended: {final_done}")
    
    # Test invalid actions
    print("\n❌ TESTING INVALID ACTIONS:")
    print("-" * 30)
    
    # Try to buy item with insufficient gold
    expensive_action = Action(
        action_type=ActionType.BUY_ITEM,
        parameters={"shop_index": 3, "item_name": "Leather Armor"}  # Locked item
    )
    
    invalid_state, invalid_reward, invalid_done = executor.execute_action(expensive_action, game_state)
    
    print(f"  Invalid buy action:")
    print(f"    Reward: {invalid_reward}")
    print(f"    State changed: {invalid_state != game_state}")
    
    # Test placement validation
    print("\n✅ TESTING PLACEMENT VALIDATION:")
    print("-" * 30)
    
    # Test valid placement
    valid_placement = action_space._is_valid_placement(
        {"shape": [[True]]}, 0, 0, 0, game_state
    )
    print(f"  Valid placement at (0,0): {valid_placement}")
    
    # Test invalid placement (occupied)
    invalid_placement = action_space._is_valid_placement(
        {"shape": [[True]]}, 1, 1, 0, game_state
    )
    print(f"  Invalid placement at (1,1) (occupied): {invalid_placement}")
    
    # Test out of bounds placement
    oob_placement = action_space._is_valid_placement(
        {"shape": [[True, True], [True, True]]}, 8, 6, 0, game_state
    )
    print(f"  Out of bounds placement at (8,6): {oob_placement}")
    
    # Test synergy calculation
    print("\n🔗 TESTING SYNERGY CALCULATION:")
    print("-" * 30)
    
    test_item = {"tags": ["weapon", "melee"]}
    synergy_bonus = executor._calculate_placement_synergy(test_item, 0, 1, game_state)
    
    print(f"  Synergy bonus for weapon placement near Hero Sword: {synergy_bonus}")
    
    # Test build quality evaluation
    print("\n🏗️ TESTING BUILD QUALITY EVALUATION:")
    print("-" * 30)
    
    build_quality = executor._evaluate_build_quality(final_state)
    print(f"  Build quality score: {build_quality:.3f}")
    
    print("\n" + "=" * 60)
    print("✅ COMPLETE ACTION SPACE TEST COMPLETE")

if __name__ == "__main__":
    test_complete_action_space()
