import sqlite3

conn = sqlite3.connect('GameData.db')
cursor = conn.cursor()

# Check tables
cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
print('Tables:', cursor.fetchall())

# Check Items schema
cursor.execute('PRAGMA table_info(Items)')
print('\nItems schema:')
for row in cursor.fetchall():
    print(f"  {row[1]} ({row[2]})")

# Sample items
cursor.execute('SELECT Name, Description, RawStats FROM Items LIMIT 3')
print('\nSample items:')
for row in cursor.fetchall():
    print(f"  Name: {row[0]}")
    print(f"  Description: {row[1]}")
    print(f"  RawStats: {row[2]}")
    print()

conn.close()
