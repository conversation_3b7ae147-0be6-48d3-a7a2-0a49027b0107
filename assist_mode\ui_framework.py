"""
Assist Mode UI Framework

Creates user interface framework for displaying recommendations and analysis including:
- Real-time recommendation display
- Build analysis visualization
- Performance monitoring dashboard
- Interactive recommendation controls
- Overlay system for game integration
"""

from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from collections import deque

from assist_mode.real_time_analysis_pipeline import RealtimeAssistant, AnalysisResult, AnalysisConfig
from assist_mode.build_recommendation_engine import Recommendation, BuildAnalysis, RecommendationType, Priority


@dataclass
class UIConfig:
    """Configuration for UI framework"""
    # Window settings
    window_title: str = "Backpack Battles Assist Mode"
    window_width: int = 800
    window_height: int = 600
    always_on_top: bool = True
    
    # Update settings
    ui_update_interval: int = 500  # milliseconds
    max_recommendation_display: int = 5
    
    # Visual settings
    theme: str = "dark"  # "dark" or "light"
    font_family: str = "Segoe UI"
    font_size: int = 10
    
    # Colors
    colors: Dict[str, str] = None
    
    def __post_init__(self):
        if self.colors is None:
            if self.theme == "dark":
                self.colors = {
                    "bg": "#2b2b2b",
                    "fg": "#ffffff",
                    "accent": "#4a9eff",
                    "success": "#4caf50",
                    "warning": "#ff9800",
                    "error": "#f44336",
                    "critical": "#e91e63",
                    "high": "#ff5722",
                    "medium": "#ff9800",
                    "low": "#9e9e9e"
                }
            else:
                self.colors = {
                    "bg": "#ffffff",
                    "fg": "#000000",
                    "accent": "#2196f3",
                    "success": "#4caf50",
                    "warning": "#ff9800",
                    "error": "#f44336",
                    "critical": "#e91e63",
                    "high": "#ff5722",
                    "medium": "#ff9800",
                    "low": "#9e9e9e"
                }


class RecommendationWidget(ttk.Frame):
    """Widget for displaying individual recommendations"""
    
    def __init__(self, parent, ui_config: UIConfig):
        super().__init__(parent)
        self.ui_config = ui_config
        self.current_recommendation: Optional[Recommendation] = None
        
        self._create_widgets()
    
    def _create_widgets(self):
        """Create recommendation display widgets"""
        # Priority indicator
        self.priority_label = ttk.Label(self, text="", width=8)
        self.priority_label.grid(row=0, column=0, padx=5, pady=2, sticky="w")
        
        # Recommendation type
        self.type_label = ttk.Label(self, text="", width=15)
        self.type_label.grid(row=0, column=1, padx=5, pady=2, sticky="w")
        
        # Confidence
        self.confidence_label = ttk.Label(self, text="", width=8)
        self.confidence_label.grid(row=0, column=2, padx=5, pady=2, sticky="w")
        
        # Reasoning (spans multiple columns)
        self.reasoning_label = ttk.Label(self, text="", wraplength=300)
        self.reasoning_label.grid(row=1, column=0, columnspan=3, padx=5, pady=2, sticky="w")
        
        # Action button
        self.action_button = ttk.Button(self, text="Execute", command=self._execute_action)
        self.action_button.grid(row=0, column=3, padx=5, pady=2)
    
    def update_recommendation(self, recommendation: Optional[Recommendation]):
        """Update displayed recommendation"""
        self.current_recommendation = recommendation
        
        if recommendation:
            # Update priority with color
            priority_text = recommendation.priority.value.upper()
            self.priority_label.config(text=priority_text)
            
            # Update type
            type_text = recommendation.recommendation_type.value.replace("_", " ").title()
            self.type_label.config(text=type_text)
            
            # Update confidence
            confidence_text = f"{recommendation.confidence:.1%}"
            self.confidence_label.config(text=confidence_text)
            
            # Update reasoning
            self.reasoning_label.config(text=recommendation.reasoning)
            
            # Enable action button
            self.action_button.config(state="normal")
        else:
            # Clear all fields
            self.priority_label.config(text="")
            self.type_label.config(text="")
            self.confidence_label.config(text="")
            self.reasoning_label.config(text="")
            self.action_button.config(state="disabled")
    
    def _execute_action(self):
        """Execute the recommendation action"""
        if self.current_recommendation:
            # For now, just show the action parameters
            params = self.current_recommendation.action_parameters
            messagebox.showinfo("Action", f"Execute: {params}")


class BuildAnalysisWidget(ttk.Frame):
    """Widget for displaying build analysis"""
    
    def __init__(self, parent, ui_config: UIConfig):
        super().__init__(parent)
        self.ui_config = ui_config
        self.current_analysis: Optional[BuildAnalysis] = None
        
        self._create_widgets()
    
    def _create_widgets(self):
        """Create build analysis widgets"""
        # Title
        title_label = ttk.Label(self, text="Build Analysis", font=(self.ui_config.font_family, 12, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=5)
        
        # Metrics frame
        metrics_frame = ttk.LabelFrame(self, text="Metrics")
        metrics_frame.grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky="ew")
        
        # Metric labels
        self.synergy_label = ttk.Label(metrics_frame, text="Synergy: --")
        self.synergy_label.grid(row=0, column=0, padx=5, pady=2, sticky="w")
        
        self.power_label = ttk.Label(metrics_frame, text="Power: --")
        self.power_label.grid(row=0, column=1, padx=5, pady=2, sticky="w")
        
        self.defense_label = ttk.Label(metrics_frame, text="Defense: --")
        self.defense_label.grid(row=1, column=0, padx=5, pady=2, sticky="w")
        
        self.offense_label = ttk.Label(metrics_frame, text="Offense: --")
        self.offense_label.grid(row=1, column=1, padx=5, pady=2, sticky="w")
        
        self.completion_label = ttk.Label(metrics_frame, text="Completion: --")
        self.completion_label.grid(row=2, column=0, padx=5, pady=2, sticky="w")
        
        self.flexibility_label = ttk.Label(metrics_frame, text="Flexibility: --")
        self.flexibility_label.grid(row=2, column=1, padx=5, pady=2, sticky="w")
        
        # Strengths and weaknesses
        strengths_frame = ttk.LabelFrame(self, text="Strengths")
        strengths_frame.grid(row=2, column=0, padx=5, pady=5, sticky="ew")
        
        self.strengths_text = tk.Text(strengths_frame, height=4, width=30, wrap=tk.WORD)
        self.strengths_text.grid(row=0, column=0, padx=5, pady=5)
        
        weaknesses_frame = ttk.LabelFrame(self, text="Weaknesses")
        weaknesses_frame.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        
        self.weaknesses_text = tk.Text(weaknesses_frame, height=4, width=30, wrap=tk.WORD)
        self.weaknesses_text.grid(row=0, column=0, padx=5, pady=5)
        
        # Recommendation
        rec_frame = ttk.LabelFrame(self, text="Recommended Direction")
        rec_frame.grid(row=3, column=0, columnspan=2, padx=5, pady=5, sticky="ew")
        
        self.direction_label = ttk.Label(rec_frame, text="", wraplength=400)
        self.direction_label.grid(row=0, column=0, padx=5, pady=5)
    
    def update_analysis(self, analysis: Optional[BuildAnalysis]):
        """Update displayed build analysis"""
        self.current_analysis = analysis
        
        if analysis:
            # Update metrics
            self.synergy_label.config(text=f"Synergy: {analysis.synergy_score:.1%}")
            self.power_label.config(text=f"Power: {analysis.power_level:.1%}")
            self.defense_label.config(text=f"Defense: {analysis.defensive_rating:.1%}")
            self.offense_label.config(text=f"Offense: {analysis.offensive_rating:.1%}")
            self.completion_label.config(text=f"Completion: {analysis.completion_percentage:.1%}")
            self.flexibility_label.config(text=f"Flexibility: {analysis.flexibility_score:.1%}")
            
            # Update strengths
            self.strengths_text.delete(1.0, tk.END)
            for strength in analysis.strengths:
                self.strengths_text.insert(tk.END, f"• {strength}\n")
            
            # Update weaknesses
            self.weaknesses_text.delete(1.0, tk.END)
            for weakness in analysis.weaknesses:
                self.weaknesses_text.insert(tk.END, f"• {weakness}\n")
            
            # Update direction
            self.direction_label.config(text=analysis.recommended_direction)
        else:
            # Clear all fields
            self.synergy_label.config(text="Synergy: --")
            self.power_label.config(text="Power: --")
            self.defense_label.config(text="Defense: --")
            self.offense_label.config(text="Offense: --")
            self.completion_label.config(text="Completion: --")
            self.flexibility_label.config(text="Flexibility: --")
            
            self.strengths_text.delete(1.0, tk.END)
            self.weaknesses_text.delete(1.0, tk.END)
            self.direction_label.config(text="")


class PerformanceWidget(ttk.Frame):
    """Widget for displaying performance metrics"""
    
    def __init__(self, parent, ui_config: UIConfig):
        super().__init__(parent)
        self.ui_config = ui_config
        self.performance_history = deque(maxlen=50)
        
        self._create_widgets()
    
    def _create_widgets(self):
        """Create performance monitoring widgets"""
        # Title
        title_label = ttk.Label(self, text="Performance", font=(self.ui_config.font_family, 12, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=5)
        
        # Status indicators
        self.status_label = ttk.Label(self, text="Status: Stopped")
        self.status_label.grid(row=1, column=0, padx=5, pady=2, sticky="w")
        
        self.fps_label = ttk.Label(self, text="Analysis FPS: --")
        self.fps_label.grid(row=1, column=1, padx=5, pady=2, sticky="w")
        
        self.processing_label = ttk.Label(self, text="Processing Time: --")
        self.processing_label.grid(row=2, column=0, padx=5, pady=2, sticky="w")
        
        self.confidence_label = ttk.Label(self, text="Confidence: --")
        self.confidence_label.grid(row=2, column=1, padx=5, pady=2, sticky="w")
        
        self.queue_label = ttk.Label(self, text="Queue: --")
        self.queue_label.grid(row=3, column=0, padx=5, pady=2, sticky="w")
        
        self.success_label = ttk.Label(self, text="Success Rate: --")
        self.success_label.grid(row=3, column=1, padx=5, pady=2, sticky="w")
    
    def update_performance(self, status: Dict[str, Any]):
        """Update performance display"""
        # Update status
        running = status.get("running", False)
        self.status_label.config(text=f"Status: {'Running' if running else 'Stopped'}")
        
        # Update performance metrics
        perf = status.get("performance", {})
        
        fps = perf.get("analyses_per_second", 0)
        self.fps_label.config(text=f"Analysis FPS: {fps:.1f}")
        
        avg_time = perf.get("average_processing_time", 0) * 1000
        self.processing_label.config(text=f"Processing: {avg_time:.1f}ms")
        
        success_rate = perf.get("success_rate", 0)
        self.success_label.config(text=f"Success Rate: {success_rate:.1%}")
        
        # Update queue info
        queue_size = status.get("queue_size", 0)
        max_queue = status.get("max_queue_size", 0)
        self.queue_label.config(text=f"Queue: {queue_size}/{max_queue}")
        
        # Store for history
        self.performance_history.append({
            "timestamp": time.time(),
            "fps": fps,
            "processing_time": avg_time,
            "success_rate": success_rate
        })


class AssistModeUI:
    """Main UI for assist mode"""
    
    def __init__(self, ui_config: Optional[UIConfig] = None, analysis_config: Optional[AnalysisConfig] = None):
        if ui_config is None:
            ui_config = UIConfig()
        if analysis_config is None:
            analysis_config = AnalysisConfig()
        
        self.ui_config = ui_config
        self.analysis_config = analysis_config
        
        # Initialize assistant
        self.assistant = RealtimeAssistant(analysis_config)
        
        # UI state
        self.root = None
        self.running = False
        self.update_thread = None
        
        # Widgets
        self.recommendation_widgets: List[RecommendationWidget] = []
        self.build_analysis_widget: Optional[BuildAnalysisWidget] = None
        self.performance_widget: Optional[PerformanceWidget] = None
        
        self._create_ui()
    
    def _create_ui(self):
        """Create the main UI"""
        self.root = tk.Tk()
        self.root.title(self.ui_config.window_title)
        self.root.geometry(f"{self.ui_config.window_width}x{self.ui_config.window_height}")
        
        if self.ui_config.always_on_top:
            self.root.attributes("-topmost", True)
        
        # Configure style
        style = ttk.Style()
        if self.ui_config.theme == "dark":
            style.theme_use("clam")
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Recommendations tab
        rec_frame = ttk.Frame(notebook)
        notebook.add(rec_frame, text="Recommendations")
        self._create_recommendations_tab(rec_frame)
        
        # Analysis tab
        analysis_frame = ttk.Frame(notebook)
        notebook.add(analysis_frame, text="Build Analysis")
        self._create_analysis_tab(analysis_frame)
        
        # Performance tab
        perf_frame = ttk.Frame(notebook)
        notebook.add(perf_frame, text="Performance")
        self._create_performance_tab(perf_frame)
        
        # Control buttons
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.start_button = ttk.Button(control_frame, text="Start", command=self._start_assistant)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop", command=self._stop_assistant, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.refresh_button = ttk.Button(control_frame, text="Refresh", command=self._force_refresh)
        self.refresh_button.pack(side=tk.LEFT, padx=5)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def _create_recommendations_tab(self, parent):
        """Create recommendations tab"""
        # Header
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(header_frame, text="Priority", width=8).grid(row=0, column=0, padx=5)
        ttk.Label(header_frame, text="Type", width=15).grid(row=0, column=1, padx=5)
        ttk.Label(header_frame, text="Confidence", width=8).grid(row=0, column=2, padx=5)
        ttk.Label(header_frame, text="Action", width=8).grid(row=0, column=3, padx=5)
        
        # Recommendations list
        rec_frame = ttk.Frame(parent)
        rec_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create recommendation widgets
        for i in range(self.ui_config.max_recommendation_display):
            widget = RecommendationWidget(rec_frame, self.ui_config)
            widget.pack(fill=tk.X, pady=2)
            self.recommendation_widgets.append(widget)
    
    def _create_analysis_tab(self, parent):
        """Create build analysis tab"""
        self.build_analysis_widget = BuildAnalysisWidget(parent, self.ui_config)
        self.build_analysis_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _create_performance_tab(self, parent):
        """Create performance monitoring tab"""
        self.performance_widget = PerformanceWidget(parent, self.ui_config)
        self.performance_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _start_assistant(self):
        """Start the assistant"""
        try:
            self.assistant.start()
            self.running = True
            
            # Start UI update thread
            self.update_thread = threading.Thread(target=self._update_loop)
            self.update_thread.daemon = True
            self.update_thread.start()
            
            # Update buttons
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            
            self.status_var.set("Assistant started")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start assistant: {e}")
    
    def _stop_assistant(self):
        """Stop the assistant"""
        try:
            self.assistant.stop()
            self.running = False
            
            # Update buttons
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            
            self.status_var.set("Assistant stopped")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop assistant: {e}")
    
    def _force_refresh(self):
        """Force a refresh of the display"""
        self._update_display()
        self.status_var.set("Display refreshed")
    
    def _update_loop(self):
        """Main UI update loop"""
        while self.running:
            try:
                self._update_display()
                time.sleep(self.ui_config.ui_update_interval / 1000.0)
            except Exception as e:
                print(f"UI update error: {e}")
    
    def _update_display(self):
        """Update all UI elements"""
        try:
            # Update recommendations
            recommendations = self.assistant.get_current_recommendations()
            for i, widget in enumerate(self.recommendation_widgets):
                if i < len(recommendations):
                    widget.update_recommendation(recommendations[i])
                else:
                    widget.update_recommendation(None)
            
            # Update build analysis
            if self.build_analysis_widget:
                analysis = self.assistant.get_current_build_analysis()
                self.build_analysis_widget.update_analysis(analysis)
            
            # Update performance
            if self.performance_widget:
                status = self.assistant.get_status()
                self.performance_widget.update_performance(status)
            
        except Exception as e:
            print(f"Display update error: {e}")
    
    def run(self):
        """Run the UI"""
        try:
            self.root.mainloop()
        finally:
            if self.running:
                self._stop_assistant()


def create_assist_mode_ui(ui_config: Optional[UIConfig] = None, 
                         analysis_config: Optional[AnalysisConfig] = None) -> AssistModeUI:
    """Create and return assist mode UI"""
    return AssistModeUI(ui_config, analysis_config)


def run_assist_mode():
    """Run assist mode with default configuration"""
    ui = create_assist_mode_ui()
    ui.run()


if __name__ == "__main__":
    run_assist_mode()
