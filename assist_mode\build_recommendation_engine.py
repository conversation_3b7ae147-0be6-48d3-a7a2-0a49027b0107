"""
Build Recommendation Engine for Assist Mode

Analyzes current game state and provides intelligent recommendations for:
- Optimal item purchases and placements
- Build synergy optimization
- Strategic decision making
- Resource management
- Timing recommendations
"""

from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import numpy as np
from collections import defaultdict

from simulator.enhanced_item_database import EnhancedItemDatabase, EnhancedItem
from simulator.character_classes import CharacterClass
from rl.enhanced_state_representation import EnhancedStateEncoder
from rl.complete_action_space import ActionSpace, Action, ActionType


class RecommendationType(Enum):
    BUY_ITEM = "buy_item"
    PLACE_ITEM = "place_item"
    SELL_ITEM = "sell_item"
    REROLL_SHOP = "reroll_shop"
    ROTATE_ITEM = "rotate_item"
    LOCK_ITEM = "lock_item"
    END_TURN = "end_turn"
    STRATEGIC_ADVICE = "strategic_advice"


class Priority(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class Recommendation:
    """Single recommendation with reasoning"""
    recommendation_type: RecommendationType
    priority: Priority
    confidence: float
    action_parameters: Dict[str, Any]
    reasoning: str
    expected_benefit: float
    risk_assessment: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for UI display"""
        return {
            "type": self.recommendation_type.value,
            "priority": self.priority.value,
            "confidence": self.confidence,
            "parameters": self.action_parameters,
            "reasoning": self.reasoning,
            "expected_benefit": self.expected_benefit,
            "risk_assessment": self.risk_assessment
        }


@dataclass
class BuildAnalysis:
    """Analysis of current build state"""
    synergy_score: float
    power_level: float
    defensive_rating: float
    offensive_rating: float
    flexibility_score: float
    completion_percentage: float
    weaknesses: List[str]
    strengths: List[str]
    recommended_direction: str


class SynergyAnalyzer:
    """Analyzes synergies and build coherence"""
    
    def __init__(self, item_database: EnhancedItemDatabase):
        self.item_database = item_database
        self.synergy_weights = {
            "weapon": {"weapon": 0.3, "accessory": 0.8},
            "armor": {"armor": 0.4, "helmet": 0.7, "shoes": 0.6},
            "potion": {"potion": 0.5, "food": 0.6},
            "tool": {"weapon": 0.6, "tool": 0.4}
        }
    
    def analyze_synergies(self, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze synergies between items"""
        if not items:
            return {"total_synergies": 0, "synergy_score": 0.0, "synergy_pairs": []}
        
        synergy_pairs = []
        total_synergy_strength = 0.0
        
        for i, item1 in enumerate(items):
            for item2 in items[i+1:]:
                synergy_strength = self._calculate_item_synergy(item1, item2)
                if synergy_strength > 0:
                    synergy_pairs.append({
                        "item1": item1.get("name", "Unknown"),
                        "item2": item2.get("name", "Unknown"),
                        "strength": synergy_strength,
                        "type": self._get_synergy_type(item1, item2)
                    })
                    total_synergy_strength += synergy_strength
        
        synergy_score = min(1.0, total_synergy_strength / max(len(items), 1))
        
        return {
            "total_synergies": len(synergy_pairs),
            "synergy_score": synergy_score,
            "synergy_pairs": synergy_pairs,
            "average_strength": total_synergy_strength / max(len(synergy_pairs), 1)
        }
    
    def _calculate_item_synergy(self, item1: Dict[str, Any], item2: Dict[str, Any]) -> float:
        """Calculate synergy strength between two items"""
        # Tag-based synergy
        tags1 = set(item1.get("tags", []))
        tags2 = set(item2.get("tags", []))
        common_tags = tags1.intersection(tags2)
        
        tag_synergy = len(common_tags) * 0.2
        
        # Type-based synergy
        type1 = item1.get("type", "").lower()
        type2 = item2.get("type", "").lower()
        
        type_synergy = 0.0
        if type1 in self.synergy_weights and type2 in self.synergy_weights[type1]:
            type_synergy = self.synergy_weights[type1][type2]
        elif type2 in self.synergy_weights and type1 in self.synergy_weights[type2]:
            type_synergy = self.synergy_weights[type2][type1]
        
        # Class-based synergy
        class1 = item1.get("class", "neutral").lower()
        class2 = item2.get("class", "neutral").lower()
        class_synergy = 0.3 if class1 == class2 and class1 != "neutral" else 0.0
        
        return min(1.0, tag_synergy + type_synergy + class_synergy)
    
    def _get_synergy_type(self, item1: Dict[str, Any], item2: Dict[str, Any]) -> str:
        """Determine the type of synergy between items"""
        tags1 = set(item1.get("tags", []))
        tags2 = set(item2.get("tags", []))
        common_tags = tags1.intersection(tags2)
        
        if common_tags:
            return f"Tag synergy: {', '.join(common_tags)}"
        
        type1 = item1.get("type", "").lower()
        type2 = item2.get("type", "").lower()
        
        if type1 == type2:
            return f"Type synergy: {type1}"
        
        class1 = item1.get("class", "neutral").lower()
        class2 = item2.get("class", "neutral").lower()
        
        if class1 == class2 and class1 != "neutral":
            return f"Class synergy: {class1}"
        
        return "Weak synergy"


class BuildOptimizer:
    """Optimizes build composition and placement"""
    
    def __init__(self, item_database: EnhancedItemDatabase):
        self.item_database = item_database
        self.synergy_analyzer = SynergyAnalyzer(item_database)
    
    def analyze_build(self, game_state: Dict[str, Any]) -> BuildAnalysis:
        """Analyze current build state"""
        backpack_items = game_state.get("backpack_items", [])
        character_class = game_state.get("character_class", "neutral")
        round_num = game_state.get("round", 1)
        
        # Calculate metrics
        synergy_analysis = self.synergy_analyzer.analyze_synergies(backpack_items)
        synergy_score = synergy_analysis["synergy_score"]
        
        power_level = self._calculate_power_level(backpack_items)
        defensive_rating = self._calculate_defensive_rating(backpack_items)
        offensive_rating = self._calculate_offensive_rating(backpack_items)
        flexibility_score = self._calculate_flexibility(backpack_items)
        completion_percentage = self._calculate_completion(backpack_items, round_num)
        
        weaknesses = self._identify_weaknesses(backpack_items, character_class)
        strengths = self._identify_strengths(backpack_items, character_class)
        recommended_direction = self._recommend_direction(backpack_items, character_class, round_num)
        
        return BuildAnalysis(
            synergy_score=synergy_score,
            power_level=power_level,
            defensive_rating=defensive_rating,
            offensive_rating=offensive_rating,
            flexibility_score=flexibility_score,
            completion_percentage=completion_percentage,
            weaknesses=weaknesses,
            strengths=strengths,
            recommended_direction=recommended_direction
        )
    
    def _calculate_power_level(self, items: List[Dict[str, Any]]) -> float:
        """Calculate overall power level of build"""
        if not items:
            return 0.0
        
        total_power = sum(item.get("power_level", 1.0) for item in items)
        return min(1.0, total_power / (len(items) * 3.0))  # Normalize to max power 3.0
    
    def _calculate_defensive_rating(self, items: List[Dict[str, Any]]) -> float:
        """Calculate defensive capability"""
        defensive_items = [item for item in items 
                          if item.get("type", "").lower() in ["armor", "helmet", "shoes", "shield"]]
        
        if not defensive_items:
            return 0.0
        
        defensive_power = sum(item.get("power_level", 1.0) for item in defensive_items)
        return min(1.0, defensive_power / 10.0)  # Normalize
    
    def _calculate_offensive_rating(self, items: List[Dict[str, Any]]) -> float:
        """Calculate offensive capability"""
        offensive_items = [item for item in items 
                          if item.get("type", "").lower() in ["weapon", "tool"]]
        
        if not offensive_items:
            return 0.0
        
        offensive_power = sum(item.get("power_level", 1.0) for item in offensive_items)
        return min(1.0, offensive_power / 10.0)  # Normalize
    
    def _calculate_flexibility(self, items: List[Dict[str, Any]]) -> float:
        """Calculate build flexibility"""
        types_present = set(item.get("type", "").lower() for item in items)
        return len(types_present) / 8.0  # Normalize by number of item types
    
    def _calculate_completion(self, items: List[Dict[str, Any]], round_num: int) -> float:
        """Calculate build completion percentage"""
        expected_items = min(round_num * 2, 15)  # Rough estimate
        return len(items) / max(expected_items, 1)
    
    def _identify_weaknesses(self, items: List[Dict[str, Any]], character_class: str) -> List[str]:
        """Identify build weaknesses"""
        weaknesses = []
        
        # Check for missing defensive items
        defensive_items = [item for item in items 
                          if item.get("type", "").lower() in ["armor", "helmet", "shoes"]]
        if len(defensive_items) < 2:
            weaknesses.append("Insufficient defensive items")
        
        # Check for missing offensive items
        offensive_items = [item for item in items 
                          if item.get("type", "").lower() in ["weapon", "tool"]]
        if len(offensive_items) < 1:
            weaknesses.append("No offensive items")
        
        # Check for class synergy
        class_items = [item for item in items 
                      if item.get("class", "neutral").lower() == character_class.lower()]
        if len(class_items) < len(items) * 0.3:
            weaknesses.append(f"Poor {character_class} class synergy")
        
        # Check for low synergy overall
        synergy_analysis = self.synergy_analyzer.analyze_synergies(items)
        if synergy_analysis["synergy_score"] < 0.3:
            weaknesses.append("Low item synergy")
        
        return weaknesses
    
    def _identify_strengths(self, items: List[Dict[str, Any]], character_class: str) -> List[str]:
        """Identify build strengths"""
        strengths = []
        
        # Check synergy strength
        synergy_analysis = self.synergy_analyzer.analyze_synergies(items)
        if synergy_analysis["synergy_score"] > 0.7:
            strengths.append("Strong item synergies")
        
        # Check power level
        power_level = self._calculate_power_level(items)
        if power_level > 0.7:
            strengths.append("High power level")
        
        # Check balance
        defensive_rating = self._calculate_defensive_rating(items)
        offensive_rating = self._calculate_offensive_rating(items)
        if abs(defensive_rating - offensive_rating) < 0.2:
            strengths.append("Well-balanced build")
        
        # Check class focus
        class_items = [item for item in items 
                      if item.get("class", "neutral").lower() == character_class.lower()]
        if len(class_items) > len(items) * 0.6:
            strengths.append(f"Strong {character_class} focus")
        
        return strengths
    
    def _recommend_direction(self, items: List[Dict[str, Any]], character_class: str, round_num: int) -> str:
        """Recommend build direction"""
        defensive_rating = self._calculate_defensive_rating(items)
        offensive_rating = self._calculate_offensive_rating(items)
        
        if round_num <= 3:
            return "Focus on economy and basic items"
        elif round_num <= 6:
            if defensive_rating < 0.3:
                return "Prioritize defensive items"
            elif offensive_rating < 0.3:
                return "Add more offensive power"
            else:
                return "Build synergies and optimize placement"
        else:
            return "Finalize build and prepare for endgame"


class RecommendationEngine:
    """Main recommendation engine"""
    
    def __init__(self):
        self.item_database = EnhancedItemDatabase()
        self.build_optimizer = BuildOptimizer(self.item_database)
        self.state_encoder = EnhancedStateEncoder()
        self.action_space = ActionSpace()
    
    def generate_recommendations(self, game_state: Dict[str, Any], 
                               max_recommendations: int = 5) -> List[Recommendation]:
        """Generate prioritized recommendations"""
        recommendations = []
        
        # Analyze current build
        build_analysis = self.build_optimizer.analyze_build(game_state)
        
        # Generate different types of recommendations
        recommendations.extend(self._recommend_shop_actions(game_state, build_analysis))
        recommendations.extend(self._recommend_placement_actions(game_state, build_analysis))
        recommendations.extend(self._recommend_management_actions(game_state, build_analysis))
        recommendations.extend(self._recommend_strategic_actions(game_state, build_analysis))
        
        # Sort by priority and confidence
        recommendations.sort(key=lambda r: (
            {"critical": 4, "high": 3, "medium": 2, "low": 1}[r.priority.value],
            r.confidence
        ), reverse=True)
        
        return recommendations[:max_recommendations]
    
    def _recommend_shop_actions(self, game_state: Dict[str, Any], 
                               build_analysis: BuildAnalysis) -> List[Recommendation]:
        """Recommend shop-related actions"""
        recommendations = []
        
        if game_state.get("phase") != "shop":
            return recommendations
        
        shop_items = game_state.get("shop_items", [])
        player_gold = game_state.get("gold", 0)
        
        for i, item in enumerate(shop_items):
            if not item:
                continue
            
            item_cost = item.get("cost", 0)
            if player_gold < item_cost:
                continue
            
            # Evaluate item fit
            fit_score = self._evaluate_item_fit(item, game_state, build_analysis)
            
            if fit_score > 0.6:
                priority = Priority.HIGH if fit_score > 0.8 else Priority.MEDIUM
                
                recommendations.append(Recommendation(
                    recommendation_type=RecommendationType.BUY_ITEM,
                    priority=priority,
                    confidence=fit_score,
                    action_parameters={"shop_index": i, "item_name": item.get("name")},
                    reasoning=f"Item fits build well (score: {fit_score:.2f})",
                    expected_benefit=fit_score * 0.5,
                    risk_assessment="Low risk" if item_cost < player_gold * 0.3 else "Medium risk"
                ))
        
        # Recommend reroll if shop quality is poor
        shop_quality = self._evaluate_shop_quality(shop_items, game_state, build_analysis)
        if shop_quality < 0.3 and player_gold >= 2:
            recommendations.append(Recommendation(
                recommendation_type=RecommendationType.REROLL_SHOP,
                priority=Priority.MEDIUM,
                confidence=0.7,
                action_parameters={"cost": 2},
                reasoning="Current shop has poor items for your build",
                expected_benefit=0.3,
                risk_assessment="Low risk"
            ))
        
        return recommendations
    
    def _recommend_placement_actions(self, game_state: Dict[str, Any], 
                                   build_analysis: BuildAnalysis) -> List[Recommendation]:
        """Recommend item placement actions"""
        recommendations = []
        
        inventory_items = game_state.get("inventory_items", [])
        
        for item in inventory_items:
            # Find optimal placement
            optimal_placement = self._find_optimal_placement(item, game_state)
            
            if optimal_placement:
                x, y, rotation = optimal_placement
                
                recommendations.append(Recommendation(
                    recommendation_type=RecommendationType.PLACE_ITEM,
                    priority=Priority.HIGH,
                    confidence=0.8,
                    action_parameters={
                        "item_name": item.get("name"),
                        "x": x,
                        "y": y,
                        "rotation": rotation
                    },
                    reasoning=f"Optimal placement for synergy at ({x}, {y})",
                    expected_benefit=0.4,
                    risk_assessment="No risk"
                ))
        
        return recommendations
    
    def _recommend_management_actions(self, game_state: Dict[str, Any], 
                                    build_analysis: BuildAnalysis) -> List[Recommendation]:
        """Recommend item management actions"""
        recommendations = []
        
        backpack_items = game_state.get("backpack_items", [])
        
        # Recommend selling weak items if inventory is full
        if len(backpack_items) > 15:  # Near capacity
            weakest_item = min(backpack_items, key=lambda x: x.get("power_level", 1.0))
            
            recommendations.append(Recommendation(
                recommendation_type=RecommendationType.SELL_ITEM,
                priority=Priority.MEDIUM,
                confidence=0.6,
                action_parameters={"item_name": weakest_item.get("name")},
                reasoning="Free up space by selling weakest item",
                expected_benefit=0.2,
                risk_assessment="Low risk"
            ))
        
        return recommendations
    
    def _recommend_strategic_actions(self, game_state: Dict[str, Any], 
                                   build_analysis: BuildAnalysis) -> List[Recommendation]:
        """Recommend strategic actions"""
        recommendations = []
        
        # End turn recommendation
        if game_state.get("phase") == "shop":
            turn_readiness = self._evaluate_turn_readiness(game_state, build_analysis)
            
            if turn_readiness > 0.7:
                recommendations.append(Recommendation(
                    recommendation_type=RecommendationType.END_TURN,
                    priority=Priority.MEDIUM,
                    confidence=turn_readiness,
                    action_parameters={},
                    reasoning="Build is ready for battle",
                    expected_benefit=0.3,
                    risk_assessment="No risk"
                ))
        
        # Strategic advice
        if build_analysis.weaknesses:
            recommendations.append(Recommendation(
                recommendation_type=RecommendationType.STRATEGIC_ADVICE,
                priority=Priority.LOW,
                confidence=0.8,
                action_parameters={"advice": build_analysis.recommended_direction},
                reasoning=f"Address weaknesses: {', '.join(build_analysis.weaknesses)}",
                expected_benefit=0.5,
                risk_assessment="No risk"
            ))
        
        return recommendations
    
    def _evaluate_item_fit(self, item: Dict[str, Any], game_state: Dict[str, Any], 
                          build_analysis: BuildAnalysis) -> float:
        """Evaluate how well an item fits the current build"""
        backpack_items = game_state.get("backpack_items", [])
        character_class = game_state.get("character_class", "neutral")
        
        fit_score = 0.0
        
        # Class synergy
        if item.get("class", "neutral").lower() == character_class.lower():
            fit_score += 0.3
        
        # Type balance
        item_type = item.get("type", "").lower()
        type_counts = defaultdict(int)
        for bp_item in backpack_items:
            type_counts[bp_item.get("type", "").lower()] += 1
        
        if item_type in ["armor", "helmet", "shoes"] and build_analysis.defensive_rating < 0.5:
            fit_score += 0.4
        elif item_type in ["weapon", "tool"] and build_analysis.offensive_rating < 0.5:
            fit_score += 0.4
        
        # Synergy potential
        for bp_item in backpack_items:
            synergy = self.build_optimizer.synergy_analyzer._calculate_item_synergy(item, bp_item)
            fit_score += synergy * 0.1
        
        # Power level consideration
        power_level = item.get("power_level", 1.0)
        if power_level > 2.0:
            fit_score += 0.2
        
        return min(1.0, fit_score)
    
    def _evaluate_shop_quality(self, shop_items: List[Any], game_state: Dict[str, Any], 
                              build_analysis: BuildAnalysis) -> float:
        """Evaluate overall shop quality"""
        if not shop_items:
            return 0.0
        
        total_fit = 0.0
        valid_items = 0
        
        for item in shop_items:
            if item:
                fit_score = self._evaluate_item_fit(item, game_state, build_analysis)
                total_fit += fit_score
                valid_items += 1
        
        return total_fit / max(valid_items, 1)
    
    def _find_optimal_placement(self, item: Dict[str, Any], 
                               game_state: Dict[str, Any]) -> Optional[Tuple[int, int, int]]:
        """Find optimal placement for an item"""
        occupied_cells = game_state.get("occupied_cells", set())
        backpack_items = game_state.get("backpack_items", [])
        
        best_score = -1
        best_placement = None
        
        # Try different positions and rotations
        for x in range(9):
            for y in range(7):
                for rotation in range(4):
                    if self._is_valid_placement(item, x, y, rotation, occupied_cells):
                        score = self._score_placement(item, x, y, backpack_items)
                        if score > best_score:
                            best_score = score
                            best_placement = (x, y, rotation)
        
        return best_placement
    
    def _is_valid_placement(self, item: Dict[str, Any], x: int, y: int, 
                           rotation: int, occupied_cells: set) -> bool:
        """Check if placement is valid"""
        # Simplified placement check
        shape = item.get("shape", [[True]])
        
        # Check bounds and collisions
        for dy, row in enumerate(shape):
            for dx, cell in enumerate(row):
                if cell:
                    cell_x, cell_y = x + dx, y + dy
                    if (cell_x >= 9 or cell_y >= 7 or 
                        (cell_x, cell_y) in occupied_cells):
                        return False
        
        return True
    
    def _score_placement(self, item: Dict[str, Any], x: int, y: int, 
                        backpack_items: List[Dict[str, Any]]) -> float:
        """Score a placement position"""
        score = 0.0
        
        # Prefer positions near synergistic items
        for bp_item in backpack_items:
            bp_pos = bp_item.get("position", (0, 0))
            distance = abs(bp_pos[0] - x) + abs(bp_pos[1] - y)
            
            if distance == 1:  # Adjacent
                synergy = self.build_optimizer.synergy_analyzer._calculate_item_synergy(item, bp_item)
                score += synergy * 0.5
        
        # Prefer corner/edge positions for stability
        if x == 0 or x == 8 or y == 0 or y == 6:
            score += 0.1
        
        return score
    
    def _evaluate_turn_readiness(self, game_state: Dict[str, Any], 
                                build_analysis: BuildAnalysis) -> float:
        """Evaluate readiness to end turn"""
        readiness = 0.0
        
        # Check if we have items to place
        inventory_items = game_state.get("inventory_items", [])
        if not inventory_items:
            readiness += 0.4
        
        # Check gold efficiency
        gold = game_state.get("gold", 0)
        if gold < 5:  # Low gold, might as well end turn
            readiness += 0.3
        
        # Check build completeness
        readiness += build_analysis.completion_percentage * 0.3
        
        return min(1.0, readiness)
